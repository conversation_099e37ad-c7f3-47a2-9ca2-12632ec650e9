<?xml version="1.0" encoding="utf-8"?>
<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.nearme.note.setting.AboutHeaderPreference
        android:layout="@layout/layout_about_header"
        android:selectable="false" />

    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="pref_open_source_licence"
            android:persistent="false"
            android:title="@string/setting_open_source_license" />
        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="pref_big_model_info"
            android:persistent="false"
            app:isPreferenceVisible="false"
            android:title="@string/setting_big_model_info" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>

</androidx.preference.PreferenceScreen>