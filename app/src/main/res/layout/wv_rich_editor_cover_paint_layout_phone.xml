<?xml version="1.0" encoding="utf-8"?>
<com.nearme.note.activity.richedit.webview.WVRichEditorCoverPaint xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rich_editor"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <View
        android:id="@+id/backcloth"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:forceDarkAllowed="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/default_background"
        android:layout_width="360dp"
        android:layout_height="match_parent"
        android:forceDarkAllowed="false"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.oplus.notes.webviewcoverpaint.container.web.BounceLayout
        android:id="@+id/bounce_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:forceDarkAllowed="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ViewStub
            android:id="@+id/paint_script_stub"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout="@layout/view_layout_paint_view" />
    </com.oplus.notes.webviewcoverpaint.container.web.BounceLayout>

    <com.oplus.note.view.WVScrollbarView
        android:id="@+id/wvScrollbarView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:wbScrollbars="vertical" />

    <include
        android:id="@+id/toolbar_layout_container"
        layout="@layout/toolbar_layout_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:forceDarkAllowed="true"
        app:layout_constraintBottom_toBottomOf="parent"/>


    <ViewStub
        android:id="@+id/rich_text_tool_panel_view_stub"
        android:layout_width="@dimen/rich_text_tool_panel_width"
        android:layout_height="wrap_content"
        android:layout="@layout/rich_text_tool_panel_layout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <ViewStub
        android:id="@+id/table_tool_panel_view_stub"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout="@layout/table_tool_panel_layout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>
    <ViewStub
        android:id="@+id/table_color_panel_view_stub"
        android:layout_width="@dimen/rich_text_tool_panel_width"
        android:layout_height="wrap_content"
        android:layout="@layout/table_color_panel_layout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>
    <ViewStub
        android:id="@+id/toolbar_layout_extra_panel_stub"
        android:layout_width="@dimen/rich_text_tool_panel_width"
        android:layout_height="wrap_content"
        android:layout="@layout/toolbar_extra_panel"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <include
        android:id="@+id/aigc_toolbar_container"
        layout="@layout/aigc_toolbar_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:forceDarkAllowed="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent" />

</com.nearme.note.activity.richedit.webview.WVRichEditorCoverPaint>