<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/couiColorBackgroundWithCard">

    <com.coui.appcompat.toolbar.COUIToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:minHeight="@dimen/toolbar_height" />


    <TextView
        android:id="@+id/model_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintEnd_toEndOf="parent"
        android:includeFontPadding="false"
        android:text="@string/model_name"
        android:textSize="@dimen/sp_14"
        android:textColor="?attr/couiColorLabelSecondary"
        android:layout_marginTop="@dimen/dimen_12"
        android:layout_marginHorizontal="@dimen/dimen_24"
        android:lineHeight="@dimen/dp24"
        android:gravity="center_vertical">

    </TextView>

    <TextView
        android:id="@+id/record_number"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/model_name"
        app:layout_constraintEnd_toEndOf="parent"
        android:includeFontPadding="false"
        android:text="@string/record_number"
        android:textSize="@dimen/sp_14"
        android:layout_marginTop="@dimen/dimen_8"
        android:textColor="?attr/couiColorLabelSecondary"
        android:layout_marginHorizontal="@dimen/dimen_24"
        android:lineHeight="@dimen/dp24"
        android:gravity="center_vertical">

    </TextView>


</androidx.constraintlayout.widget.ConstraintLayout>