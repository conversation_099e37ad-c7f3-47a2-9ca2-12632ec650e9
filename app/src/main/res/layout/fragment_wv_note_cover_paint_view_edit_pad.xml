<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/note_edit_content"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/bottom_cloth"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:forceDarkAllowed="false"/>

    <LinearLayout
        android:id="@+id/skin_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:forceDarkAllowed="false">

        <ImageView
            android:id="@+id/skin_top_extra_bg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter"
            android:visibility="gone" />

        <com.nearme.note.skin.SpotlightView
            android:id="@+id/skin_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rich_linearlayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/rl_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:forceDarkAllowed="false"
            android:gravity="bottom"
            android:paddingTop="@dimen/toolbar_margin_top"
            app:layout_constraintTop_toTopOf="parent">

            <com.coui.appcompat.toolbar.COUIToolbar
                android:id="@+id/tool_bar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toStartOf="@id/edit_complete"
                android:background="@android:color/transparent"
                android:minHeight="@dimen/toolbar_min_hight">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:orientation="horizontal">

                    <View
                        android:id="@+id/blank_view"
                        android:layout_width="@dimen/toolbar_normal_blank_width"
                        android:layout_height="1dp" />

                    <ImageView
                        android:id="@+id/menu_undo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/coui_toolbar_menu_bg"
                        android:contentDescription="@string/note_undo"
                        android:paddingStart="@dimen/dp_4"
                        android:paddingEnd="@dimen/dp_4"
                        android:src="@drawable/ic_paint_undo"
                        android:visibility="invisible" />

                    <View
                        android:layout_width="@dimen/undo_redo_diff_width"
                        android:layout_height="1dp" />

                    <ImageView
                        android:id="@+id/menu_redo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/coui_toolbar_menu_bg"
                        android:contentDescription="@string/note_redo"
                        android:paddingStart="@dimen/dp_4"
                        android:paddingEnd="@dimen/dp_4"
                        android:src="@drawable/ic_paint_redo"
                        android:visibility="invisible" />

                </LinearLayout>
            </com.coui.appcompat.toolbar.COUIToolbar>

            <ImageView
                android:id="@+id/edit_complete"
                android:layout_width="@dimen/edit_complete_width"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@id/tool_bar"
                android:layout_alignParentEnd="true"
                android:layout_marginRight="@dimen/edit_complete_margin_right"
                android:background="@drawable/note_toolbar_menu_bg"
                android:contentDescription="@string/edit_complete"
                android:minHeight="@dimen/toolbar_min_hight"
                android:scaleType="center"
                android:src="@drawable/menu_ic_edit_complete" />
        </RelativeLayout>


        <com.nearme.note.activity.edit.NoteTimeLinearLayout
            android:id="@+id/note_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/rl_toolbar"
            tools:layout_height="32dp" />

        <View
            android:id="@+id/ancher"
            android:layout_width="1dp"
            android:layout_height="1dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/note_time" />

        <com.nearme.note.activity.richedit.ContentFrameLayout
            android:id="@+id/content_layout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/note_time">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ViewStub
                    android:id="@+id/view_stub_error_des"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp_24"
                    android:layout_marginTop="@dimen/dp_16"
                    android:layout_marginBottom="@dimen/dp_16"
                    android:layout="@layout/fragment_note_view_edit_error_des" />

                <include
                    android:id="@+id/richEditor"
                    layout="@layout/wv_rich_editor_cover_paint_layout_pad"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
            </LinearLayout>

            <ViewStub
                android:id="@+id/mask_screen"
                android:layout_width="match_parent"
                android:visibility="gone"
                android:layout_height="match_parent"
                android:layout="@layout/fragment_note_view_edit_note_detail_mask"/>

        </com.nearme.note.activity.richedit.ContentFrameLayout>

        <ViewStub
            android:id="@+id/play_pop_controller_view"
            android:layout="@layout/voice_control_pop_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="@id/note_time" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <ViewStub
        android:id="@+id/guide_cycle_stylus_click_stub"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_228"
        android:layout_gravity="top|center_horizontal"
        android:layout="@layout/stylus_click_guide" />

    <ViewStub
        android:id="@+id/search_container"
        android:layout="@layout/search_container_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ViewStub
        android:id="@+id/fake_current_screen"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/fragment_note_view_edit_fake_current_screen"/>

</FrameLayout>