<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/frame_Layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="MissingClass">

    <com.oplus.richtext.editor.view.RichEditText xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:fontFamily="sans-serif-regular"
        android:freezesText="false"
        android:gravity="top|start"
        android:includeFontPadding="false"
        android:inputType="textCapSentences|textMultiLine"
        android:maxLength="@integer/max_text"
        android:paddingStart="@dimen/common_margin"
        android:paddingEnd="@dimen/common_margin"
        android:paddingBottom="@dimen/dp_12"
        android:singleLine="false"
        android:textAlignment="viewStart"
        android:textSize="16sp" />
</FrameLayout>
