/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  CollectPrivacyDao.java
 * * Description: CollectPrivacyDao.java
 * * Version: 1.0
 * * Date : 2024/9/4
 * * Author: gan<PERSON>ali
 * *
 * * OPLUS Coding Static Checking Skip
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * gan<PERSON>ali  2024/9/4      1.0    build this module
 ****************************************************************/

package com.nearme.note.db.daos;

import static androidx.room.OnConflictStrategy.REPLACE;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.nearme.note.db.entities.CollectPrivacy;


import java.util.List;


@Dao
public abstract class CollectPrivacyDao extends BaseDao {

    private static final String TAG = "CollectPrivacyDao";

    /***
     * 存入收集数据
     */
    @Insert(onConflict = REPLACE)
    public abstract void insertItem(CollectPrivacy collectPrivacy);

    /***
     * 查询最近几天收集的内容
     */
    @Query("select * from collect_privacy  where type =:typeState and  datetime(create_time/1000,'unixepoch','localtime','start of day') between  datetime('now', '-' || :day || ' days','localtime','start of day') and datetime('now', '-1 days','localtime','start of day')")
    public abstract List<CollectPrivacy> getCollectionContents(String typeState, int day);

    /***
     * 查询今天收集的数据类型是否存在
     */
    @Query("select * from collect_privacy  where type =:typeState and  datetime(create_time/1000,'unixepoch','localtime','start of day') = datetime('now', 'localtime','start of day')")
    public abstract List<CollectPrivacy> getCollectionTodayContents(String typeState);

    /***
     * 查询今天收集的是数据是否有重复
     */
    @Query("select * from collect_privacy  where type =:typeState and content =:content and  datetime(create_time/1000,'unixepoch','localtime','start of day') = datetime('now', 'localtime','start of day')")
    public abstract boolean getCollectionTodayContentsEqual(String typeState, String content);

    @Update
    public abstract int updateItem( CollectPrivacy item);
    /***
     * 清除
     */
    @Query("DELETE FROM collect_privacy")
    public abstract int deleteAll();

}
