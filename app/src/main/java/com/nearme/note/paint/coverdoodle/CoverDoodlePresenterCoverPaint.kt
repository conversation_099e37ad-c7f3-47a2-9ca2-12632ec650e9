package com.nearme.note.paint.coverdoodle

import android.annotation.SuppressLint
import android.app.Activity
import android.content.res.Configuration
import android.graphics.Matrix
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import androidx.annotation.RequiresApi
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.heytap.tbl.webkit.WebView
import com.nearme.note.MyApplication
import com.nearme.note.logic.ThumbFileManager
import com.nearme.note.paint.ToolKitHelper
import com.nearme.note.paint.popup.PopupWindowReturn
import com.nearme.note.paint.popup.PopupWindowZoom
import com.nearme.note.skin.api.SkinManager
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.DarkModeUtil
import com.nearme.note.view.helper.UiHelper
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import com.oplus.note.osdk.proxy.OplusVibrateProxy
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.utils.toast
import com.oplus.notes.webviewcoverpaint.container.api.IWebViewContainerCoverpaint
import com.oplus.notes.webviewcoverpaint.container.web.BounceWebView
import com.oplus.richtext.editor.view.CoverPaintView
import com.oplusos.vfxsdk.doodleengine.Paint
import com.oplusos.vfxsdk.doodleengine.PaintView
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

@SuppressLint("WrongConstant")
class CoverDoodlePresenterCoverPaint(
    private val mPaintView: CoverPaintView?,
    private val mRichRecyclerView: RecyclerView?,
    private val mBackground: View? = null,
    private var mWebView: WebView? = null,
    private var webViewContainer: IWebViewContainerCoverpaint? = null,
    private val mainScope: CoroutineScope? = null
) {
    companion object {
        const val TAG = "CoverDoodlePresenter"
        const val MAX_FILE_SIZE = 41943040L//40MB（40*1024*1024） 单位byte
        const val DELAY_TIME_MILLS_100 = 100L
        const val DEFAULT_PAINT_SIZE_LEVEL = 3
        var isFisrtSetDefaultPaintColor = true
        var currentIsDarkMode: Boolean = DarkModeUtil.isDarkMode(MyApplication.appContext)
    }

    private var onScrollStartListener: (() -> Unit)? = null
    private var onScrollEndListener: (() -> Unit)? = null
    private var onInitializedListener: (() -> Unit)? = null
    private var onScrollScaleListener: ((scroll: Float) -> Unit)? = null
    private var onPaintSavedListener: ((fromParent: Boolean, syncImmediately: Boolean, isAddWidget: Boolean, isNeedJumpTwopane: Boolean) -> Unit)? = null
    private var onLoadedListener: (() -> Unit)? = null
    private var onSavedListener: (() -> Unit)? = null
    private var onInitialized: (() -> Unit)? = null
    private var onSetScaleListener: ((scroll: Float) -> Unit)? = null
    private var onUndidListener: (() -> Unit)? = null
    private var onRedoListener: (() -> Unit)? = null
    private var onAddNodeListener: (() -> Unit)? = null
    private var onLeapedListener: ((scroll: Float) -> Unit)? = null
    private var onSaveShareListListener: ((paths: ArrayList<String>?) -> Unit)? = null
    private var syncImmediately: Boolean = true
    private var fromParent: Boolean = false
    private var isAddWidget: Boolean = false
    private var isNeedJumpTwopane: Boolean = false
    //是否为单应用分屏的触发保存
    var isSplitScreen: Boolean = false

    val itemHeightArray = mutableListOf<Int>()
    var isStop = false
    //该标志位用于在屏幕中间息屏的场景
    var isNeedRestartCorrect = false

    private val mHandler = Handler(Looper.getMainLooper())
    private val mDisMissDialogTask = DismissDialogTask()

    var isTwopane = false

    var isImeAnimating = false
    var isRollStart = false
    var isDeleting = false
    var isSearch = false
    var isChangedByNodeUnRedo = false
    var doodleChanged = false
    var isSaveWithNode = true
    var jumpTwopaneWithDoodle = false
    var isSpeechFromViewMode = false
    var isBackFromShare = false

    val undoState: MutableLiveData<Boolean> by lazy {
        MutableLiveData(false)
    }
    val redoState: MutableLiveData<Boolean> by lazy {
        MutableLiveData(false)
    }

    var initialized = false
    var scaleSize = 1.0F
    var scrollScaling = false
    var skinOffsetChangeListener:  ((offset: Float) -> Unit)? = null
    // 能否增加空行，在图片分享时禁用，默认需要支持分享
    var enableAddLine = true

    fun setWebView(
        mRichRecyclerView: WebView?,
        webViewContainer: IWebViewContainerCoverpaint?
    ) {
        mWebView = mRichRecyclerView
        this.webViewContainer = webViewContainer
    }

    fun setOnScrollScaleListener(listener: (Float) -> Unit) {
        onScrollScaleListener = listener
    }

    fun setOnLeapedListener(listener: (Float) -> Unit) {
        onLeapedListener = listener
    }

    fun setOnScrollStartlListener(listener: () -> Unit) {
        onScrollStartListener = listener
    }

    fun setOnScrollEndlListener(listener: () -> Unit) {
        onScrollEndListener = listener
    }

    fun setOnInitializedListener(listener: () -> Unit){
        onInitializedListener = listener
    }

    fun setOnPaintSavedListener(listener: (Boolean, Boolean, Boolean, Boolean) -> Unit) {
        onPaintSavedListener = listener
    }

    fun setOnAddNodeListener(listener: (() -> Unit)?) {
        onAddNodeListener = listener
    }

    fun setOnRedoListener(listener: () -> Unit) {
        onRedoListener = listener
    }

    fun setOnUndoListener(listener: () -> Unit) {
        onUndidListener = listener
    }

    fun setOnLoadedListener(listener: () -> Unit){
        onLoadedListener = listener
    }

    fun setOnsetScaleListener(listener: (Float?) -> Unit) {
        onSetScaleListener = listener
    }

    fun setOnSaveShareListListener(listener: (ArrayList<String>?) -> Unit){
        onSaveShareListListener = listener
    }

    fun setEnableEmergencySave(imagePath: String?, dataPath: String?) {
        if (dataPath != null) {
            /*这里先注释掉，用他们的缓存会引发一些问题，经过一段时间测试后无问题则删除相关代码
            mPaintView?.enableEmergencySave(imagePath, dataPath)*/
        } else {
            AppLogger.BASIC.e(TAG, "setEnableEmergencySave error: dataPath is null!")
        }
    }

    val saveListener = object : PaintView.SaveListener {
        override fun onDataTruncation() {
            super.onDataTruncation()
        }

        override fun onDrawingsSaved(code: PaintView.FileCode?, paths: ArrayList<String>?) {
            super.onDrawingsSaved(code, paths)
            GlobalScope.launch(Dispatchers.Main) {
                onSaveShareListListener?.invoke(paths)
            }
        }

        override fun onPreviewSaved(code: PaintView.FileCode?, imagePath: String?) {
            super.onPreviewSaved(code, imagePath)
        }

        override fun onSaved() {
            super.onSaved()
            onPaintSavedListener?.invoke(fromParent, syncImmediately, isAddWidget, isNeedJumpTwopane)
        }
    }

    val controller = object : PaintView.PaintViewListener {
        override fun initialized() {
            var color = 0
            if (mPaintView?.context?.getColor(R.color.black) != null) {
                color = mPaintView.context?.getColor(R.color.black)!!
            }
            Log.i(TAG, "initialized color:$color")
            initialized = true
            mPaintView?.setPaint(Paint(Paint.Type.BALLPEN, DEFAULT_PAINT_SIZE_LEVEL,
                    color.red.toFloat() ,
                    color.green.toFloat(),
                    color.blue.toFloat(), 1F))
            if (isFisrtSetDefaultPaintColor && (mPaintView?.context != null)) {
                isFisrtSetDefaultPaintColor = false
                ToolKitHelper.setDefaultPaintAttrs(mPaintView.context)
            }

            onInitializedListener?.invoke()
        }

        override fun onLoaded() {
            Log.i(TAG, "onLoaded")
            onLoadedListener?.invoke()
        }

        override fun onAddedNode() {
            Log.i(TAG, "onAddedNote")
            undoState.postValue(mPaintView?.undoStatus)
            redoState.postValue(mPaintView?.redoStatus)
            isChangedByNodeUnRedo = true
            doodleChanged = true
            onAddNodeListener?.invoke()
        }

        override fun onUndid() {
            undoState.postValue(mPaintView?.undoStatus)
            redoState.postValue(mPaintView?.redoStatus)
            isChangedByNodeUnRedo = true
            doodleChanged = true
            onUndidListener?.invoke()
        }

        override fun onRedone() {
            undoState.postValue(mPaintView?.undoStatus)
            redoState.postValue(mPaintView?.redoStatus)
            isChangedByNodeUnRedo = true
            doodleChanged = true
            onRedoListener?.invoke()
        }

        override fun onMenuChanged(type: PaintView.MenuType) {

        }

        override fun onAdsorption() {
            OplusVibrateProxy.vibrateByLinearmotor(mPaintView?.context)
        }

        //PaintChanged  RollingChanged Extended
        override fun onCanvasExtended(code: PaintView.CanvasExtendType, dy: Float) {
            Log.i(TAG, "onCanvasExtend code " + code)
            if (code == PaintView.CanvasExtendType.RollingChanged) {
                return
            }
        }
    }

    val popReturn: PopupWindowReturn by lazy {
        PopupWindowReturn.PopReturnBuilder.init(mPaintView!!).setRetureListener {
            returnOutOfCanvas()
        }.builder()
    }
    var popReturnWindowShowing = false

    fun showReturnButton(){
        popReturn.show()
        popReturnWindowShowing = true
    }

    val popZoom: PopupWindowZoom by lazy {
        PopupWindowZoom.PopZoomBuilder.init(mPaintView!!).builder()
    }
    var popZoomWindowShowing = false

    private val mCurMatrix = Matrix()

    inner class DismissDialogTask() : Runnable {
        override fun run() {
            dismissPopZoom()
        }
    }

    fun dismissPopZoom() {
        if (popZoomWindowShowing) {
            popZoom.dismiss()
            popZoomWindowShowing = false
        }
    }

    fun dismissPopReturn() {
        if (ConfigUtils.isSupportOverlayPaint && popReturnWindowShowing) {
            popReturn.dismiss()
            popReturnWindowShowing = false
        }
    }

    fun saveCoverDoodleToFile(
        noteId: String,
        pictureAttachmentNew: Attachment?,
        paintAttachmentNew: Attachment?,
        fromParent: Boolean = false,
        syncImmediately: Boolean = true,
        isAddWidget: Boolean = true,
        saveWithNode: Boolean = true,
        isNeedJumpTwopane: Boolean = false
    ) {
        //PaintView.isChanged 是用于判断是否需要保存，1说明这个数据是临时数据，是一种中间状态，所以加载临时保存的文件后，调用该接口返回的是true
        //需要在之前笔记退出编辑时，采用standard Save
        AppLogger.BASIC.d(TAG, "syncImmediately:$syncImmediately,saveWithNode:$saveWithNode，mPaintView?.isChanged:${mPaintView?.isChanged}")
        val type = if (syncImmediately == true || saveWithNode == false || mPaintView?.isChanged == false) {
            0
        } else {
            1
        }
        if (type == 1 && mPaintView != null) {
            //临时保存重新设置一下临时保存的路径
            setEnableEmergencySave(
                pictureAttachmentNew?.absolutePath(mPaintView.context),
                paintAttachmentNew?.absolutePath(mPaintView.context)
            )
        }
        this.fromParent = fromParent
        this.syncImmediately = syncImmediately
        this.isAddWidget = isAddWidget
        this.isNeedJumpTwopane = isNeedJumpTwopane
        ThumbFileManager.ensureRichNoteFolderExist(noteId)
        if(paintAttachmentNew != null) {
            val save = mPaintView?.save(
                pictureAttachmentNew?.absolutePath(mPaintView.context),
                paintAttachmentNew.absolutePath(mPaintView.context),
                    type,
                    MAX_FILE_SIZE
            )
            AppLogger.BASIC.d(TAG, "saveCoverDoodleToFile save: $save   type:$type ")
        } else {
            AppLogger.BASIC.e(TAG, "saveCoverDoodleToFile error: paint paintAttachmentNew is null")
        }
    }

    //和上次传type为0的时候是否有新增操作变化
    fun isCoverPaintChanged(): Boolean {
        return mPaintView?.isChanged ?: false
    }

    /**判断是否有涂鸦内容*/
    fun isCoverPaintEmpty(): Boolean {
        return mPaintView?.isStrokeEmpty ?: true
    }

    fun isCoverPaintChangedWithNodeUnRedo(): Boolean {
        //sdk提供的ischanged是因为照上次的不带撤销恢复的状态相比
        //如果进入涂鸦，画几笔右上角保存带撤销的涂鸦，再到浏览模式中撤销到最初进入的样子，则ischanged为false
        //所以增加了绘画和撤销恢复的判断
        return (mPaintView?.isChanged ?: false || isChangedByNodeUnRedo)
    }

    //获取整个lsit的高度
    fun getNoteHeight(): Int {
        var noteHeight = 0
        for (itemHeight in itemHeightArray) {
            noteHeight += itemHeight
        }
        return noteHeight
    }

    //获取这个position和这个position以前的高度
    //比如传入2则是前两个itme的高度
    fun getNoteHeightWightPosition(position: Int):Int{
        if (position >= itemHeightArray.size){
            return -1
        }
        var noteHeight = 0
        for (i in itemHeightArray.indices ) {
            if (i < position) {
                noteHeight += itemHeightArray[i]
            }
        }
        return noteHeight
    }

    //判断是否超出画布内容(有虚线则认为超出)，超出则显示回到画布区域
    fun isOutOfCanvas(): Boolean {
        return (((mPaintView?.displayHeight ?: 0) > (mPaintView?.maxPaintHeight ?: 0)))
    }

    //回到画布区域要滚动的距离
    fun returnOutOfCanvas() {
        val value = (mPaintView?.maxPaintHeight ?: 0) - (mPaintView?.displayHeight ?: 0)
        mRichRecyclerView?.scrollBy(0, (value / mScale * mInitPaintScale).toInt())
        mWebView?.scrollBy(0, (value / mScale * mInitPaintScale).toInt())
        correctingDoodle()
    }

    /***********软键盘弹出计算逻辑start***********/
    var mCurrentItem: View? = null
    fun setImePrepare() {
        Log.i(TAG, "setImePrepare")
        isImeAnimating = true
    }

    fun setImeEnd() {
        Log.i(TAG, "setImeEnd")
        if (isImeAnimating) {
            isImeAnimating = false
        }
        //这里增加了判断是因为之前的scrolling会时刻的correcti，但是后续取消了，所以停下软键盘不能再去判断rollstart了 if (!isRollStart)
        correctingDoodleIme()
    }

    //考虑滚动过程中屏蔽软键盘收起
    fun setImeProgress() {
        Log.i(TAG,"setImeProgress")
        if (initialized == true && isImeAnimating) {
            correctingDoodleIme()
        }
    }
    /***********软键盘弹出计算逻辑end***********/

    //涂鸦向下滚动
    fun rollStart() {
        if (!ConfigUtils.isSupportOverlayPaint) {
            return
        }
        Log.i(TAG, "rollStart")
        isRollStart = true
        mPaintView?.rollStart()
    }

    fun rolling(scroll: Float, extendCanvas: Boolean) {
        if (!ConfigUtils.isSupportOverlayPaint) {
            return
        }
        if (scroll != 0F) {
            Log.i(TAG, "rolling: $scroll, mScale: $mScale, mInitPaintScale: $mInitPaintScale")
            mPaintView?.rolling(scroll/* * mScale*/, extendCanvas)
        }
    }

    //涂鸦向下滚动
    fun rollEnd() {
        if (!ConfigUtils.isSupportOverlayPaint) {
            return
        }
        Log.i(TAG, "rollEnd")
        isRollStart = false
        kotlin.runCatching {
            mPaintView?.rollEnd()
        }
    }

    //两个层级 图文和涂鸦 都滚到顶部
    fun scrollToTop() {
        Log.i(TAG, "scrollToTop")
        scrollToTopDoodle()
        mWebView?.scrollTo(0, 0)
    }

    fun scrollToTopDoodle() {
        if (!ConfigUtils.isSupportOverlayPaint) {
            return
        }
        Log.i(TAG, "scrollToTopDoodle")
        updateContent(mInitPaintX, 0F, mInitPaintScale)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            mCurMatrix.reset()
            mWebView?.animationMatrix = mCurMatrix
            mBackground?.animationMatrix = mCurMatrix
        }
    }

    //判断是否需要保存涂鸦
    fun isNeedSaveDoodle(): Boolean {
        if (isCoverPaintEmpty() == false && isCoverPaintChangedWithNodeUnRedo() == true) {
            return true
        }
        return false
    }

    //校正dietu
    fun correctingDoodleWithOffset(x: Float, y: Float, scale: Float) {
        Log.i(TAG, "correctingDoodle for offset")
        val correct = getCorrectValue(true, false)
        updateContent(x, correct, scale)
    }

    var imeCorrect = 0F
    fun correctingDoodleIme() {
        Log.i(TAG, "correctingDoodleIme")
        val correct = getCorrectValue(false, true)
        if (!isStop) {
            if (correct != imeCorrect) {
                imeCorrect = correct
                updateContent(mInitPaintX, correct, mInitPaintScale)
            }
        } else {
            isNeedRestartCorrect = true
        }
    }

    //叠涂模式下使用这种
    fun correctingDoodleInCover() {
        Log.i(TAG, "correctingDoodleCoverView")
        val correct = getCorrectValue(true, false)
        updateContent(mPaintX, correct, mPaintScale)
    }

    //校正dietu
    fun correctingDoodle() {
        Log.i(TAG, "correctingDoodle isStop=$isStop")
        if (!isStop) {
            if (mPaintView?.getPreviewStatus() == true) {
                Log.i(TAG, "correctingDoodle true")
                updateContent(mInitPaintX, getCorrectValue(true, true), mInitPaintScale)
            } else {
                Log.i(TAG, "correctingDoodle false")
                updateContent(mPaintX, getCorrectValue(true, false), mPaintScale)
            }
        } else {
            isNeedRestartCorrect = true
        }
    }

    //分屏下系统不会回调WindowInsetsAnimationCompat 正常的逻辑是要在progress里面去做
    //correctingDoodleIme 方法内部有去重判断
    fun correctInMulti(delayTime: Long) {
        mPaintView?.postDelayed({
            //期间如果用户进入了涂鸦模式，不去执行
            if ((mPaintView.getPreviewStatus())) {
                correctingDoodleIme()
            }
        }, delayTime)
    }

    //根据当前recyclerview的位置，获取涂鸦应该显示的位置
    private fun getCorrectValue(isNeedRefresh: Boolean, isViewMode: Boolean): Float {
//        if (isNeedRefresh) {
//            updateListHeightRecycler()
//        } else {
//            updateListHeight()
//        }
//        if (itemHeightArray.size == 0) {
//            return 0F
//        }
        val correct = -(mWebView?.scrollY?.toFloat() ?: 0f)
        Log.i(TAG, "CoverDoodlePresenter.getCorrectValue -> $correct")
//        val lastVisiblePosition =
//            mRichRecyclerView?.localLayoutManager?.findLastVisibleItemPosition() ?: 0
//        val lastVisibleItem =
//            mRichRecyclerView?.localLayoutManager?.findViewByPosition(lastVisiblePosition)
//        val lastY = lastVisibleItem?.y ?: 0F
//        correct = lastY
//        for (i in 0 until lastVisiblePosition) {
//            correct -= itemHeightArray[i]
//        }

        return if (isViewMode) {
            correct/* * mScale*/
        } else {
            correct/* * mScale*/ * mPaintScale / mInitPaintScale
        }
    }

    fun updateWebContentHeightWithPaintCanvasHeight() {
        val webView = mWebView
        val webViewContainer = webViewContainer
        if ((webView != null) && (webViewContainer != null)) {
            val webContentHeight = (webView.contentHeight * webView.scale).toInt()
            val canvasHeight = mPaintView?.canvasHeight ?: 0
            AppLogger.BASIC.i(
                TAG,
                "updateWebContentHeightWithPaintCanvasHeight, contentHeight:$webContentHeight, canvasHeight:$canvasHeight, scale:${webView.scale}"
            )
            if (canvasHeight > webContentHeight) {
                webViewContainer.updateContentHeight((canvasHeight / webView.scale).toInt())
            }
        }
    }

    /**
     * webView.getContentHeight 返回的高度最小值为 webView 的高度。
     * 而要求是内容的真实高度，因此需要从js端去获取
     */
    suspend fun getTitleAndContentEditorHeight(webContainer: IWebViewContainerCoverpaint, webScale: Float): Pair<Int, Int> =
        suspendCancellableCoroutine { continuation ->
            webContainer.getEditorsHeight {
                val heights: List<Float> = Gson().fromJson(it, object : TypeToken<List<Float>>() {}.type)
                val titleHeight = UiHelper.dp2px(heights.getOrNull(0) ?: 0f, webScale)
                val contentHeight = UiHelper.dp2px(heights.getOrNull(1) ?: 0f, webScale)
                continuation.resume(Pair(titleHeight, contentHeight))
            }
            continuation.invokeOnCancellation {
                AppLogger.BASIC.w(TAG, "getTitleAndContentEditorHeight canceled")
            }
        }

    //部分场景布局没有初始化成功，需要增加200延迟，使用这个校正机制
    fun correctDoodleRestart() {
        mPaintView?.postDelayed({ correctingDoodle() }, 200)
    }

    fun moveBy(scroll: Float, isDrag: Boolean = false) {
        val canvasHeight = mPaintView?.canvasHeight ?: 0
        val noteHeight = getNoteHeight()
        if (noteHeight + scroll > canvasHeight) {
            if (!isDrag) {  //拖拽时 不让 自动补屏
                mPaintView?.extendCanvas(scroll)
            }
        }
        if (scroll != 0F) {
            mPaintView?.moveBy(0F, -scroll)
        }
    }

    fun resetContentScale() {
        Log.i(TAG, "resetContentScale")
        mCurMatrix.reset()
        if (mStandardWidth > 0 || mScale == 1.0F) {
            updateContent(mInitPaintX, getCorrectValue(true, true), mInitPaintScale)
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            mRichRecyclerView?.animationMatrix = mCurMatrix
            mWebView?.animationMatrix = mCurMatrix
            mBackground?.animationMatrix = mCurMatrix
        }
    }

    var mContentWidth = -1 //需要显示的大小，即视觉上面涂鸦和图文的宽度
    var mStandardWidth = -1 //基准的大小，即父子级的子集宽度
    var mPaintWidth = -1  //涂鸦的宽度，即涂鸦布局的宽度getCorrectValue
    var mScale = 1.0F
    var diffScale = 1.0F
    var mPaintScale = 1.0F
    var mPaintX = 1.0f
    var paddingOffset = 0
    var mZoomScale = 1.0F

    var mInitPaintX = 0F
    var mInitPaintScale = 1.0F
    val mNeedSmallScaleFlod = 1.07F
    var mNeedSmallScale = 1.1F
    var mMinScale = 1.0F
    var mMaxScale = 6.0F
    var mPaintStandardScale = 1.0F

    val minScaleValue = 0.8F
    val maxScaleValue = 6.0F
    //调试的时候基准值给父子级的宽度，
    //获取scale比例，基准值(一个预先设定的值，比如父子级的宽)和布局的宽（非父子级为视觉提供的图文的宽度，即recycvlerview的宽度，父子级为父子级的子元素的宽note_twopane_detail_size）的比例
    //获取x的差值，基准值和布局宽的（非父子级为recyclerview的宽度）差值除以2

    fun initPaintValue(){
        mPaintScale = mInitPaintScale
        mPaintX = mInitPaintX
    }

    @SuppressLint("ObjectAnimatorBinding")
    fun setScaleInit(
        isTwoPane: Boolean,
        isMulitiScreen: Boolean,
        configuration: Configuration,
        isReCreate: Boolean,
        rotation: Int,
        activity: Activity? = null,
        isCallDetail:Boolean =false
    ) {
        mContentWidth = CoverScaleRatio.getRatioContent(isTwoPane, isMulitiScreen, configuration, activity,isCallDetail)
        mStandardWidth = CoverScaleRatio.getRatioStandard()
        paddingOffset = 0
        val offsetValue = CoverScaleRatio.getOffsetValue()
        mScale = mContentWidth.toFloat() / mStandardWidth
        onSetScaleListener?.invoke(mScale.coerceAtMost(1.0F))
        mWebView?.let {
            AppLogger.BASIC.d(TAG, "setScaleInit isReCreate: $isReCreate isTwoPane $isTwoPane isMulitiScreen $isMulitiScreen isCallDetail:$isCallDetail")

            if (mScale != 0F) {
                it.pivotX = mStandardWidth.toFloat() / 2
                it.pivotY = 0F
                mBackground?.pivotX = mStandardWidth.toFloat() / 2
                mBackground?.pivotY = 0F
            }
            mPaintWidth = CoverScaleRatio.getRatioPaint(isTwoPane, isMulitiScreen, configuration, rotation, activity,isCallDetail)
            //mContent = mStandardWidth * mScale
            mInitPaintScale = mStandardWidth * mScale / mPaintWidth
            (it as? BounceWebView)?.setInitScale(mInitPaintScale)
            mInitPaintX = ((mPaintWidth - mStandardWidth * mScale) / 2).toFloat()
            mPaintStandardScale = mStandardWidth.toFloat() / mPaintWidth
            initPaintValue()
            //这个方法可以设置回调的初始值嘛
            //涂鸦内容要显示的宽度除以 paintview真是布局宽度
            if (UiHelper.isDeviceFold()) {
                mNeedSmallScale = mNeedSmallScaleFlod
            }
            if (mScale < mNeedSmallScale) {
                //如果默认放大倍数小于1.25，首次进入涂鸦不可以缩小,以当前系数为最小值 //最大值是原来的6倍但不超过6
                if (UiHelper.isDevicePad() && !isTwoPane && CoverScaleRatio.isPortrait(rotation)) {
                    mMinScale = mStandardWidth.toFloat() * mScale / mPaintWidth * 0.8F
                } else {
                    mMinScale = mStandardWidth.toFloat() * mScale / mPaintWidth
                }
                mMaxScale = Math.min(mStandardWidth.toFloat() / mPaintWidth * maxScaleValue, mMaxScale)
            } else {
                //如果默认放大倍数大于1.25，首次进入涂鸦可以缩小 // 但最大的系数不能超过6倍
                //因为mStandardWidth.toFloat() * mScale / mPaintWidth / mNeedSmallScale 肯定大于1，所以整体缩小0.8即可
                mMinScale = mStandardWidth.toFloat() * mScale / mPaintWidth * 0.8F
                //这里限制了最大的，否则如果直接写死600太大了
                mMaxScale = Math.min(mStandardWidth * mScale / mPaintWidth * maxScaleValue, maxScaleValue)
            }
            mPaintView?.post {
                mPaintView.setMinScale(mMinScale)
                mPaintView.setMaxScale(mMaxScale)
                updateContent(mInitPaintX, 0F, mInitPaintScale)
            }
            AppLogger.BASIC.d(TAG, "setScaleInit mMinScale=$mMinScale,mMaxScale=$mMaxScale,mScale=$mScale")
            AppLogger.BASIC.d(TAG, "setScaleInit mContentWidth=$mContentWidth,mStandardWidth=$mStandardWidth,mPaintWidth=$mPaintWidth")
            AppLogger.BASIC.d(TAG, "setScaleInit offsetValue=$offsetValue,mInitPaintScale=$mInitPaintScale,mInitPaintX=$mInitPaintX")
        }
    }

    fun floatToInt(f: Float): Int {
        var i = 0
        i = if (f > 0) {
            //正数
            ((f * 10 + 5) / 10).toInt()
        } else if (f < 0) {
            //负数
            ((f * 10 - 5) / 10).toInt()
        } else {
            0
        }
        return i
    }

    var coverTotalYF = 0F
    var coverTotalYInt = 0

    var coverLeapTotalYF = 0F
    var coverLeapTotalYInt = 0

    val scrollListener = object : PaintView.ScrollListener {
        private fun showContentWithPercent(diff: Float, scale: Float): Boolean {
            return (diff > 0.055F)
                    || (popZoomWindowShowing && diff > 0.02F)
                    || (popZoomWindowShowing && (scale == 1.0F || scale == mMinScale || scale == mMaxScale))
        }

        @RequiresApi(Build.VERSION_CODES.Q)
        override fun onScrollScale(x: Float, y: Float, dx: Float, dy: Float, scale: Float) {
            GlobalScope.launch(Dispatchers.Main) {
                Log.i(TAG, "onScrollScale: $scale")
                val diff = Math.abs(scale - diffScale)
                if (showContentWithPercent(diff, scale)) {
                    diffScale = scale
                    webViewContainer?.setPaintScrollScale(scale)
                    if (mHandler.hasCallbacks(mDisMissDialogTask)) {
                        mHandler.removeCallbacks(mDisMissDialogTask)
                    }
                    var content = 0F
                    if (mScale < mNeedSmallScale) {
                        //minitpaintscale 和 min 是一个值
                        content = 1 + (scale - mInitPaintScale) / (mMaxScale - mInitPaintScale) * (maxScaleValue - 1)
                    } else {
                        if (scale >= mInitPaintScale) {
                            content =
                                1 + (scale - mInitPaintScale) / (mMaxScale - mInitPaintScale) * (maxScaleValue - 1)
                        } else {
                            content =
                                1 - (mInitPaintScale - scale) / (mInitPaintScale - mMinScale) * (1 - minScaleValue)
                        }
                    }
                    val contentWithPercent = mPaintView?.context?.getString(
                        R.string.zoom_content,
                        (content * 100).toInt().toString()
                    )
                    contentWithPercent?.let {
                        popZoom.show(contentWithPercent, isTwopane)
                        popZoomWindowShowing = true
                    }
                } else {
                    mHandler.postDelayed(mDisMissDialogTask, 2000)
                }
                mPaintScale = scale
                mPaintX = x
                mZoomScale = scale / mInitPaintScale
                mCurMatrix.reset()
                mCurMatrix.postScale(scale / mInitPaintScale, scale / mInitPaintScale)


                mCurMatrix.postTranslate(x - mInitPaintX - paddingOffset * (scale - 1.0F), 0F)
                mWebView?.animationMatrix = mCurMatrix
                mBackground?.animationMatrix = mCurMatrix
                //去精度的逻辑
                coverTotalYF += dy * mInitPaintScale
                val valueF = coverTotalYF - coverTotalYInt
                val valueInt = floatToInt(valueF)
                coverTotalYInt += valueInt
                //去精度的逻辑

                mRichRecyclerView?.scrollBy(0, -valueInt)
                mWebView?.scrollBy(0, -valueInt)
                mPaintView?.rolling(0F, true)
                //step
                onScrollScaleListener?.invoke(y)
            }
        }

        //撤销涂鸦内容，触发onLeaped回调，接口含义同scrollScale回调
        //撤销图文层内容，通过UpdateContent同步涂鸦
        override fun onLeaped(x: Float, y: Float, dx: Float, dy: Float, scale: Float) {
            Log.i(TAG, "onLeap: " + dy)
            mPaintView?.post {
                mCurMatrix.reset()
                mCurMatrix.postScale(scale / mInitPaintScale, scale / mInitPaintScale)
                val value = (mPaintWidth - x * 2 - mStandardWidth * mScale * scale / mInitPaintScale) / 2.0F
                mCurMatrix.postTranslate(
                    x / mScale - (mScale - 1.0F) * value / mScale - mInitPaintX / mScale - paddingOffset / mScale * (scale - 1.0F),
                    0F
                )
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    mRichRecyclerView?.animationMatrix = mCurMatrix
                    mWebView?.animationMatrix = mCurMatrix
                    mBackground?.animationMatrix = mCurMatrix
                }
                //去精度的逻辑         //比例转换
                coverLeapTotalYF += dy / mScale
                val valueF = coverLeapTotalYF - coverLeapTotalYInt
                val valueInt = floatToInt(valueF)
                coverLeapTotalYInt += valueInt
                mRichRecyclerView?.scrollBy(0, -valueInt.toInt())
                mWebView?.scrollBy(0, -valueInt)
                mPaintView.rolling(0F, true)
                mPaintX = x
                correctingDoodleInCover()
            }
            onLeapedListener?.invoke(scale)
        }

        override fun onScrollStart() {
            Log.i(TAG, "onScrollStart")
            scrollScaling = true
            coverTotalYF = 0F
            coverTotalYInt = 0
            coverLeapTotalYF = 0F
            coverLeapTotalYInt = 0
            mainScope?.launch {
                if (initialized) {
                    rollStart()
                }
                onScrollStartListener?.invoke()
            }
        }

        override fun onScrollEnd() {
            Log.i(TAG, "onScrollEnd")
            scrollScaling = false
            mHandler.postDelayed(mDisMissDialogTask, 2000)
            mainScope?.launch {
                if (initialized) {
                    rollEnd()
                }
                if (isOutOfCanvas()) {
                    showReturnButton()
                }
                onScrollEndListener?.invoke()
            }
        }

        override fun onScrollRestricted() {
            GlobalScope.launch(Dispatchers.Main) {
                correctingDoodle()
                mPaintView?.context?.toast(R.string.doodle_reach_limit)
            }
        }
    }

    fun updateContent(x: Float, y: Float, scale: Float) {
        Log.i(TAG, "updateContent x: $x y: $y scale: $scale")
        Log.i(TAG, "updateContent mContentWidth=$mContentWidth,mStandardWidth=$mStandardWidth,mPaintWidth=$mPaintWidth")
        mPaintView?.updateContent(x, y, scale)
    }

    fun updateSkinView(owner: LifecycleOwner?) {
        owner?.let {
            mRichRecyclerView?.postDelayed({
                skinOffsetChangeListener?.let { listener ->
                    val height = getSkinViewOffset()
                    Log.i(TAG, "updateSkinView offset: $height")
                    listener.invoke(-height)
                    SkinManager.updateManualSkinPath(it, height)
                }
            }, DELAY_TIME_MILLS_100)
        }
    }

    fun getSkinViewOffset(): Float {
        if (itemHeightArray.size == 0) {
            return 0f
        }
        var correct = 0f
        /*mRichRecyclerView?.let { richRecyclerView ->
            richRecyclerView.localLayoutManager.let {
                val firstVisiblePosition =
                    it.findFirstVisibleItemPosition()
                val firstVisibleItem =
                    it.findViewByPosition(firstVisiblePosition)
                val lastY = firstVisibleItem?.y ?: 0f
                correct = lastY
                for (i in 0 until firstVisiblePosition) {
                    if (i < itemHeightArray.size) {
                        correct -= itemHeightArray[i]
                    }
                }
                return correct
            }
            AppLogger.NOTE.d(TAG, "getSkinViewOffset localLayoutManager is null")
            return correct
        }*/
        AppLogger.NOTE.d(TAG, "getSkinViewOffset richRecyclerView is null")
        return correct
    }

    /**
     *  recycle all callback , Avoid memory leaks
     */
    fun release() {
        popZoomWindowShowing = false
        mWebView = null
        onInitializedListener = null
        onScrollScaleListener = null
        onScrollStartListener = null
        onScrollEndListener = null
        onLeapedListener = null
        onPaintSavedListener = null
        onSaveShareListListener = null
        onLoadedListener = null
        onSetScaleListener = null
        onAddNodeListener = null
        onRedoListener = null
        onUndidListener = null
    }
}