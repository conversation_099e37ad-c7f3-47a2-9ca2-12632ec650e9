package com.nearme.note.paint.coverdoodle

import android.util.Log
import androidx.lifecycle.*
import com.nearme.note.main.UIConfigMonitor
import com.nearme.note.view.helper.UiHelper
import com.oplus.notes.webview.container.api.IUndoManager
import com.oplus.richtext.editor.view.CoverPaintView
import com.oplusos.vfxsdk.doodleengine.PaintView

class CoverDoodleLifeCyclerCoverPaint(
    val mPaintView: CoverPaintView?,
    val mCoverDoodlePresenter: CoverDoodlePresenterCoverPaint?,
    val mSaveListener: PaintView.SaveListener,
    val mController: PaintView.PaintViewListener,
    val mScrollController: PaintView.ScrollListener,
    val mWebUndoManager: IUndoManager? = null
) : LifecycleEventObserver {

    private val TAG = "CoverDoodleLifeCycler"
    private val totalPage = 30
    private var isPaused = false
    var isFoldOpen = true

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {

        when (event) {
            Lifecycle.Event.ON_CREATE -> {
                Log.i(TAG, "ON_CREATE")
                mPaintView?.addListener(mController)
                mPaintView?.setScrollListener(mScrollController)
                mPaintView?.setSaveListener(mSaveListener)
                mPaintView?.onCreate(totalPage)
            }
            Lifecycle.Event.ON_START -> {
                Log.i(TAG, "ON_START")
                mPaintView?.onStart()
                mCoverDoodlePresenter?.isStop = false
                if (mCoverDoodlePresenter?.isNeedRestartCorrect == true) {
                    mCoverDoodlePresenter?.correctDoodleRestart()
                    mCoverDoodlePresenter?.isNeedRestartCorrect = false
                } else {
                    if (mCoverDoodlePresenter?.isBackFromShare == true) {
                        mCoverDoodlePresenter?.isBackFromShare = false
                    } else {
                        //折叠屏config周期比较特殊，这里config的scrolltotop有时候触发时机有误，这里判断start的时候如果fold状态发生变化，则触发一次scroltotop
                        if (UiHelper.isDeviceFold()) {
                            val isFoldingOpen = UIConfigMonitor.isFoldingModeOpen()
                            if (isFoldingOpen != isFoldOpen) {
                                mCoverDoodlePresenter?.scrollToTop()
                                isFoldOpen = isFoldingOpen
                            } else {
                                mCoverDoodlePresenter?.correctingDoodle()
                            }
                        } else {
                            mCoverDoodlePresenter?.correctingDoodle()
                        }
                    }
                }
            }
            Lifecycle.Event.ON_RESUME -> {
                Log.i(TAG, "ON_RESUME")
                mPaintView?.onResume()
                if (isPaused) {
                    isPaused = false
                    mPaintView?.invalidate()
                }
            }
            Lifecycle.Event.ON_PAUSE -> {
                Log.i(TAG, "ON_PAUSE")
                mPaintView?.onPause()
            }
            Lifecycle.Event.ON_STOP -> {
                Log.i(TAG, "ON_STOP")
                mPaintView?.onStop()
                isPaused = true
                mCoverDoodlePresenter?.isStop = true
            }
            Lifecycle.Event.ON_DESTROY -> {
                Log.i(TAG, "ON_DESTROY")
                Log.i(TAG, "mPaintView?.isStrokeEmpty" + mPaintView?.isStrokeEmpty)
                Log.i(TAG, "mUndoManager.isUndoAvailable()" + mWebUndoManager?.isUndoAvailable())
                //如果内容为空且没有上一步，则关闭cache保存功能
                //平板46分屏旋转的时候，会触发两次ondestroy，导致两次触发cache，保存cache失败后，会删除cache文件导致涂鸦文件丢失
                //这里判断内容为空的时候，且没有上一步的时候，不去触发cache机制。
                if (mPaintView?.isStrokeEmpty == true && (mWebUndoManager?.isUndoAvailable() == false)) {
                    mPaintView?.disableEmergencySave()
                }
                mPaintView?.removeListener(mController)
                mPaintView?.onDestroy()
            }
            else -> {
                // do nothing
            }
        }

    }

}