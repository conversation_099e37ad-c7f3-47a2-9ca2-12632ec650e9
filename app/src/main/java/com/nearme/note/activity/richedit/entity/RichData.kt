package com.nearme.note.activity.richedit.entity

import android.text.TextUtils
import com.google.gson.Gson
import com.heytap.usercenter.accountsdk.utils.GsonUtil
import com.nearme.note.MyApplication
import com.nearme.note.activity.richedit.DataOperatorController
import com.nearme.note.activity.richedit.entity.RichData.Companion.PICS_UPPER_BOUND
import com.nearme.note.thirdlog.TAG
import com.nearme.note.thirdlog.ThirdLogParser
import com.nearme.note.util.AttachmentMenuStatiscsUtils
import com.nearme.note.util.StringUtils.hasTableTag
import com.oplus.note.data.CombinedCard
import com.oplus.note.data.third.ThirdLogParagraph
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.repo.note.entity.PageResult
import com.oplus.note.repo.note.entity.RichNote
import com.oplus.note.repo.note.entity.SpeechLogInfo
import com.oplus.note.repo.note.entity.SubAttachment
import com.oplus.note.utils.LcrAttachmentUtils
import com.oplus.notes.webview.container.api.NoteHtmlData
import com.oplus.richtext.core.parser.HtmlParser
import java.io.File
import java.util.UUID

/**
 * The representation of rich note in view-layer
 */
data class RichData(
    /**
     * Rich note meta info, like folder info, state,and so on
     */
    val metadata: RichNote,
    /**
     * Title rich note info.记录view中的标题数据，部分场景下和metadata中存储的title字段值可能不一致。
     */
    val title: Data,
    /**
     * Data list contains all rich items
     */
    @Deprecated(message = "use webItems instead")
    val items: MutableList<Data>,  //list pic text //显示在列表中的
    /**
     * Paints info.
     * update property name "paints" to "subAttachments" by id 2241553
     */
    var subAttachments: MutableList<Attachment> = mutableListOf(), //paint(coverpaint paint) and voice

    var coverPictureAttachment: Attachment?, // cover_pic

    var lrcAttachment: Attachment? = null,

    var speechLogInfo: SpeechLogInfo? = null,
    /**
     * Web data list contains all rich items
     */
    val webItems: MutableList<Data> = mutableListOf(),
    /**
     * 保存来自外部的临时数据，比如：通话界面新建便签、OCR新建便签、浏览器分享到便签、中转站分享到便签等。
     * 使用后需要及时删除，如：
     * 1. 解析并更新到RichNote.htmlText后删除，避免重复
     * 2. 详情页加载完成后删除，避免重复
     */
    val extItems: MutableList<Data> = mutableListOf()
) {
    companion object {
        const val PICS_UPPER_BOUND = 50
        const val CONTENT_COUNT_UNDEFINED = -1

        /**
         * Check if the given item is image type.
         */
        fun isImageItem(item: Data): Boolean {
            return item.type == Data.TYPE_ATTACHMENT && item.attachment?.type == Attachment.TYPE_PICTURE
        }

        fun isVideoItem(item: Data, attachmentList: MutableList<Attachment>): Boolean {
            return isVideoItemInternal(item, attachmentList)
        }

        fun findItem(item: Data, attachmentList: MutableList<Attachment>, vararg types: Int): Attachment? {
            val attachment = item.attachment ?: return null
            val findAttachment = attachmentList.find {
                it.subAttachment?.associateAttachmentId == attachment.attachmentId
            }
            val isFind =
                (findAttachment != null) && item.type == Data.TYPE_ATTACHMENT && types.contains(findAttachment.type)
            if (isFind) {
                return findAttachment
            }
            return null
        }

        fun isHintTextItem(item: Data): Boolean {
            return item.type == Data.TYPE_PHONE_HINT_TEXT
        }

        fun isCardType(item: Data): Boolean {
            return item.type == Data.TYPE_CARD
        }

        fun isVoiceItem(item: Data, attachmentList: MutableList<Attachment>): Boolean {
            val attachment = item.attachment?.attachmentId?.let { id ->
                attachmentList.find {
                    id == it.subAttachment?.associateAttachmentId &&
                            (it.type == Attachment.TYPE_VOICE)
                }
            }
            return item.type == Data.TYPE_ATTACHMENT && (attachment != null)
        }

        fun isSpeechAudioItem(item: Data, attachmentList: MutableList<Attachment>): Boolean {
            val attachmentId = item.attachment?.attachmentId ?: ""
            return item.type == Data.TYPE_ATTACHMENT && isSpeechAudioItem(attachmentId, attachmentList)
        }

        fun isSpeechAudioItem(attachmentId: String, attachmentList: MutableList<Attachment>): Boolean {
            val tempList = ArrayList(attachmentList)
            val attachment = tempList.find {
                attachmentId == it.subAttachment?.associateAttachmentId &&
                        (it.type == Attachment.TYPE_SPEECH_AUDIO)
            }
            return attachment != null
        }

        fun isIdentityVoiceItem(item: Data, attachmentList: MutableList<Attachment>): Boolean {
            val attachment = item.attachment?.attachmentId?.let { id ->
                attachmentList.find {
                    id == it.subAttachment?.associateAttachmentId &&
                            (it.type == Attachment.TYPE_IDENTIFY_VOICE)
                }
            }
            return item.type == Data.TYPE_ATTACHMENT && (attachment != null)
        }

        fun isExternalAudioItem(item: Data, attachmentList: MutableList<Attachment>): Boolean {
            val attachment = item.attachment?.attachmentId?.let { id ->
                attachmentList.find {
                    id == it.subAttachment?.associateAttachmentId &&
                            (it.type == Attachment.TYPE_EXTERNAL_AUDIO)
                }
            }
            return item.type == Data.TYPE_ATTACHMENT && (attachment != null)
        }

        fun isIdentityVoiceItem(picAttachId: String?, subAttachments: MutableList<Attachment>?): Boolean {
            val attachment = subAttachments?.find {
                it.subAttachment?.associateAttachmentId == picAttachId
            }
            return (attachment?.type == Attachment.TYPE_IDENTIFY_VOICE)
        }

        private fun isScheduleCardItem(item: Data, attachmentList: MutableList<Attachment>): Boolean {
            val attachment = item.attachment?.attachmentId?.let { id ->
                attachmentList.find { id == it.subAttachment?.associateAttachmentId && it.type == Attachment.TYPE_SPEECH_SCHEDULE }
            }
            return item.type == Data.TYPE_ATTACHMENT && (attachment != null)
        }

        private fun isContactCardItem(item: Data, attachmentList: MutableList<Attachment>): Boolean {
            val attachment = item.attachment?.attachmentId?.let { id ->
                attachmentList.find { id == it.subAttachment?.associateAttachmentId && it.type == Attachment.TYPE_SPEECH_CONTACT }
            }
            return item.type == Data.TYPE_ATTACHMENT && (attachment != null)
        }

        private fun isFileCardItemInternal(item: Data, attachmentList: MutableList<Attachment>): Boolean {
            val attachment = item.attachment?.attachmentId?.let { id ->
                attachmentList.find { id == it.subAttachment?.associateAttachmentId && it.type == Attachment.TYPE_FILE_CARD }
            }
            return item.type == Data.TYPE_ATTACHMENT && (attachment != null)
        }

        private fun isVideoItemInternal(
            item: Data,
            attachmentList: MutableList<Attachment>
        ): Boolean {
            val attachment = item.attachment?.attachmentId?.let { id ->
                attachmentList.find { id == it.subAttachment?.associateAttachmentId && it.type == Attachment.TYPE_VIDEO }
            }
            return item.type == Data.TYPE_ATTACHMENT && (attachment != null)
        }

        fun isContactItem(item: Data, attachmentList: MutableList<Attachment>): Boolean {
            return isContactCardItem(item, attachmentList)
        }

        fun isScheduleItem(item: Data, attachmentList: MutableList<Attachment>): Boolean {
            return isScheduleCardItem(item, attachmentList)
        }

        fun isAudioItem(item: Data, attachmentList: MutableList<Attachment>): Boolean {
            return isSpeechAudioItem(item, attachmentList) || isVoiceItem(item, attachmentList) || isExternalAudioItem(item, attachmentList)
        }

        fun isFileCardItem(item: Data, attachmentList: MutableList<Attachment>): Boolean {
            return isFileCardItemInternal(item, attachmentList)
        }

        fun isOcrHintType(item: Data): Boolean {
            return item.type == Data.TYPE_OCR_HINT
        }

        fun isSummayType(item: Data): Boolean {
            return item.type == Data.TYPE_SUMMAY
        }

        fun isSummaryDocumentType(item: Data, attachmentList: MutableList<Attachment>): Boolean {
            val attachment = item.attachment?.attachmentId?.let { id ->
                attachmentList.find { id == it.subAttachment?.associateAttachmentId && it.type == Attachment.TYPE_FILE_CARD }
            }
            return item.type == Data.TYPE_ATTACHMENT && (attachment != null)
        }

        fun isCardLoadingType(item: Data): Boolean {
            return item.type == Data.TYPE_COMBINED_CARD_LOADING
        }

        fun positionDataType(
            note: RichData?,
            dataPosition: Int,
            controller: DataOperatorController
        ): Int? {
            val data = note?.webItems?.get(dataPosition)
            val voiceAttachment = data?.let { note.findSubAttachment(it, Attachment.TYPE_VOICE) }
            val speechAudioAttachment = data?.let { note.findSubAttachment(it, Attachment.TYPE_SPEECH_AUDIO) }
            return if (note != null && controller.indexIsInclude(note, dataPosition)) {
                when {
                    (voiceAttachment != null) -> AttachmentMenuStatiscsUtils.TYPE_ATTACHMENT_TYPE_VOICE
                    (speechAudioAttachment != null) -> AttachmentMenuStatiscsUtils.TYPE_ATTACHMENT_TYPE_SPEECH_AUDIO
                    isImageItem(note.webItems[dataPosition]) -> AttachmentMenuStatiscsUtils.TYPE_ATTACHMENT_TYPE_IMAGE
                    isCardType(note.webItems[dataPosition]) -> AttachmentMenuStatiscsUtils.TYPE_ATTACHMENT_TYPE_CARD
                    else -> null
                }
            } else {
                null
            }
        }

        fun isTextType(item: Data): Boolean {
            return item.type == Data.TYPE_TEXT || item.type == Data.TYPE_OCR_HINT || item.type == Data.TYPE_PHONE_HINT_TEXT
        }
    }

    internal var contentCharCount = CONTENT_COUNT_UNDEFINED

    init {
        contentCharCount = metadata.text.length
        AppLogger.BASIC.d(TAG, "init contentCharCount = $contentCharCount")
    }

    fun halfDeepCopy(): RichData {
        val newItems = items.map { it.copy() }.toMutableList()
        val newWebItems = webItems.map { it.copy() }.toMutableList()
        val newSubAttachments = subAttachments.map { it.copy() }.toMutableList()
        val length = getContentCount()
        return RichData(
            metadata = metadata.copy(),
            title = title.copy(),
            items = newItems,
            subAttachments = newSubAttachments,
            coverPictureAttachment = coverPictureAttachment,
            webItems = newWebItems
        ).apply {
            setContentCharCount(length)
        }
    }

    fun deepCopy(): RichData {
        val newItems = items.map { it.deepCopy() }.toMutableList()
        val newWebItems = webItems.map { it.deepCopy() }.toMutableList()
        val newSubAttachments = subAttachments.map { it.copy() }.toMutableList()
        val length = getContentCount()
        return RichData(
            metadata = metadata.copy(),
            title = title.deepCopy(),
            items = newItems,
            subAttachments = newSubAttachments,
            coverPictureAttachment = coverPictureAttachment,
            webItems = newWebItems
        ).apply {
            setContentCharCount(length)
        }
    }

    fun getSpeechLogInfoList(): List<ThirdLogParagraph>? {
        val path = lrcAttachment?.absolutePath(MyApplication.appContext) ?: return null
        val file = File(path)
        if (file.exists()) {
            val thirdLog = ThirdLogParser.parseThirdLogFromFile(file)
            return if (thirdLog == null) {
                null
            } else {
                val list = thirdLog.thirdLogParagraph
                val speechStartTime = thirdLog.speechStartTime
                val baseTime = if (speechStartTime == 0L) {
                    list[0].startTime
                } else {
                    speechStartTime
                }
                for (i in list.indices) {
                    list[i].startTime -= baseTime
                    list[i].endTime -= baseTime
                }
                list
            }
        } else {
            return null
        }
    }

    @Suppress("LongMethod")
    fun reNewLocalId(shouldDeleteOriginalAttachment: Boolean = true): RichData {
        val newLocalId = UUID.randomUUID().toString()
        val newGlobalId = UUID.randomUUID().toString()
        val newMetadata = metadata.copy(
            localId = newLocalId,
            state = RichNote.STATE_NEW,
            globalId = newGlobalId,
            createTime = System.currentTimeMillis(),
            sysVersion = null,
            encryptSysVersion = null
        )

        val attachmentIdMap = mutableMapOf<String, String>()
        val newItems = mutableListOf<Data>()
        items.forEach {
            if (it.type == Data.TYPE_ATTACHMENT) {
                it.attachment?.let { oldAttachment ->
                    val oldAttachmentId = oldAttachment.attachmentId
                    val newAttachment = oldAttachment.copy(
                        attachmentId = UUID.randomUUID().toString(),
                        richNoteId = newLocalId,
                        state = Attachment.STATE_NEW
                    )
                    val oldFile = File(oldAttachment.absolutePath(MyApplication.appContext))
                    val newFile = File(newAttachment.absolutePath(MyApplication.appContext))
                    if (oldFile.exists()) {
                        oldFile.copyRecursively(newFile)
                        if (shouldDeleteOriginalAttachment) {
                            oldFile.deleteRecursively()
                        }
                    }
                    it.attachment = newAttachment

                    attachmentIdMap[oldAttachmentId] = newAttachment.attachmentId
                }
            }
            newItems.add(it)
        }
        val newSubAttachments = mutableListOf<Attachment>()
        val newCardsAttachments = mutableListOf<Pair<Attachment, Attachment>>()
        val newLrcPicAttachments = mutableMapOf<String, String>()
        val identifyVoiceAttachments = mutableListOf<Pair<Attachment, Attachment>>()
        val identifyVoiceLrcAttachments = mutableMapOf<String, Pair<Attachment, Attachment>>()
        subAttachments.forEach { oldAttachment ->
            val newAttachment = oldAttachment.copy(
                attachmentId = UUID.randomUUID().toString(),
                richNoteId = newLocalId,
                state = Attachment.STATE_NEW,
                fileName = null,
            )
            val oldAssociateAttachmentId = oldAttachment.subAttachment?.associateAttachmentId
            if (oldAssociateAttachmentId != null) {
                attachmentIdMap[oldAssociateAttachmentId]?.apply {
                    newAttachment.subAttachment = SubAttachment(this)
                }
                if (oldAttachment.type == Attachment.TYPE_SPEECH_CONTACT || oldAttachment.type == Attachment.TYPE_SPEECH_SCHEDULE) {
                    /**
                     * Replace attachment mapping in speechLogInfo.combinedCard
                     */
                    newCardsAttachments.add(Pair(oldAttachment, newAttachment))
                }
            }

            if (oldAttachment.type == Attachment.TYPE_LRC_PICTURE) {
                /**
                 * Replace attachment id of type [Attachment.TYPE_SPEECH_LRC] in Json file
                 */
                newLrcPicAttachments[oldAttachment.attachmentId] = newAttachment.attachmentId
            } else if (oldAttachment.type == Attachment.TYPE_IDENTIFY_VOICE) {
                identifyVoiceAttachments.add(Pair(oldAttachment, newAttachment))
            } else if (oldAttachment.type == Attachment.TYPE_IDENTIFY_VOICE_LRC) {
                identifyVoiceLrcAttachments[oldAttachment.attachmentId] = Pair(oldAttachment, newAttachment)
            }

            val oldFile = File(oldAttachment.absolutePath(MyApplication.appContext))
            val newFile = File(newAttachment.absolutePath(MyApplication.appContext))
            if (oldFile.exists()) {
                oldFile.copyRecursively(newFile)
                if (shouldDeleteOriginalAttachment) {
                    oldFile.deleteRecursively()
                }
            }
            newAttachment.fileName = newFile.name
            newAttachment.cloudMetaData?.apply {
                fileName = newAttachment.fileName
                id = newAttachment.attachmentId
            }
            newSubAttachments.add(newAttachment)
        }
        if (identifyVoiceAttachments.isNotEmpty()) {
            identifyVoiceAttachments.forEach { (oldVoiceAttachment, newVoiceAttachment) ->
                val newVoiceLrcAttachment = identifyVoiceLrcAttachments[oldVoiceAttachment.extra?.asrAttachId]?.second
                newVoiceAttachment.extra?.let {
                    it.asrAttachId = newVoiceLrcAttachment?.attachmentId
                    it.audioDuration = it.audioDuration
                }
            }
        }

        updateCombineCardAtt(newCardsAttachments)
        /**
         * reNew时，需要将通话摘要笔记的附件全部重新创建并关联到speechLogInfo中
         */
        var newCoverPictureAttachment: Attachment? = null
        var newLrcAttachment: Attachment? = null
        var newSpeechLog: SpeechLogInfo? = null

        if (coverPictureAttachment != null) {
            newCoverPictureAttachment = coverPictureAttachment?.copy(
                attachmentId = UUID.randomUUID().toString(),
                richNoteId = newLocalId,
                state = Attachment.STATE_NEW
            )

            val oldFile = coverPictureAttachment?.absolutePath(MyApplication.appContext)?.let { File(it) }
            val newFile = newCoverPictureAttachment?.absolutePath(MyApplication.appContext)?.let { File(it) }
            if (oldFile?.exists() == true && newFile != null) {
                oldFile.copyRecursively(newFile)
                if (shouldDeleteOriginalAttachment) {
                    oldFile.deleteRecursively()
                }
            }
        }

        if (lrcAttachment != null) {
            newLrcAttachment = lrcAttachment!!.copy(
                attachmentId = UUID.randomUUID().toString(),
                richNoteId = newLocalId,
                state = Attachment.STATE_NEW
            )
            val oldFile = lrcAttachment?.absolutePath(MyApplication.appContext)?.let { File(it) }
            val newFile = File(newLrcAttachment.absolutePath(MyApplication.appContext))
            if (oldFile?.exists() == true) {
                oldFile.copyRecursively(newFile)
                if (shouldDeleteOriginalAttachment) {
                    oldFile.deleteRecursively()
                }
            }
            updateLrcAtt(newLrcAttachment, newLrcPicAttachments)
        }

        if (speechLogInfo != null) {
            newSpeechLog = speechLogInfo?.copy(
                summaryId = UUID.randomUUID().toString(),
                richNoteId = newLocalId,
                voiceLrcId = newLrcAttachment?.attachmentId,
                voiceAttId = newSubAttachments.find { it.type == Attachment.TYPE_SPEECH_AUDIO }?.attachmentId,
                audioPicId = newSubAttachments.find { it.type == Attachment.TYPE_SPEECH_AUDIO }?.subAttachment?.associateAttachmentId
            )
            updateMarks(newSpeechLog, newLrcPicAttachments)
        }
        if (attachmentIdMap.isNotEmpty()) {
            //attachment id被替换后，html中记录的id数据也需要replace。否则就会出现html中的id仍然指向旧的id，导致附件关联丢失
            var replaceHtmlText = metadata.htmlText
            attachmentIdMap.forEach { (old, new) ->
                replaceHtmlText = replaceHtmlText.replace(old, new)
            }
            AppLogger.BASIC.i("RichData", "reNewLocalId, replace html text for:$attachmentIdMap")
            newMetadata.htmlText = replaceHtmlText
        }

        return copy(
            metadata = newMetadata,
            /**重新赋值时需要保持items内存地址不改变，改变会导致adapter更新数据失效*/
            items = items.apply {
                clear()
                addAll(newItems)
            },
            webItems = webItems.apply {
                clear()
                addAll(newItems.filter { it.type == Data.TYPE_ATTACHMENT })
            },
            subAttachments = subAttachments.apply {
                clear()
                addAll(newSubAttachments)
            },
            coverPictureAttachment = newCoverPictureAttachment,
            lrcAttachment = newLrcAttachment,
            speechLogInfo = newSpeechLog
        )
    }

    /**
     * find the associated SubAttachment by Type.
     */
    fun findSubAttachment(data: Data, type: Int): Attachment? {
        return data.attachment?.attachmentId?.let { id ->
            subAttachments.find { id == it.subAttachment?.associateAttachmentId && type == it.type }
        }
    }

    fun findAudioAttachment(data: Data): Attachment? {
        val speechAudio = findSubAttachment(data, Attachment.TYPE_SPEECH_AUDIO)
        val asrAudio = findSubAttachment(data, Attachment.TYPE_IDENTIFY_VOICE)
        val voice = findSubAttachment(data, Attachment.TYPE_VOICE)
        val externalAudio = findSubAttachment(data, Attachment.TYPE_EXTERNAL_AUDIO)
        return speechAudio ?: asrAudio ?: voice ?: externalAudio
    }

    fun findDocumentAttachment(data: Data): Attachment? {
        return findSubAttachment(data, Attachment.TYPE_FILE_CARD)
    }

    fun findVideoAttachment(data: Data): Attachment? {
        return findSubAttachment(data, Attachment.TYPE_VIDEO)
    }

    fun findItem(sub: Attachment?): Data? {
        return webItems.find { it.attachment?.attachmentId == sub?.subAttachment?.associateAttachmentId }
    }

    /**
     * find the associated SubAttachment by Type and Id.
     */
    fun findSubAttachment(id: String, type: Int): Attachment? {
        return subAttachments.find { it.type == type && it.subAttachment?.associateAttachmentId == id }
    }

    fun findSubAudioAttachment(id: String): Attachment? {
        return subAttachments.find {
            (it.type == Attachment.TYPE_SPEECH_AUDIO ||
                    it.type == Attachment.TYPE_IDENTIFY_VOICE ||
                    it.type == Attachment.TYPE_VOICE ||
                    it.type == Attachment.TYPE_EXTERNAL_AUDIO) &&
                    it.subAttachment?.associateAttachmentId == id
        }
    }

    fun findAttachment(id: String, type: Int): Attachment? {
        return webItems.filter { it.type == Data.TYPE_ATTACHMENT }
            .find { it.attachment?.type == type && it.attachment?.attachmentId == id }?.attachment
    }

    fun findAttachData(id: String, type: Int): Data? {
        if (type == Data.TYPE_ATTACHMENT) {
            return webItems.find { it.attachment?.attachmentId == id }
        }

        if (type == Data.TYPE_CARD) {
            return webItems.find { it.card?.item_id == id }
        }

        return null
    }

    fun findSunAttachmentCover(): Attachment? {
        val tempList = ArrayList(subAttachments)
        return tempList.find { Attachment.TYPE_COVER_PAINT == it.type }
    }

    fun removeSunAttachmentCover() {
        val newSubAttachments = subAttachments.filter {
            it.type != Attachment.TYPE_COVER_PAINT
        }
        subAttachments.clear()
        subAttachments.addAll(newSubAttachments)
    }

    /**
     * delete paint from paints.
     */
    fun deleteSubAttachment(subAttachment: Attachment, type: Int): Boolean {
        subAttachments.find { it.attachmentId == subAttachment.attachmentId && type == it.type }?.apply {
            subAttachments.remove(this)
            return true
        }
        return false
    }

    fun addItem(index: Int, data: Data) {
        when {
            index >= items.size -> {
                items.add(data)
            }

            index < 0 -> {
                items.add(0, data)
            }

            else -> {
                items.add(index, data)
            }
        }
        when {
            index >= webItems.size -> webItems.add(data)
            index < 0 -> webItems.add(0, data)
            else -> webItems.add(index, data)
        }
    }

    fun addExtItem(index: Int, data: Data) {
        when {
            index >= extItems.size -> extItems.add(data)
            index < 0 -> extItems.add(0, data)
            else -> extItems.add(index, data)
        }
    }

    fun getRecycleTime(): Long {
        return metadata.recycleTime
    }

    fun getFolderGuid(): String {
        return metadata.folderGuid
    }

    fun getNoteGuid(): String {
        return metadata.localId
    }

    fun setTopTime(time: Long) {
        metadata.topTime = time
    }

    fun setAlarmTime(time: Long) {
        metadata.alarmTime = time
    }

    fun setSkinId(skin: String) {
        metadata.skinId = skin
    }

    fun setContentCharCount(count: Int) {
        contentCharCount = count
    }

    fun getTotalCharCount(): Int {
        val charCount = contentCharCount
        return charCount + getLinkCharCount()
    }

    fun getLinkCharCount(): Int {
        var charCount = 0
        webItems.forEach { data ->
            if (data.type == Data.TYPE_CARD) {
                val content = data.card?.url
                if (content != null) {
                    charCount += content.length
                }
            }
        }

        return charCount
    }

    private fun updateCombineCardAtt(
        replaceList: MutableList<Pair<Attachment, Attachment>>,
    ) {
        val oldCombinedCardRaw = speechLogInfo?.combinedCard
        val oldCombinedCard =
            Gson().fromJson(oldCombinedCardRaw, CombinedCard::class.java) ?: return

        oldCombinedCard.cardContacts?.forEach { cc ->
            val replaceItem =
                replaceList.find { it.first.subAttachment?.associateAttachmentId == cc.associateId }
            if (replaceItem != null) {
                cc.attId = replaceItem.second?.attachmentId
                cc.associateId = replaceItem.second?.subAttachment?.associateAttachmentId
            }
        }
        oldCombinedCard.cardSchedules?.forEach { cs ->
            val replaceItem =
                replaceList.find { it.first.subAttachment?.associateAttachmentId == cs.associateId }
            if (replaceItem != null) {
                cs.attId = replaceItem?.second?.attachmentId
                cs.associateId = replaceItem?.second?.subAttachment?.associateAttachmentId
            }
        }
        Gson().toJson(oldCombinedCard).run {
            metadata.extra?.speechLogExtra?.entityGroup = this
            speechLogInfo?.combinedCard = this
        }
    }

    /**
     * modify attachment, need upload
     */
    private fun updateLrcAtt(
        newLrcAttachment: Attachment,
        newLrcPicAttachments: MutableMap<String, String>
    ) {
        runCatching {
            LcrAttachmentUtils.replaceAttachmentIdInLrc(
                newLrcAttachment.absolutePath(
                    MyApplication.appContext
                ), newLrcPicAttachments
            )
            newLrcAttachment.url = null
            newLrcAttachment.md5 = null
        }.onFailure {
            AppLogger.BASIC.e("RichData", "updateLrcAtt error:${it.message}")
        }.onSuccess {
            AppLogger.BASIC.d("RichData", "updateLrcAtt success")
        }
    }

    private fun updateMarks(
        speechLogInfo: SpeechLogInfo?,
        newLrcPicAttachments: MutableMap<String, String>
    ) {
        runCatching {
            val marks = speechLogInfo?.speechMark
            marks?.apply {
                val newMarks =
                    LcrAttachmentUtils.replaceAttachmentIdInMarks(this, newLrcPicAttachments)
                speechLogInfo.speechMark = newMarks
            }
        }
    }


    fun isSummaryEdited(): Boolean? {
        return if (this.getFolderGuid() == FolderInfo.FOLDER_GUID_ARTICLE_SUMMARY) {
            this.metadata.extra?.isSummaryEdited()
        } else {
            this.speechLogInfo?.isSummaryEdited()
        }
    }
}

fun RichData?.isDeleted(): Boolean {
    this?.getRecycleTime()?.let { return it > 0 }
    return false
}

fun RichData?.hasTableTag(): Boolean {
    return this?.metadata?.htmlText?.hasTableTag() ?: false
}

fun RichData?.isEmpty(): Boolean {
    return isTitleEmpty() && isContentEmpty()
}

fun RichData?.isEmptyWithoutCover(): Boolean {
    return isTitleEmpty() && isContentEmptyWithoutCover()
}

fun RichData?.isTitleEmpty(): Boolean {
    this?.metadata?.title?.let { title ->
        if (title.trim().isNotEmpty()) {
            //has title
            return false
        }
    }
    return true
}

fun RichData?.isContentEmptyWithoutCover(): Boolean {
    if (this.getContentCount() > 0) {
        return false
    }

    this?.webItems?.forEach { item ->
        if (item.type == Data.TYPE_ATTACHMENT) {
            return false
        }

        //判断链接卡片是否为空
        if (item.type == Data.TYPE_CARD) {
            if (item.card != null) {
                item.card?.let {
                    val text = it.url
                    if (text.isNotEmpty()) {//has text
                        return false
                    }
                }
            } else {
                return false
            }
        }
    }
    return true
}

fun RichData?.isContentEmpty(): Boolean {
    if (this?.coverPictureAttachment != null) {
        return false
    }

    if (this.getContentCount() > 0) {
        return false
    }

    return isContentEmptyWithoutCover()
}

fun RichData?.getContentCount(): Int {
    return this?.contentCharCount ?: 0
}

fun RichData?.getPicCount(): Int {
    this?.apply {
        val tempList = ArrayList(webItems)
        return tempList.count {
            RichData.isImageItem(it) &&
                    !RichData.isScheduleItem(it, subAttachments) &&
                    !RichData.isContactItem(it, subAttachments)
        }
    }
    return 0
}

fun RichData?.getCardCount(): Int {
    this?.apply {
        return webItems.count { RichData.isCardType(it) }
    }
    return 0
}

fun RichData?.getAttachCount(): Int {
    this?.apply {
        return (this.getPicCount().plus(this.getCardCount()))
    }
    return 0
}

fun RichData?.getAddAttachCount(): Int {
    var addNum = PICS_UPPER_BOUND - getAttachCount()
    if (addNum <= 0) {
        return 0
    } else {
        return addNum
    }
}

fun RichData?.reachImageLimit(): Boolean {
    return getAttachCount() >= PICS_UPPER_BOUND
}

fun RichData.findSpeechAudioItemIndex(): Int {
    val tempList = mutableListOf<Data>().apply {
        addAll(webItems)
    }
    return tempList.indexOfFirst {
        RichData.isSpeechAudioItem(it, subAttachments)
    }
}

fun RichData.findFirstIdentifyVoiceItemIndex(): Int {
    return this.webItems.indexOfFirst {
        RichData.isIdentityVoiceItem(it, subAttachments)
    }
}

fun RichData.findVoiceItem(): Data? {
    return this.webItems.firstOrNull {
        RichData.isVoiceItem(it, subAttachments)
    }
}

fun RichData.findVoiceItems(): MutableList<Data>? {
    return this.webItems.filter {
        RichData.isVoiceItem(it, subAttachments)
    }.toMutableList()
}

fun RichData.findSpeechAudioItem(): Data? {
    return this.webItems.firstOrNull {
        RichData.isSpeechAudioItem(it, subAttachments)
    }
}

fun RichData.findIdentifyVoiceItems(): MutableList<Data>? {
    return this.webItems.filter {
        RichData.isIdentityVoiceItem(it, subAttachments)
    }.toMutableList()
}

fun RichData.findFirstIdentifyVoiceItem(): Data? {
    return this.webItems.firstOrNull {
        RichData.isIdentityVoiceItem(it, subAttachments)
    }
}

fun RichData?.hasPaintAttachment(): Boolean {
    this?.subAttachments?.forEach { item ->
        if (item.type == Attachment.TYPE_PAINT) {
            return true
        }
    }

    return false
}

fun RichData.getPageResults(): List<PageResult>? {
    var pageResults: MutableList<PageResult>? = null
    val richNote = this.metadata
    if (!TextUtils.isEmpty(richNote.web_notes)) {
        pageResults =
            GsonUtil.fromJson(richNote.web_notes, Array<PageResult>::class.java).toMutableList()
    } else {
        if (richNote.extra != null && !richNote.extra?.pageResults.isNullOrEmpty()) {
            pageResults = (richNote.extra?.pageResults?.toList()) as? MutableList<PageResult>
            pageResults?.forEach { result ->
                result.sourceLabel = richNote.extra?.sourcePackage?.label ?: ""
            }
        }
    }
    return pageResults
}

fun RichData.getPhoneHintTextData(): Data? {
    return extItems.find {
        it.type == Data.TYPE_PHONE_HINT_TEXT
    }
}

fun RichData.getTextData(): Data? {
    return extItems.find {
        it.type == Data.TYPE_TEXT
    }
}

fun RichData.getPictureAttachments(): List<Attachment> {
    val pictureAttachments: MutableList<Attachment> = mutableListOf()
    this.webItems.forEach { item ->
        if (RichData.isImageItem(item)) {
            item.attachment?.let { pictureAttachments.add(it) }
        }
    }
    return pictureAttachments
}

fun RichData.updateMetaDataWithWebViewData(plainText: String, noteHtmlData: NoteHtmlData?) {
    metadata.text = plainText
    if (!HtmlParser.isEmpty(noteHtmlData?.title)) {
        metadata.rawTitle = noteHtmlData?.title
    }
    noteHtmlData?.content?.let {
        metadata.htmlText = it
    }
    kotlin.runCatching {
        if (plainText.isEmpty()) {
            webItems.removeAll(webItems.filter { it.type == Data.TYPE_TEXT })
        }
    }.onFailure {
        AppLogger.BASIC.d("RichData", "updateMetaDataWithWebViewData ${it.message}")
    }
}
fun RichData.updateMetaDataWithWebViewDataCoverPaint(plainText: String, noteHtmlData: com.oplus.notes.webviewcoverpaint.container.api.NoteHtmlData?) {
    metadata.text = plainText
    if (!HtmlParser.isEmpty(noteHtmlData?.title)) {
        metadata.rawTitle = noteHtmlData?.title
    }
    noteHtmlData?.content?.let {
        metadata.htmlText = it
    }
    kotlin.runCatching {
        if (plainText.isEmpty()) {
            webItems.removeAll(webItems.filter { it.type == Data.TYPE_TEXT })
        }
    }.onFailure {
        AppLogger.BASIC.d("RichData", "updateMetaDataWithWebViewDataCoverPaint ${it.message}")
    }
}

fun RichData.clearExternalItems() {
    extItems.clear()
}

/**
 * 返回当前笔记中视频音频文档类型附件的数量
 *
 * @return
 */
fun RichData.countOfAudioVideoAndDoc(): Int {
    return this.subAttachments.count {
        (it.type == Attachment.TYPE_VIDEO ||
                it.type == Attachment.TYPE_FILE_CARD ||
                it.type == Attachment.TYPE_VOICE ||
                it.type == Attachment.TYPE_SPEECH_AUDIO ||
                it.type == Attachment.TYPE_IDENTIFY_VOICE ||
                it.type == Attachment.TYPE_EXTERNAL_AUDIO)
    }
}