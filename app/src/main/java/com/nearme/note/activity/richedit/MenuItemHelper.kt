/**
 * Copyright (C), 2010-2025, OPLUS Mobile Comm Corp., Ltd.
 * File           : MenuItemHelper.kt
 * Description    : MenuItemHelper.kt
 * Version        : 1.0
 * Date           : 2024/10/14
 * Author         : hufeng
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * hufeng     2024/10/14         1.0           create
 */
package com.nearme.note.activity.richedit

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.PorterDuff
import android.graphics.drawable.Drawable
import android.view.MenuItem
import androidx.appcompat.widget.ThemeUtils
import androidx.core.content.ContextCompat
import com.oplus.note.R
import com.oplus.note.logger.AppLogger

object MenuItemHelper {
    private const val TAG = "MenuItemHelper"

    @SuppressLint("RestrictedApi")
    fun updateMenuItemColor(context: Context?, menuItem: MenuItem?, drawable: Drawable?, selected: Boolean?) {
        if ((context == null) || (menuItem == null)) {
            return
        }

        AppLogger.BASIC.d(TAG, "updatePaintBtnColor $selected")
        if (selected == true) {
            drawable?.setColorFilter(ThemeUtils.getThemeAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary), PorterDuff.Mode.SRC_ATOP)
        } else {
            drawable?.setColorFilter(ContextCompat.getColor(context, R.color.note_menu_normal_color), PorterDuff.Mode.SRC_ATOP)
        }
        menuItem.icon = drawable
    }
}