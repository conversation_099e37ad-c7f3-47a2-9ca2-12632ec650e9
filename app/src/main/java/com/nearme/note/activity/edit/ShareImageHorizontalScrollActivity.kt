/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - ShareImageHorizontalScrollActivity.kt
** Description: 支持左右滑动的分享预览页
** Version: 1.0
** Date : 2025/1/8
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  yanglinlong       2025/1/8      1.0     create file
****************************************************************/
package com.nearme.note.activity.edit

import android.widget.ImageView
import com.nearme.note.skin.bean.Skin
import com.oplus.note.R

/**
 * 和 SaveImageAndShare 区别在于，多嵌套了一个 HorizontalScrollView
 * 布局中 skin_container 宽度改成 wrap_content ，让图片能够完全显示
 * 布局中显示涂鸦图片的容器 capture_cover，宽度改成 wrap_content，让涂鸦只显示屏幕那么宽
 * */
class ShareImageHorizontalScrollActivity : SaveImageAndShare() {

    companion object {
        private const val TAG = "ShareImageHorizontalScrollActivity"
        private const val TOP_EXTRA_HEIGHT_FOR_ONLINE_SKIN2 = 635
    }

    override fun getLayoutRes(): Int {
        return R.layout.share_preview_horizontal_scroll_layout
    }

    override fun onCaptureLoadFinish() {
        val detailWidth = intent.getIntExtra(KEY_BACKGROUND_WIDTH, 0)
        mLogoLinearLayout?.layoutParams?.width = detailWidth
        mGridSkinCover.layoutParams.width = detailWidth
        super.onCaptureLoadFinish()
    }

    /**横向宽度太大时，平铺会导致皮肤拉伸异常。这里特殊处理 ONLINE_SKIN_2_ID 这个皮肤，使用.9图横向拉伸，竖向限制最大高度*/
    override fun setTopExtraForOnlineSkin2(skinId: String?, topExtraBg: Skin.SharePage.Background.TopExtraBg?) {
        mTopExtraBg?.apply {
            setImageResource(R.drawable.online_skin_2_top_extra_bg)
            setTopPaddingForEmbedPureSkin(false)
            maxHeight = TOP_EXTRA_HEIGHT_FOR_ONLINE_SKIN2
            scaleType = ImageView.ScaleType.FIT_XY
        }
    }
}