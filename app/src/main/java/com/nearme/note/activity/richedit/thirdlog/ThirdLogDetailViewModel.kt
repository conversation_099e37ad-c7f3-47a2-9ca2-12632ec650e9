/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/10/26
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  dongzibin1       2023/10/26      1.0     create file
 ****************************************************************/
package com.nearme.note.activity.richedit.thirdlog

import android.content.Context
import android.content.Intent
import androidx.collection.ArrayMap
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.gson.Gson
import com.nearme.note.MyApplication
import com.nearme.note.activity.richedit.DataOperatorController
import com.oplus.note.data.third.ThirdLog
import com.oplus.note.data.third.ThirdLogParagraph
import com.nearme.note.db.AppDatabase
import com.nearme.note.model.RichNoteRepository
import com.nearme.note.model.findLrcPathByAudio
import com.nearme.note.thirdlog.ThirdLogNoteBuildHelper
import com.oplus.note.repo.note.entity.Folder
import com.nearme.note.thirdlog.ThirdLogParser
import com.nearme.note.util.NoteSearchManagerWrapper
import com.nearme.note.util.TimeUtils
import com.nearme.note.util.postValueSafe
import com.oplus.note.data.ThirdLogAIMark
import com.oplus.note.data.ThirdLogManualMark
import com.oplus.note.data.ThirdLogMark
import com.oplus.note.data.ThirdLogMarks
import com.oplus.note.data.ThirdLogPicMark
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.RichNote
import com.oplus.note.repo.note.entity.RichNoteEditType
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.note.repo.note.entity.SpeechLogInfo
import com.oplus.note.search.NoteSearchManager
import com.oplus.note.utils.ExtraJsonHelper
import com.oplus.richtext.core.utils.Constants
import com.oplus.richtext.editor.utils.MarkUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.io.File
import java.util.concurrent.CopyOnWriteArrayList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.UUID

class ThirdLogDetailViewModel : ViewModel() {

    var thirdList: List<ThirdLogParagraph>? = mutableListOf()
    var thirdDeleteList: MutableList<ThirdLogParagraph> = mutableListOf()
    var docNeedData: DocNeedData? = null
    var createTime: Long? = 0
    var phoneName: String? = ""

    companion object {
        const val TAG = "ThirdLogDetailViewModel"
        const val ACTION_EDIT_LRC = "action_edit_lrc"
    }

    private val thirdLogMarkAdapterList = mutableListOf<ThirdLogMark>()
    private var thirdLogParagraphList: List<ThirdLogParagraph>? = null
    var thirdLogMarks: ThirdLogMarks? = null
    var lrcUri: String = ""
    var currentFolder: LiveData<Folder>? = null
    val dataController = DataOperatorController()
    var thirdLog: ThirdLog? = null
    var mThirdLogData = ArrayList<ThirdLogParagraph>()
    var richNoteId: String? = null
    var speechLogInfo: SpeechLogInfo? = null
    // 是否选中全部
    internal var isAllSelected: Boolean = false
    // 是否为选择状态
    internal var isInSelection = false
    // 保存选中的内容，key为position，value是文本内容
    private val selectionThirdLogs: ArrayMap<Int, CharSequence> by lazy {
        ArrayMap()
    }
    // 复制和分享的纯文本
    internal val copyText: String
        get() = selectionThirdLogs.values.joinToString("\n")
    internal val selectedCount: MutableLiveData<Int> by lazy { MutableLiveData(0) }
    internal val thirdLogCount: Int
        get() = thirdLog?.thirdLogParagraph?.size ?: 0
    // 需要删除的图片，采用线程安全的集合
    val needDeleteImageAttachmentIds by lazy {
        CopyOnWriteArrayList<String>()
    }
    private val renameContacts by lazy { ArrayMap<Int, String>() }
    val originalNames by lazy { ArrayMap<Int, String>() }
    var speechAttachmentId = ""
    var speechPicAttachmentId = ""
    var speechAttachmentType = -1
    var speechLrcAttachmentId = ""
    var speechNewLrcAttachmentId = ""
    var updateComplete = MutableLiveData<Boolean>()
    override fun onCleared() {
        super.onCleared()
        AppLogger.BASIC.d(TAG, "onCleared")
    }

    fun clean(lifecycleOwner: LifecycleOwner?) {
        if (lifecycleOwner != null) {
            currentFolder?.removeObservers(lifecycleOwner)
        }
        currentFolder = null
    }

    private fun loadRichData() {
        kotlin.runCatching {
            richNoteId?.let {
                speechLogInfo = RichNoteRepository.querySpeechLogByRichNoteId(it)
                if (speechLogInfo == null) {
                    AppLogger.BASIC.d(TAG, "speechLogInfo == null")
                } else {
                    speechLogInfo?.speechMark?.let { speechMark ->
                        thirdLogMarks =
                            ExtraJsonHelper.fromJson(speechMark, ThirdLogMarks::class.java)
                    } ?: kotlin.run {
                        AppLogger.BASIC.w(TAG, "speechMark is null")
                    }
                }
            }
        }.onFailure { e ->
            AppLogger.BASIC.d(TAG, "loadRichData ${e.message}")
        }
    }

    fun loadThirdLogAndParseMark(context: Context, needQuery: Boolean, listener: OnLoadThirdLogListener?) {
        viewModelScope.launch(Dispatchers.IO) {
            if (speechLogInfo == null) {
                loadRichData()
            }
            AppLogger.BASIC.d(TAG, "getThirdLog uri:${lrcUri.length}")
            val file = File(lrcUri)
            if (file.exists()) {
                loadFile(file, listener)
            } else {
                lrcUri =
                    if (speechAttachmentType == Attachment.TYPE_SPEECH_AUDIO) {
                        RichNoteRepository.dao.getSpeechLogOfRichNoteId(
                            richNoteId ?: return@launch
                        )?.voiceLrcId?.let {
                            "${context.filesDir.absolutePath}/$richNoteId/$it.json"
                        }
                    } else {
                        RichNoteRepository.dao.getAttachmentById(speechAttachmentId)
                            ?.findLrcPathByAudio(context)
                    } ?: return@launch
                val file = File(lrcUri)
                if (file.exists()) {
                    loadFile(file, listener)
                } else {
                    AppLogger.BASIC.e(TAG, "file not exit")
                    withContext(Dispatchers.Main) {
                        listener?.onLoadError("file not exit")
                    }
                }
            }
        }
    }

    private suspend fun loadFile(file: File, listener: OnLoadThirdLogListener?) {
        thirdLog = ThirdLogParser.parseThirdLogFromFile(file)
        thirdLog?.thirdLogParagraph = thirdLog?.thirdLogParagraph?.filter {
            it.paragraph.isNotEmpty() || it.screenShots?.isNotEmpty() == true
        } ?: ArrayList<ThirdLogParagraph>()
        val list = thirdLog?.thirdLogParagraph
            ?: ArrayList<ThirdLogParagraph>()
        if (list.isEmpty()) {
            withContext(Dispatchers.Main) {
                listener?.onLoadError("third log is empty")
            }
        } else {
            // 每段录音的开始结束时间，减去摘要录音开始时间，得到每段文字对应到音频文件的时间点
            val speechStartTime = thirdLog?.speechStartTime ?: 0L
            val baseTime = if (speechStartTime == 0L) {
                list[0].startTime
            } else {
                speechStartTime
            }
            for (i in list.indices) {
                list[i].startTime -= baseTime
                list[i].endTime -= baseTime
                list[i].showTime = TimeUtils.getFormatTimeExclusiveMill(list[i].startTime, true)
            }

            AppLogger.BASIC.d(TAG, "getThirdLog list size: ${list.size}")
            thirdLogParagraphList = list
            handleMarks()
            withContext(Dispatchers.Main) {
                listener?.onLoadSuccess(
                    list,
                    thirdLogMarkAdapterList,
                    thirdLogMarks,
                    richNoteId
                )
            }
        }
    }

    fun getNewContactName(position: Int): String? {
        return renameContacts[position]
    }

    fun observeSelectedCount(lifecycleOwner: LifecycleOwner, block: (Int) -> Unit) {
        // 监听选中item的数量
        selectedCount.observe(lifecycleOwner) {
            block(it)
        }
    }

    internal fun updateSelectionThirdLogs(position: Int, isRemove: Boolean) {
        /**
         * text中可能含有标记的小旗子，这会导致其与原始数据匹配不上，因此直接用原数据进行匹对
         */
        val size = thirdLog?.thirdLogParagraph?.size ?: -1
        if (position < 0 || position >= size) {
            AppLogger.BASIC.d(TAG, "position is out of range size:$size,position:$position")
            return
        }
        val paragraph = thirdLog?.thirdLogParagraph?.get(position)?.paragraph
        if (isRemove) {
            // 由于RV的缓存机制，先检查集合中pos对应的text是否为当前的text，若是才删除，否则不删除
            paragraph.takeIf { selectionThirdLogs[position] == it }?.let {
                selectionThirdLogs.remove(position)
            }
        } else {
            selectionThirdLogs[position] = paragraph
        }
        selectedCount.value = selectionThirdLogs.size
    }

    internal fun clearSelectionThirdLogs() {
        // 取消全选时，清空selectionThirdLogs
        selectionThirdLogs.clear()
        selectedCount.value = 0
    }

    internal fun fillSelectionThirdLogs() {
        // 全选时，重新填充selectionThirdLogs
        selectionThirdLogs.clear()
        thirdLog?.thirdLogParagraph?.forEachIndexed { index, thirdLogParagraph ->

            selectionThirdLogs[index] = thirdLogParagraph.paragraph
        }
        selectedCount.value = selectionThirdLogs.size
    }

    fun isSelected(position: Int): Boolean {
        return selectionThirdLogs.keys.contains(position)
    }

    private fun updateThirdLog(absolutePath: String, oldLrcAbsolutePath: String, thirdLog: ThirdLog?) {
        AppLogger.BASIC.d(TAG, "updateThirdLog")
        val file = File(absolutePath)
        //还原每段内容的startTime 和 endTime
        if (thirdLog?.thirdLogParagraph?.isEmpty() == true) {
            AppLogger.BASIC.d(TAG, "list is empty")
            kotlin.runCatching {
                File(oldLrcAbsolutePath).delete()
            }
        } else {
            try {
                val list = ArrayList<ThirdLogParagraph>()
                thirdLog?.thirdLogParagraph?.let {
                    it.forEach { item ->
                        list.add(item.copy())
                    }
                }
                val newThirdLog = ThirdLog(
                    thirdLog?.thirdLogId ?: "",
                    thirdLog?.richNoteId ?: richNoteId ?: "",
                    thirdLog?.speechStartTime ?: 0,
                    list, thirdLog?.pics ?: emptyList()
                )
                val speechStartTime = newThirdLog.speechStartTime ?: 0L
                val baseTime = if (speechStartTime == 0L) {
                    list[0].startTime
                } else {
                    speechStartTime
                }
                for (i in list.indices) {
                    list[i].startTime += baseTime
                    list[i].endTime += baseTime
                }
                val result =
                        ThirdLogParser.writerThirdLogToFile(Gson().toJson(newThirdLog), file)
                File(oldLrcAbsolutePath).delete()
                AppLogger.BASIC.d(TAG, "result = $result")
            } catch (e: IllegalStateException) {
                AppLogger.BASIC.e(TAG, " ${e.message}")
            }
        }
    }

    private fun clearMarks(noteWithAttachments: RichNoteWithAttachments) {
        RichNoteRepository.updateSpeechLogMark(noteWithAttachments.richNote.localId, "", updateRichNoteExtra = true)
    }

    private fun clearThirdLog(path: String, oldLrcUri: String) {
        viewModelScope.launch(Dispatchers.IO) {
            runCatching {
                val newLog = thirdLog?.copy(
                    thirdLogParagraph = emptyList(), pics = emptyList()
                )
                val result =
                    ThirdLogParser.writerThirdLogToFile(Gson().toJson(newLog), File(path))
                AppLogger.BASIC.d(
                    TAG,
                    "clearThirdLog new: $result -- ${newLog?.thirdLogParagraph?.size} -- ${newLog?.pics?.size}"
                )
                File(oldLrcUri).delete()
            }.onSuccess {
                AppLogger.BASIC.d(TAG, "clearThirdLog success ${File(lrcUri).length()}")
            }.onFailure {
                AppLogger.BASIC.d(TAG, "clearThirdLog failed:${it.message}")
            }
        }
    }

    private fun updateRenameToThirdLog() {
        // 保存时，将修改的联系人名称应用到ThirdLog中
        thirdLog?.thirdLogParagraph?.let {
            renameContacts.forEach { (pos, name) ->
                val paragraph = it[pos]
                paragraph.name = name
            }
        }
    }

    fun clearRenameContacts() {
        renameContacts.clear()
    }

    fun deleteParagraphContent() {
        val oldThirdLog = thirdLog ?: return
        // 将List<ThirdLogParagraph>转为List<Pair<Int, ThirdLogParagraph>>形式，方便删除，避免报错
        val paragraphs = oldThirdLog.thirdLogParagraph.mapIndexed { index, thirdLogParagraph ->
            Pair(index, thirdLogParagraph)
        }.toMutableList()
        thirdDeleteList.clear()
        selectionThirdLogs.keys.forEach { key ->
            val paragraph = paragraphs.find { it.first == key }
            paragraph?.second?.let {
                thirdDeleteList.add(it)
            }
            // 将通话内容选中的段落删除，并记录图片id
            paragraph?.second?.screenShots?.forEach { img ->
                needDeleteImageAttachmentIds.add(img.attachmentId)
            }

            paragraphs.remove(paragraph)
        }
        // 后续需要刷新列表，清空选中集合，防止选中其他item
        clearSelectionThirdLogs()
        // 由于Gson默认处理对kotlin支持不是很友好，不为空的对象可能解析成空对象
        AppLogger.BASIC.d(TAG, "oldThirdLogId: ${oldThirdLog.thirdLogId}, oldRichNoteId: ${oldThirdLog.richNoteId}")
        // 创建一个新的ThirdLog对象，并赋值给thirdLog
        val newThirdLog = ThirdLog(oldThirdLog.thirdLogId ?: "",
            oldThirdLog.richNoteId ?: richNoteId ?: "",
            oldThirdLog.speechStartTime, paragraphs.map { it.second }, oldThirdLog.pics ?: emptyList()
        )
        thirdLog = newThirdLog
    }

    fun deleteImageAttachmentForParagraph(noteId: String, isFromSmooth: Boolean) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                needDeleteImageAttachmentIds.forEach {
                    RichNoteRepository.deleteAttachment(it)
                }
            }
            // 清空需删除的图片集合
            needDeleteImageAttachmentIds.clear()
            saveParagraphContent(noteId, isFromSmooth, true)
        }
    }

    fun deleteEmptyParagraph() {
        selectionThirdLogs.clear()
        thirdLog?.thirdLogParagraph?.forEachIndexed { index, thirdLogParagraph ->
            if (thirdLogParagraph.paragraph == "" && thirdLogParagraph.screenShots.isNullOrEmpty()) {
                selectionThirdLogs[index] = thirdLogParagraph.paragraph
            }
        }
        if (selectionThirdLogs.size != 0) {
            deleteParagraphContent()
        }
        handleMarks()
    }

    fun saveParagraphContent(noteId: String, isFromSmooth: Boolean, isDelete: Boolean = false, isFromUnSave: Boolean = false) {
        AppLogger.BASIC.d(TAG, "saveParagraphContent isFromSmooth=$isFromSmooth,isFromUnSave=$isFromUnSave")
        if (isFromSmooth) {
            saveSpeechOriginalByNoteId(noteId, isDelete)
        } else {
            saveParagraphContentByNoteId(noteId, isDelete, isFromUnSave)
        }
    }

    /**
     * 保存修改（删除）的段落内容
     * @param isFromUnSave 是否是编辑后未保存的场景
     */
    fun saveParagraphContentByNoteId(noteId: String, isDelete: Boolean = false, isFromUnSave: Boolean = false) {
        AppLogger.BASIC.d(TAG, "saveParagraphContent：isFromUnSave=$isFromUnSave,$noteId")
        val thirdLog = if (isFromUnSave) {
            /*
            页面重建场景下，保存流程还未执行完成，概率出现先执行页面的create，重新读取了thirdLog文件，
            导致修改后的数据被重置为修改前的数据，造成数据丢失。所以此场景先拷贝修改后的数据再用于保存流程
             */
            <EMAIL>?.copy()
        } else {
            <EMAIL>
        }
        updateRenameToThirdLog()
        CoroutineScope(Dispatchers.IO).launch {
            updateComplete.postValueSafe(false)
            val noteWithAttachments = RichNoteRepository.findById(noteId) //笔记内容
            val oldAttachmentId = noteWithAttachments?.getLrcAttachment()?.attachmentId ?: ""
            if (noteWithAttachments != null) {
                //1、创建新的附件、将附件存入附件表数据库中、替换笔记详情和通话记录文件id的关联关系
                val attachment = ThirdLogNoteBuildHelper.createAudioAttachmentsAndUpdate(
                    lrcUri,
                    noteWithAttachments.richNote,
                    Attachment.TYPE_SPEECH_LRC
                )
                val oldLrcUri = lrcUri
                val absolutePath = attachment.absolutePath(MyApplication.appContext)
                lrcUri = absolutePath
                //2、再将old附件id及附件删除
                RichNoteRepository.deleteAttachment(oldAttachmentId)
                noteWithAttachments.attachments?.find { it.type == Attachment.TYPE_SPEECH_LRC }
                    ?.apply {
                        RichNoteRepository.deleteAttachment(this)
                    }

                //3、删除全部通话内容时，将文件置为空，关闭入口
                AppLogger.BASIC.d(TAG, "isDelete: $isDelete, thirdLogCount: $thirdLogCount")
                if (isDelete && thirdLogCount == 0) {
                    clearMarks(noteWithAttachments)
                    clearThirdLog(absolutePath, oldLrcUri)
                    AppLogger.BASIC.d(TAG, "new:${attachment.attachmentId} old:$oldAttachmentId")
                } else {
                    updateThirdLog(absolutePath, oldLrcUri, thirdLog)
                }

                //4、记录修改类型为修改了通话内容
                val modifyTime = System.currentTimeMillis()
                noteWithAttachments.richNote.extra?.updateEditInfos(mapOf(RichNoteEditType.TYPE_DETAIL_LOG_MODIFED.flag to modifyTime))

                //5、更改rich_note的updateTime和modify(需要上云）
                noteWithAttachments.richNote.state = RichNote.STATE_MODIFIED
                noteWithAttachments.richNote.updateTime = modifyTime
                RichNoteRepository.updateWithNoTimestamp(noteWithAttachments.richNote)
                NoteSearchManagerWrapper.notifyDataChange()
                LocalBroadcastManager.getInstance(MyApplication.appContext).sendBroadcast(getBroadCastIntent())
            }
            updateComplete.postValueSafe(true)
        }
    }

    private fun getBroadCastIntent(): Intent {
        val result = Intent(ACTION_EDIT_LRC)
        result.putExtra(ThirdLogDetailActivity.VOICELRC, lrcUri)
        result.putExtra(ThirdLogDetailActivity.SPEECH_ATTACHMENT_ID, speechAttachmentId)
        result.putExtra(ThirdLogDetailActivity.SPEECH_PIC_ATTACHMENT_ID, speechPicAttachmentId)
        result.putExtra(ThirdLogDetailActivity.SPEECH_ATTACHMENT_TYPE, speechAttachmentType)
        result.putExtra(ThirdLogDetailActivity.SPEECH_LRC_ATTACHMENT_ID, speechLrcAttachmentId)
        result.putExtra(ThirdLogDetailActivity.SPEECH_NEW_LRC_ATTACHMENT_ID, speechNewLrcAttachmentId)
        return result
    }

    fun saveSpeechOriginalByNoteId(noteId: String, isDelete: Boolean = false) {
        CoroutineScope(Dispatchers.IO).launch {
            val noteWithAttachments = RichNoteRepository.findById(noteId) //笔记内容
            if (noteWithAttachments == null) {
                AppLogger.BASIC.d(TAG, "saveVoiceOriginalByNoteId noteWithAttachments is null")
                return@launch
            }
            val speechAttachment = noteWithAttachments.getAttachment(speechAttachmentId)
            val speechLrcAttachment = noteWithAttachments.getAttachment(speechLrcAttachmentId)
            val id = UUID.randomUUID().toString()
            val newSpeechLrcAttachment = Attachment(id, noteId, Attachment.TYPE_IDENTIFY_VOICE_LRC, fileName = "$id.json")
            speechNewLrcAttachmentId = newSpeechLrcAttachment.attachmentId
            val context = MyApplication.appContext
            val oldPath = speechLrcAttachment?.absolutePath(context) ?: ""
            val newPath = newSpeechLrcAttachment.absolutePath(context)
            lrcUri = newPath
            if (isDelete && thirdLogCount == 0) {
                clearThirdLog(newPath, oldPath)
            } else {
                updateThirdLog(newPath, speechLrcAttachment?.absolutePath(context) ?: "", <EMAIL>)
            }
            RichNoteRepository.deleteAttachment(speechLrcAttachmentId)
            RichNoteRepository.insertAttachment(newSpeechLrcAttachment)
            val extra = speechAttachment?.extra
            if (extra == null) {
                AppLogger.BASIC.d(TAG, "extra is null")
            } else {
                extra.asrAttachId = newSpeechLrcAttachment.attachmentId
                RichNoteRepository.updateAttachment(speechAttachment)
            }
            //4、记录修改类型为修改了通话内容
            val modifyTime = System.currentTimeMillis()
            noteWithAttachments.richNote.extra?.updateEditInfos(mapOf(RichNoteEditType.TYPE_DETAIL_LOG_MODIFED.flag to modifyTime))

            //5、更改rich_note的updateTime和modify(需要上云）
            noteWithAttachments.richNote.state = RichNote.STATE_MODIFIED
            noteWithAttachments.richNote.updateTime = modifyTime
            RichNoteRepository.updateWithNoTimestamp(noteWithAttachments.richNote)
            NoteSearchManagerWrapper.notifyDataChange()
        }
    }
    fun deleteThirdLogImage(
        attachmentId: String,
        dataPosition: Int,
        detailPosition: Int
    ): Boolean {
        if (thirdLog == null) {
            AppLogger.BASIC.d(TAG, "deleteThirdLogImage thirdLog is null")
            return false
        }
        val listData = thirdLog?.thirdLogParagraph
            ?: ArrayList<ThirdLogParagraph>()

        AppLogger.BASIC.d(TAG, "screenShots list: ${listData[detailPosition].screenShots}")
        if (listData[detailPosition].screenShots != null && listData[detailPosition].screenShots?.isNotEmpty() == true) {
            AppLogger.BASIC.d(TAG, "screenShots list size: ${listData[detailPosition].screenShots?.size}")
            if (attachmentId == listData[detailPosition].screenShots?.get(dataPosition)?.attachmentId) {
                listData[detailPosition].screenShots?.removeAt(dataPosition)
            } else {
                AppLogger.BASIC.d(TAG, "deleteThirdLogImage attachmentId is not same")
                return false
            }
            AppLogger.BASIC.d(TAG, "listScreenShot: ${listData[detailPosition].screenShots?.size}")
        }
        CoroutineScope(Dispatchers.IO).launch {
            RichNoteRepository.deleteAttachment(attachmentId)
        }
        richNoteId?.let { saveParagraphContentByNoteId(it) }

        return true
    }

    /**
     * 内容页面进入页面添加标记
     */
    private fun handleMarks() {
        thirdLogMarkAdapterList.clear()
        thirdLogMarks?.let { thirdLogMarks ->
            thirdLogMarks?.manualMarkList?.forEach { thirdLogManualMark ->
                thirdLogManualMark.relativelyTimestamp =
                    thirdLogManualMark.timestamp - thirdLogMarks.timestamp
                thirdLogManualMark.showTime = TimeUtils.getFormatTimeExclusiveMill(thirdLogManualMark.relativelyTimestamp, true)
                thirdLogParagraphList?.forEach { thirdLogParagraph ->
                    //手动标记的时间和段落的时间相等
                    if (MarkUtils.checkMarkTimeInParagraphTime(
                            thirdLogManualMark,
                            thirdLogParagraph
                        )
                    ) {
                        thirdLogManualMark.content =
                            if ("${thirdLogParagraph.paragraph}".length > Constants.MAX_SPEECH_TITLE_SUB_LENGTH) {
                                thirdLogParagraph.paragraph.substring(
                                    0,
                                    Constants.MAX_SPEECH_TITLE_SUB_LENGTH
                                )
                            } else "${thirdLogParagraph.paragraph}"
                        thirdLogParagraph.hasManualMark = true
                        return@forEach
                    }
                }
                thirdLogMarkAdapterList.add(thirdLogManualMark)
            }

            thirdLogMarks.aiMarkList?.forEach { thirdLogAIMark ->
                thirdLogAIMark.relativelyTimestamp =
                    thirdLogAIMark.timestamp - thirdLogMarks.timestamp
                thirdLogAIMark.showTime = TimeUtils.getFormatTimeExclusiveMill(
                    thirdLogAIMark.relativelyTimestamp,
                    true
                )
                thirdLogMarkAdapterList.add(thirdLogAIMark)
            }

            thirdLogMarks.picMarkList?.forEach { thirdLogPicMark ->
                thirdLogPicMark.relativelyTimestamp =
                    thirdLogPicMark.timestamp - thirdLogMarks.timestamp
                thirdLogPicMark.showTime = TimeUtils.getFormatTimeExclusiveMill(thirdLogPicMark.relativelyTimestamp, true)
                thirdLogMarkAdapterList.add(thirdLogPicMark)
            }

            thirdLogParagraphList?.forEach { thirdLogParagraph ->
                //不存在手动标记
                if (!thirdLogParagraph.hasManualMark) {
                    MarkUtils.addMarks(
                        MyApplication.appContext,
                        thirdLogParagraph,
                        thirdLogParagraph.paragraph,
                        thirdLogMarks,
                        isBlod = true
                    )
                }
            }
        }
    }

    /**
     * 处理标记重命名 删除动作
     */
    fun handeOperateMarkChange(
        thirdLogMarks: ThirdLogMarks?,
        thirdLogMark: ThirdLogMark?,
        isDelete: Boolean,
        callback: () -> Unit
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            if (thirdLogMark is ThirdLogAIMark) {
                //重命名 自动标记不用处理  删除 自动标记需要处理
                if (isDelete) {
                    thirdLogParagraphList?.forEach { thirdLogParagraph ->
                        thirdLogMark.content?.let { content ->
                            if ((thirdLogParagraph.paragraphSpannable != null) &&
                                (thirdLogParagraph.paragraph?.contains(content) == true)
                            ) {
                                MarkUtils.addMarks(
                                    MyApplication.appContext,
                                    thirdLogParagraph,
                                    thirdLogParagraph.paragraph,
                                    thirdLogMarks,
                                    isBlod = true
                                )
                            }
                        }
                    }
                    callback.invoke()
                }
            } else if (thirdLogMark is ThirdLogManualMark) {
                //重命名 手动标记不用处理  删除 手动标记需要处理
                if (isDelete) {
                    thirdLogParagraphList?.forEach { thirdLogParagraph ->
                        if (MarkUtils.checkMarkTimeInParagraphTime(
                                thirdLogMark,
                                thirdLogParagraph
                            )
                        ) {
                            thirdLogParagraph.hasManualMark = false
                            //不存在手动标记  重新判断手动标记是否存在
                            MarkUtils.addMarks(
                                MyApplication.appContext,
                                thirdLogParagraph,
                                thirdLogParagraph.paragraph,
                                thirdLogMarks,
                                isBlod = true
                            )
                            return@forEach
                        }
                    }
                    callback.invoke()
                }
            } else if (thirdLogMark is ThirdLogPicMark) {
                //重命名 图片标记不用处理  删除 图片标记需要处理
                if (isDelete) {
                    thirdLogParagraphList?.forEach { thirdLogParagraph ->
                        if (MarkUtils.checkMarkTimeInParagraphTime(thirdLogMark, thirdLogParagraph)) {
                            thirdLogParagraph.screenShots?.forEach {
                                if (thirdLogMark.id == it.attachmentId) {
                                    AppLogger.BASIC.e(TAG, "isMark:${it.isMark}")
                                    it.isMark = false
                                }
                                return@forEach
                            }
                            return@forEach
                        }
                    }
                    richNoteId?.let { saveParagraphContentByNoteId(it) }
                    callback.invoke()
                }
            }
        }
    }

    fun initFolder(guid: String?, lifecycleOwner: LifecycleOwner, block: (Boolean) -> Unit) {
        guid?.let { id ->
            viewModelScope.launch {
                currentFolder = withContext(Dispatchers.IO) {
                    AppDatabase.getInstance().foldersDao().findFolderByGuid(id)
                }
                currentFolder?.observe(lifecycleOwner) {
                    block(it?.isEncrypted ?: false)
                }
            }
        }
    }

    interface OnLoadThirdLogListener {
        fun onLoadSuccess(
            thirdLogParagraphList: List<ThirdLogParagraph>,
            thirdLogMarkList: MutableList<ThirdLogMark>,
            thirdLogMarks: ThirdLogMarks?,
            richNoteId: String? = null
        )
        fun onLoadError(error: String)
    }

    interface OnUpdateThirdLogListener {
        fun onUpdateResult(result: Boolean)
    }

    fun reName(position: Int, newName: String, all: Boolean) {
        AppLogger.BASIC.d(TAG, "position: $position, newName: $newName, all: $all")
        // 不修改原数据，采用集合保存修改的名称，更新到编辑模式的列表
        thirdLog?.thirdLogParagraph?.let {
            val oldName = originalNames[position]
            if (!all) {
                renameContacts[position] = newName
                originalNames[position] = newName
            } else {
                it.forEachIndexed { index, _ ->
                    if (oldName == originalNames[index]) {
                        renameContacts[index] = newName
                        originalNames[index] = newName
                    }
                }
            }
        }
    }

    /**点击【取消】时还原数据*/
    fun restoreThirdLogData() {
        thirdLog?.thirdLogParagraph?.forEach {
            it.paragraph = it.backupParagraph ?: ""
        }
    }
}