/***********************************************************
 * * Copyright (C), 2019-2027, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: NoteListFragment.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/5/25
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.activity.richedit

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.app.ActivityOptions
import android.app.Dialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.text.TextUtils
import android.text.format.DateFormat
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.view.animation.LinearInterpolator
import android.widget.AbsListView
import android.widget.FrameLayout
import androidx.appcompat.content.res.AppCompatResources
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.content.ContextCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import androidx.vectordrawable.graphics.drawable.VectorDrawableCompat
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.google.android.material.navigation.NavigationBarView
import com.nearme.note.DialogFactory
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.activity.list.ItemClickHelper
import com.nearme.note.activity.list.NoteItemAnimator
import com.nearme.note.activity.list.NoteListUpdateCallback
import com.nearme.note.activity.list.NoteModeSwitcher
import com.nearme.note.activity.list.NoteStaggeredGridLayoutManager
import com.nearme.note.activity.list.NoteViewHolder
import com.nearme.note.activity.richedit.webview.WVNoteViewEditFragment
import com.nearme.note.activity.richlist.NoteSearchAdapterInterface
import com.nearme.note.activity.richlist.OnSelectionChangeListener
import com.nearme.note.activity.richlist.RichNoteDiffCallBack
import com.nearme.note.activity.richlist.RichNoteItem
import com.nearme.note.activity.richlist.RichNoteListAdapter
import com.nearme.note.activity.richlist.RichNoteSearchAdapter
import com.nearme.note.common.Constants
import com.nearme.note.control.list.NoteListHelper
import com.nearme.note.db.NotesProvider
import com.nearme.note.logic.MenuExecutor
import com.nearme.note.logic.MenuExecutor.ExecutorProgressListener
import com.nearme.note.logic.NoteSyncProcess
import com.nearme.note.logic.NoteSyncProcess.CloudSyncStateCallback
import com.nearme.note.logic.NoteSyncProcessProxy
import com.nearme.note.main.ActivitySharedViewModel
import com.nearme.note.main.BaseFragment
import com.nearme.note.main.UIConfigMonitor
import com.nearme.note.setting.SettingPresenter
import com.nearme.note.setting.SettingsActivity
import com.nearme.note.util.AccessibilityUtils
import com.nearme.note.util.CloudSyncTrigger
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.DarkModeUtil
import com.nearme.note.util.DensityHelper
import com.nearme.note.util.DeviceInfoUtils
import com.nearme.note.util.EnvirStateUtils
import com.nearme.note.util.ImageHelper
import com.nearme.note.util.IntentParamsUtil
import com.nearme.note.util.MbaUtils
import com.nearme.note.util.MultiClickFilter
import com.nearme.note.util.PRIVACY_PASSWORD_LIST_MOVE_CODE
import com.nearme.note.util.PRIVACY_PASSWORD_NOTE_CODE
import com.nearme.note.util.PrivacyPolicyHelper
import com.nearme.note.util.SortRule
import com.nearme.note.util.SortRule.SORT_RULE_BY_CREATE_TIME
import com.nearme.note.util.SortRule.readSortRule
import com.nearme.note.util.StatisticsUtils
import com.oplus.note.utils.isPackageDisabled
import com.nearme.note.util.startPrivacyPassword
import com.nearme.note.view.helper.MenuMultiSelectHelper
import com.nearme.note.view.helper.NavigationAnimatorHelper
import com.nearme.note.view.refresh.BounceCallBack
import com.nearme.note.view.refresh.BounceHandler
import com.nearme.note.view.refresh.BounceLayout
import com.nearme.note.view.refresh.DefaultHeader
import com.nearme.note.view.refresh.EventForwardingHelper
import com.nearme.note.view.scalebehavior.PrimaryTitleBehavior
import com.oplus.cloudkit.util.CloudKitSyncStatus
import com.oplus.cloudkit.view.CloudKitSyncGuidManager
import com.oplus.cloudkit.view.SyncGuideManagerWrapper
import com.oplus.note.R
import com.oplus.note.databinding.QuickNoteListFragmentBinding
import com.oplus.note.edgeToEdge.EdgeToEdgeManager
import com.oplus.note.logger.AppLogger
import com.oplus.note.osdk.proxy.OplusActivityManagerProxy
import com.oplus.note.osdk.proxy.OplusFlexibleWindowManagerProxy
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.repo.note.entity.FolderItem
import com.oplus.note.util.encryptnote.EncryptHelper
import com.oplus.note.utils.SharedPreferencesUtil
import com.oplus.note.view.EmptyContentView
import com.oplus.note.view.EmptyContentViewLazyLoader
import com.oplus.note.view.PressAnimView
import com.oplus.richtext.editor.utils.QuickNoteStatisticsUtils
import kotlinx.coroutines.launch
import java.util.Arrays
import java.util.Objects
import kotlin.math.abs

@Suppress("LargeClass")
class QuickNoteListFragment : BaseFragment() {
    companion object {
        const val TAG = "QuickNoteListFragment"
        const val DELETE_ANIMATION_TRANSITION = 1000
        private const val ALPHA_DURATION = 160L
        private const val DELAY_TIME = 100L
        private const val SHOW_SEARCH_HIDE_TIPS_DURAITON = 200L

        fun newInstance(folderName: String?, folderGuid: String?): QuickNoteListFragment {
            var args = Bundle()
            args.putString(NotesProvider.COL_NOTE_FOLDER_GUID, folderGuid)
            args.putString(NotesProvider.COL_NOTE_FOLDER, folderName)
            return QuickNoteListFragment().apply {
                arguments = args
            }
        }
    }

    var twoPane: Boolean = false

    private val noteListViewModel by viewModels<QuickNoteListViewModel>({ requireActivity() })
    private val noteViewModel by viewModels<QuickNoteViewModel>({ requireActivity() })
    private val noteMarginViewModel by viewModels<QuickNoteListMarginViewModel>({ requireActivity() })
    private val sharedViewModel by viewModels<ActivitySharedViewModel>({ requireActivity() })

    private var binding: QuickNoteListFragmentBinding? = null
    private val editMenuStub = lazy { binding?.root?.findViewById<ViewStub>(R.id.note_edit_menu_stub) }
    private val editMenuStubSecondary = lazy { binding?.root?.findViewById<ViewStub>(R.id.note_edit_menu_stub_secondary) }
    private var mLastSearchText: String? = ""
    private var mEmptyContentPageHelper: ImageHelper? = null

    val adapter by lazy {
        RichNoteListAdapter(requireContext(), noteListViewModel.currentFolder.value?.toFolder(), noteListViewModel.selectionManager)
    }

    private val searchAdapter by lazy {
        RichNoteSearchAdapter(requireContext())
    }

    private var noteModeSwitcher: NoteModeSwitcher? = null
    private var currentEncrypGuid: String? = null
    private var layoutManager: StaggeredGridLayoutManager? = null
    private var noteListCountPre = -1
    private var changeSort = false
    private var loadDataFinished = false
    private var supportTitleMarginStart = 0
    private var behavior: PrimaryTitleBehavior? = null
    private var searchItem: MenuItem? = null
    private var onlyMaskAnim = false
    private var isReCreated = false
    private var hasPlayAnimation = false
    private var guideManager: SyncGuideManagerWrapper? = null
    private var emptyContentViewLazyLoader: EmptyContentViewLazyLoader? = null
    private var notePlaceHolderView: View? = null
    private var searchPlaceHolderView: View? = null
    private var isQueryTextCleared = false
    private var isRestoreFlag = false
    private var noteSyncProcess: NoteSyncProcessProxy? = null
    private var preHourFormat = false
    private var isCurrentFolderFirstInit = true
    private var isSelectionModeFirstInit = true
    private var preCheckedGuid = ""
    private var mInZoomWindowState = false
    private var mToolNavigationView: COUINavigationView? = null
    private var mToolNavigationViewSecondary: COUINavigationView? = null
    private var mNavigationAnimatorHelper: NavigationAnimatorHelper? = null
    private var mIsAnimating = false
    private var mSelectItemSize = 0
    private var mSyncEnable = false
    private var mNoteListHelper: NoteListHelper? = null
    private var mIsEncryptOrDecrypt = false
    private var mDialogClickListener: DialogFactory.DialogOnClickListener? = null
    private var mDialogFactory: DialogFactory? = null
    private var localReceiver: LocalReceiver? = null
    private var guidHashMap: HashMap<String, Int>? = null
    private var downY = 0F
    private var moveY = 0F
    private var mCallBack: BounceCallBack? = null
    private var isGridMode = false
    private var isSwitchGrideModing = false
    private var mDeleteDialog: Dialog? = null
    private var cloudSyncState: Int = -1

    private var mIsFirstLoadNoteList = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mInZoomWindowState = sharedViewModel.inZoomWindowState
        supportTitleMarginStart = resources.getDimensionPixelOffset(R.dimen.toolbar_support_title_margin_start)
        initNoteListHelper()
        preHourFormat = DateFormat.is24HourFormat(context)
        guidHashMap = HashMap()
        localReceiver = LocalReceiver()
        val intentFilter = IntentFilter(Constants.ACTION_SAVE_NOTE_COMPLETE)
        intentFilter.addAction(Constants.ACTION_SAVE_NOTE_FINISHED)
        intentFilter.addAction(Constants.ACTION_SAVE_PICTURE_COMPLETE)
        intentFilter.addAction(Constants.ACTION_DOWNLOAD_SKIN_COMPLETE)
        LocalBroadcastManager.getInstance(requireContext())
                .registerReceiver(localReceiver!!, intentFilter)
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        arguments?.apply {
            var folderGuid = this.getString(NotesProvider.COL_NOTE_FOLDER_GUID, null)
            var folderName = this.getString(NotesProvider.COL_NOTE_FOLDER, null)
            noteListViewModel.currentFolder.value?.apply {
                guid = folderGuid
                name = folderName
            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = QuickNoteListFragmentBinding.inflate(inflater, container, false)
        binding?.lifecycleOwner = this
        binding?.viewModel = noteMarginViewModel
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        AppLogger.BASIC.d(TAG, "----onViewCreated----")
        initiateWindowInsets()

        if (savedInstanceState != null) {
            isReCreated = true
        }

        initiateToolbar()
        initiateNoteItemListView(savedInstanceState)
        initBehavior()
        initiateSearchViewAdapter()
        initRefreshView()
        initiateEmptyPage()

        initiateObservers()
        initRefreshAndPermissionObserver()
    }

    private fun initiateWindowInsets() {
        EdgeToEdgeManager.observeOnApplyWindowInsets(binding?.root) { v, insets ->
            val systemBarInsets = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            val stableStatusBarInsets = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.statusBars())
            val top = stableStatusBarInsets.top
            v.updatePadding(
                left = systemBarInsets.left,
                right = if (twoPane) 0 else systemBarInsets.right,
                top = top,
                bottom = systemBarInsets.bottom
            )
            behavior?.setSystemBarInsetsTop(top)
            binding?.root?.visibility = View.VISIBLE
            binding?.statusView?.updateLayoutParams<CoordinatorLayout.LayoutParams> {
                topMargin = -top
                height = top
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mNoteListHelper?.onResume()
        resetHourFormat()

        if (sharedViewModel.isRecentDeleteFolder.value == true) {
            if (noteListViewModel.richNoteItemList.value?.isEmpty() == true) { //最近删除为空
                mToolNavigationView?.visibility = View.GONE
                mToolNavigationViewSecondary?.visibility = View.GONE
            } else { //最近删除不为空
                mToolNavigationView?.visibility = if (sharedViewModel.isSearch.value == true) View.GONE else View.VISIBLE
            }
        }
    }

    private fun resetHourFormat() {
        val currentHourFormat = DateFormat.is24HourFormat(context)
        if (preHourFormat != currentHourFormat) {
            preHourFormat = currentHourFormat

            if (sharedViewModel.isSearch.value == true) {
                searchAdapter.notifyDataSetChangedDelegate()
            } else {
                adapter.notifyDataSetChangedDelegate()
            }
        }
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        sharedViewModel.isSearch.value = false
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            NoteListHelper.REQUEST_CODE_PASSWORD -> {
                if (null != mNoteListHelper) {
                    if (resultCode == Activity.RESULT_OK) {
                        val noteData: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotes.value
                        if (noteData?.first != null) {
                            val selectedNotes: Set<String> = HashSet(noteData.first)
                            mNoteListHelper!!.noteListEdit(DialogFactory.TYPE_DIALOG_DELETE_CONFIRM,
                                    noteListViewModel.currentFolder.value, selectedNotes, isAllNoteSelected(), false)
                        }
                    } else {
                        noteListViewModel.isDeletingOrRecovering = false
                        AppLogger.BASIC.e(TAG, "delete all note but verify password failed!")
                    }
                }
            }
            PRIVACY_PASSWORD_LIST_MOVE_CODE -> {
                if (resultCode == Activity.RESULT_OK) {
                    mIsEncryptOrDecrypt = true
                    val noteData: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotes.value
                    if (noteData?.first != null) {
                        val selectedNotes: Set<String> = HashSet(noteData.first)
                        mNoteListHelper?.setEncryptNoteData(noteListViewModel.currentFolder.value, selectedNotes)
                    }
                    mNoteListHelper?.startMoveExecutor(isAllNoteSelected())
                }
            }
            PRIVACY_PASSWORD_NOTE_CODE -> {
                if (resultCode == Activity.RESULT_OK) {
                    currentEncrypGuid = noteListViewModel.currentGuid
                    openEncryptedNote()
                }
            }
        }
    }

    override fun onMultiWindowModeChanged(isInMultiWindowMode: Boolean) {
        AppLogger.BASIC.d(TAG, "folder onMultiWindowModeChanged")
        binding?.mainTitle?.postDelayed({
            behavior?.updateToolbar()
        }, DELAY_TIME)

        resetMainEmptyPage()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        AppLogger.BASIC.d(TAG, "----onConfigurationChanged----")
        updateRecyclerViewPadding()
        super.onConfigurationChanged(newConfig)
        val tempFlag = OplusFlexibleWindowManagerProxy.isInFreeFormMode(activity)
        if (tempFlag != mInZoomWindowState) {
            mInZoomWindowState = tempFlag

            if (sharedViewModel.isSearch.value != true) {
                behavior?.updateToolbar()
            }
        }

        mDialogFactory?.onConfigurationChanged(newConfig)
    }

    override fun backToTop() {
        if (sharedViewModel.isSearch.value == true) {
            binding?.searchPage?.resultList?.smoothScrollToPosition(0)
        } else {
            binding?.noteList?.stopScroll()
            binding?.noteList?.smoothScrollToPosition(0)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        guideManager?.release()
    }

    override fun onDestroy() {
        super.onDestroy()
        AppLogger.BASIC.d(TAG, "----onDestroy----")
        if (mDialogFactory != null) {
            mDialogFactory!!.onDestory()
            mDialogFactory = null
        }
        mNoteListHelper?.onBack()
        mNoteListHelper?.onDestroy()
        noteSyncProcess?.release()
        if (localReceiver != null) {
            LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(localReceiver!!)
        }
    }

    private fun initiateToolbar() {
        binding?.toolbar?.inflateMenu(R.menu.quick_menu_note_list)
        binding?.toolbar?.isTitleCenterStyle = false
        binding?.toolbar?.title = getString(R.string.quick_note)
        updateToolbarMenuBySortRule(readSortRule())
        binding?.toolbar?.setOnMenuItemClickListener { menu ->
            when (menu.itemId) {
                R.id.select_all -> {
                    val isSelectAll = getString(R.string.select_all).contentEquals(menu.title)

                    if (isSelectAll) {
                        adapter.selectAll()
                        refreshCheckBox(MenuMultiSelectHelper.MenuMode.SELECT_ALL)
                    } else {
                        adapter.deSelectAll()
                        refreshCheckBox(MenuMultiSelectHelper.MenuMode.DE_SELECT_AL)
                    }
                }
                R.id.cancel -> {
                    sharedViewModel.noteSelectionMode.setValue(false)
                }
                R.id.menu_add_quick -> {
                    val intent = Intent(activity, QuickNoteViewRichEditActivity::class.java).let {
                        it.putExtra(NotesProvider.COL_NOTE_FOLDER, resources.getString(R.string.quick_note))
                        it.putExtra(NotesProvider.COL_NOTE_FOLDER_GUID, FolderInfo.FOLDER_GUID_QUICK)
                        it.putExtra(WVNoteViewEditFragment.EXTRA_CREATE_NEW_NOTE, true)
                    }
                    activity?.startActivity(intent)
                    activity?.overridePendingTransition(-1,
                        R.anim.quick_activity_show_out)
                }
                R.id.edit_note -> {

                    if (sharedViewModel.isSearch.value == true) {
                        AppLogger.BASIC.d(TAG, "onMenuItemClick, in search mode")
                        return@setOnMenuItemClickListener false
                    }
                    sharedViewModel.noteSelectionMode.setValue(true)
                }
                R.id.mode_note -> {
                    context?.let { changeMode(it, menu) }
                }
                R.id.jump_setting -> {
                    context?.let {
                        if (EnvirStateUtils.getComponentState(it, SettingsActivity::class.java)) {
                            val intent = Intent(it, SettingsActivity::class.java)
                            intent.putExtra(SettingsActivity.NOTE_SETTING_TITLE, getString(com.oplus.note.baseres.R.string.setting))
                            startActivity(intent)
                            val sliderEnter = com.support.appcompat.R.anim.coui_open_slide_enter
                            val sliderExit = com.support.appcompat.R.anim.coui_open_slide_exit
                            activity?.overridePendingTransition(sliderEnter, sliderExit)
                            // click setting buried point
                            StatisticsUtils.setEventSettingOpenCount(it)
                        }
                    }
                }
                R.id.sort_rule -> {
                    if (!isSwitchGrideModing) {
                        switchAdapterSortRule()
                    }
                }
                R.id.note_searchView -> {
                    onSearchViewClick()
                }
            }
            return@setOnMenuItemClickListener true
        }
        val isTextDark = !DarkModeUtil.isDarkMode(context)
        val fontColor = if (isTextDark) Color.BLACK else Color.WHITE
        val resId = com.support.appcompat.R.drawable.coui_menu_ic_cancel
        binding?.toolbar?.setNavigationOnClickListener() {
            activity?.let {
                it.finish()
            }
        }
        AppCompatResources.getDrawable(requireContext(), resId)?.apply {
            isAutoMirrored = true
            setTint(fontColor)
            layoutDirection = resources.configuration.layoutDirection
            binding?.toolbar?.navigationIcon = this
        }
        val addQuickNote = binding?.toolbar?.menu?.findItem(R.id.menu_add_quick)
        addQuickNote?.let {
            VectorDrawableCompat.create(resources, R.drawable.ic_oplus_add, activity?.theme)
                ?.apply {
                    setTint(fontColor)
                    it.icon = this
                }
        }
    }

    private fun changeMode(context: Context, menu: MenuItem) {
        val isGrid = sharedViewModel.isGridMode()
        val switchToGrid = !isGrid
        if (switchToGrid) {
            SharedPreferencesUtil.getInstance().putInt(context, SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                    SharedPreferencesUtil.HOME_PAGE_MODE_KEY, SharedPreferencesUtil.HOME_PAGE_MODE_GRID_MODE)
        } else {
            SharedPreferencesUtil.getInstance().putInt(context, SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                    SharedPreferencesUtil.HOME_PAGE_MODE_KEY, SharedPreferencesUtil.HOME_PAGE_MODE_LIST_MODE)
        }

        sharedViewModel.noteMode.value = switchToGrid
        menu.setTitle(if (switchToGrid) R.string.note_list_mode else R.string.note_grid_mode)

        CloudSyncTrigger.sendDataChangedBroadcast(context)
        StatisticsUtils.setEventSwitchNote(context, if (switchToGrid) StatisticsUtils.TYPE_SWITCH_NOTE_LIST else StatisticsUtils.TYPE_SWITCH_NOTE_GRID)
    }

    private fun initiateNoteItemListView(savedInstanceState: Bundle?) {
        isGridMode = false  //sharedViewModel.isGridMode()

        noteModeSwitcher = NoteModeSwitcher(binding?.noteList)
        updateRecyclerViewPadding()
        updateToolbarMenuByMode(isGridMode)

        // 1.set recyclerview layout manager
        layoutManager = object : NoteStaggeredGridLayoutManager(RichNoteListAdapter.LIST_SPAN_COUNT, VERTICAL) {
            override fun canScrollVertically(): Boolean {
                return (binding?.refresh?.isRefreshing() == false) && super.canScrollVertically()
            }
        }
        val spanCount = if (isGridMode) RichNoteListAdapter.getGridSpanCount() else RichNoteListAdapter.LIST_SPAN_COUNT
        layoutManager?.spanCount = spanCount
        binding?.noteList?.layoutManager = layoutManager

        // 2.set recyclerview item animator
        val itemAnimator = NoteItemAnimator()
        binding?.noteList?.itemAnimator = itemAnimator
        itemAnimator.setAnimatorListener {
            val hasNotes = adapter.getNoteItemCount() > 0
            if (behavior?.getScaleEnable() == true || !hasNotes) {
                behavior?.updateToolbar()
            }
        }

        noteMarginViewModel.mNotePlaceHolderViewHeight.observe(viewLifecycleOwner) { height: Int ->
            adapter.updatePlaceHolderViewHeight(height)
        }
        noteMarginViewModel.setFolderHeaderHeight(0)
        val notePlaceHolderViewHeight: Int = noteMarginViewModel.mMarginTopDefault +
                resources.getDimensionPixelOffset(R.dimen.note_list_padding_top)
        notePlaceHolderView = View(context)
        notePlaceHolderView?.apply {
            layoutParams = AbsListView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, notePlaceHolderViewHeight)
            visibility = View.INVISIBLE
        }
        adapter.initPlaceHolder(notePlaceHolderView!!)

        // 4.set recyclerview adapter
        adapter.setAdapterMode(if (isGridMode) RichNoteListAdapter.ADAPTER_MODE_GRID else RichNoteListAdapter.ADAPTER_MODE_LIST)
        adapter.setSelectionChangeListener(object : OnSelectionChangeListener {
            override fun onSelectionChange(count: Int) {
                notifySelectionChange()
            }
        })
        if (twoPane && (savedInstanceState != null) && (noteListViewModel.checkedGuid.isNotEmpty())) {
            adapter.setCheckedGuid(noteListViewModel.checkedGuid)
            preCheckedGuid = noteListViewModel.checkedGuid
        }
        adapter.setSortRule(readSortRule())
        binding?.noteList?.adapter = adapter

        // 5.set recyclerview item click listener
        binding?.noteList?.addOnItemTouchListener(object : ItemClickHelper({ position ->
            !adapter.isHeaderView(position)
        }) {
            override fun onItemClick(adapter: RecyclerView.Adapter<*>?, view: View, position: Int) {
                super.onItemClick(adapter, view, position)
                if (adapter !is RichNoteListAdapter) {
                    return
                }
                if (!adapter.isHeaderView(position)) {
                    val viewHolder = binding?.noteList?.findViewHolderForLayoutPosition(position)
                    if (viewHolder is NoteViewHolder) {
                        val guid = adapter.getClickItemGuid(position)
                        if (TextUtils.isEmpty(guid)) {
                            AppLogger.BASIC.d(TAG, "onItemClick guid is empty")
                            return
                        }
                        if (adapter.inSelectionMode()) {
                            adapter.onItemClick(position, viewHolder)
                            notifySelectionChange()
                        } else {
                            if (MultiClickFilter.isEffectiveClick(view)) {
                                val isEncryptedNote = FolderInfo.FOLDER_GUID_ENCRYPTED == viewHolder.folderGuid
                                val isEncryptedFolder = FolderInfo.FOLDER_GUID_ENCRYPTED == adapter.getCurrentFolder()?.guid
                                if (isEncryptedNote && !isEncryptedFolder) {
                                    currentEncrypGuid = guid
                                    noteListViewModel.currentGuid = guid
                                    activity?.let {
                                        EncryptHelper.hasSettingsPassword(it, viewLifecycleOwner) { hasSettingsPassword ->
                                            startPrivacyPassword(it, PRIVACY_PASSWORD_NOTE_CODE, hasSettingsPassword)
                                        }
                                    }
                                } else {
                                    updateCheckedInfo(viewHolder.mGuid, position)
                                    openNote(guid, viewHolder)
                                }
                            }
                        }
                    } else {
                        AppLogger.BASIC.e(TAG, "Invalid holder, position is$position")
                    }
                }
            }
        })
    }

    private fun initBehavior() {
        val params = binding?.appBar?.layoutParams as? CoordinatorLayout.LayoutParams
        behavior = params?.behavior as? PrimaryTitleBehavior
        behavior?.setIsNoteFragment(true)
    }

    private fun initRefreshView() {
        binding?.refresh?.apply {
            setRefreshEnable(false)
            setBounceHandler(BounceHandler(), getChildAt(0))
            setEventForwardingHelper(object : EventForwardingHelper {
                override fun notForwarding(
                        downX: Float,
                        downY: Float,
                        moveX: Float,
                        moveY: Float
                ): Boolean {
                    return downY < moveY
                }
            })
            setHeaderView(DefaultHeader(context), binding?.headerRoot)
            mCallBack = setBounceCallBack(object : BounceCallBack {
                override fun startLoadingMore() {
                    //do nothing
                }

                override fun startRefresh() {
                    // save detail data first,use -1 for distinct with menu id.
                    noteViewModel.notifyDetailSaveData.value = -1
                }

                override fun refreshCompleted() {
                    correctSearchViewState()
                }

                override fun touchEventCallBack(ev: MotionEvent) {
                    when (ev.actionMasked) {
                        MotionEvent.ACTION_DOWN, MotionEvent.ACTION_POINTER_DOWN -> {
                            downY = ev.y
                        }
                        MotionEvent.ACTION_MOVE -> {
                            moveY = ev.y
                            if (abs(moveY - downY) > 50F) {
                                if (sharedViewModel.isPullingDown.value != true) {
                                    AppLogger.BASIC.d(TAG, "set isPullingDown true")
                                    sharedViewModel.isPullingDown.value = true
                                }
                            }
                        }
                        MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL, MotionEvent.ACTION_POINTER_UP -> {
                            sharedViewModel.isPullingDown.value = false
                        }
                    }
                }
            })
            val headerViewHeight = resources.getDimensionPixelSize(R.dimen.default_height)
            val headerTopPadding = resources.getDimensionPixelOffset(R.dimen.pull_refresh_head_top_padding)
            val headerBottomPadding = resources.getDimensionPixelOffset(R.dimen.pull_refresh_head_bottom_padding)
            val listPadding = resources.getDimensionPixelOffset(R.dimen.note_list_padding_top)
            val listMaxTop =
                    resources.getDimensionPixelOffset(R.dimen.pull_refresh_down_fragment_max_drag_distance) + headerBottomPadding
            /* In different layouts, the loading animation (LottieView) is hidden (hidden by other views) by setting
               the topMargin of the root layout, and is 24dp above the listView or recyclerView, This value is up to
               the business side according to the needs. */
            val headerTopMargin = headerViewHeight + headerTopPadding + listPadding
            (binding?.headerRoot?.layoutParams as FrameLayout.LayoutParams?)?.topMargin =
                    -headerTopMargin - headerBottomPadding
            /* Drag threshold. When the drag distance is greater than or equal to this value, it means that it will enter
               the loading state after letting go. The reason for this calculation is to make the loading animation (LottieView)
               24dp from the top (the View that covered it before), and also 24dp from the listView or recyclerView below
               (including the topPadding of the listView), so the specific value is up to the business party according to the
               needs up to you. */
            mDragDistanceThreshold =
                    headerViewHeight + headerBottomPadding + headerTopPadding + headerBottomPadding
            /* The maximum distance that bounceLayout can be dragged down. */
            mMaxDragDistance = listMaxTop
        }
        val callback = if (!ConfigUtils.isUseCloudKit) {
            null
        } else {
            object : CloudKitSyncGuidManager.OnSyncFinishCallback {
                override fun onSyncing() {
                }

                override fun onSyncFinish(syncStatus: CloudKitSyncStatus) {
                    noteListViewModel.completeRefreshWithTipsAndDelay.value = Pair(null, BounceLayout.MSG_REFRESH_DELAY)
                }

                override fun onSyncFinishSubtitleChange() {
                }
            }
        }

        if (callback != null) {
            guideManager = SyncGuideManagerWrapper(this, null, true, null, callback)
        }
        if (context != null && isPackageDisabled(MbaUtils.PACKAGER_CLOUD, requireContext())) {
            noteListViewModel.syncEnable.value = false
        }
        noteSyncProcess = NoteSyncProcessProxy(NoteSyncProcess(activity, object : CloudSyncStateCallback {
            override fun refreshViewState(syncState: Int) {
                AppLogger.BASIC.d(TAG, "refreshViewState canSyncToCloud = $syncState")
                val canSyncToCloud = syncState > NoteSyncProcess.NOTE_SETTING_CLOUD_SYNC_CLOSE
                try {
                    if (isPackageDisabled(MbaUtils.PACKAGER_CLOUD, requireContext())
                            || !DeviceInfoUtils.isAppInstalled(requireContext(), MbaUtils.PACKAGER_CLOUD)) {
                        noteListViewModel.syncEnable.value = false
                    } else if (cloudSyncState != syncState) {
                        noteListViewModel.syncEnable.value = false
                    }
                    cloudSyncState = syncState
                } catch (e: Exception) {
                    return
                }
            }

            override fun refreshModuleState(isSupport: Boolean) {}
        }))
        noteSyncProcess?.checkSyncSwitchStateTask()
        noteSyncProcess?.registerStateReceiver()
    }

    private fun initiateSearchViewAdapter() {
        searchPlaceHolderView = View(context).apply {
            layoutParams = AbsListView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 0)
            visibility = View.INVISIBLE
        }

        searchAdapter.addPlaceHolderView(searchPlaceHolderView!!)
        binding?.searchPage?.resultList?.apply {
            adapter = searchAdapter
            layoutManager = LinearLayoutManager(context)
            addOnItemTouchListener(object : ItemClickHelper() {
                override fun onItemClick(adapter: RecyclerView.Adapter<*>?, view: View, position: Int) {
                    if (adapter !is NoteSearchAdapterInterface<*>) {
                        return
                    }
                    val targetAdapter = adapter as NoteSearchAdapterInterface<*>
                    if (!targetAdapter.isHeaderView(position)) {
                        val holder = binding?.searchPage?.resultList?.findViewHolderForLayoutPosition(position) as? NoteViewHolder
                        if (holder == null) {
                            AppLogger.BASIC.e(TAG, "Invalid holder, position is$position")
                            return
                        }
                        val guid = targetAdapter.getClickItemGuid(position)
                        if (MultiClickFilter.isEffectiveClick(view) && !TextUtils.isEmpty(guid)) {
                            updateCheckedInfo(guid, getPositionByGuid(guid))
                            openNote(guid, null)
                        }
                    }
                }
            })
        }

        binding?.searchPage?.noSearchResultLottie?.setAnimation(R.raw.no_search_results)
    }

    private fun initiateEmptyPage() {
        mEmptyContentPageHelper = ImageHelper(requireActivity())
        val stub: ViewStub? = binding?.emptyContentStub?.viewStub
        if (stub != null) {
            emptyContentViewLazyLoader = EmptyContentViewLazyLoader(stub, object : EmptyContentView.EmptyPageClickListener {
                override fun onSwitch() {
                    if (context != null && isPackageDisabled(MbaUtils.PACKAGER_CLOUD, requireContext())) {
                        MbaUtils.showMbaCloudDialog(requireContext())
                    } else {
                        NoteSyncProcess.startCloudSettingActivity(context)
                    }
                }
            })
        }
        mEmptyContentPageHelper?.let { emptyContentViewLazyLoader?.init(it) }
    }

    private fun initiateEmptyPageIfNeeded(hasNotes: Boolean) {
        val isPrivacyDenied = (context == null) || !PrivacyPolicyHelper.isAgreeUserNotice(context)
        if (!hasNotes || isPrivacyDenied) {
            emptyContentViewLazyLoader?.initialize()
        }
    }

    private fun onSearchViewClick() {
    }

    private fun openNote(guid: String, viewHolder: NoteViewHolder?) {
        AppLogger.BASIC.d(TAG, "openNote:$guid , twoPane:$twoPane")
        val note = noteListViewModel.richNoteItemList.value?.find { it.data?.richNote?.localId == guid }?.data
        if (note != null) {
            var searchText = noteListViewModel.searchText.getValue()
            if (sharedViewModel.isSearch.value == false) {
                searchText = ""
            }

            var folder = noteListViewModel.folders.value?.find { it.guid == note.richNote.folderGuid }
            if (note.richNote.folderGuid != FolderInfo.FOLDER_GUID_ENCRYPTED
                    && folder == null && noteListViewModel.folders.value?.isNotEmpty() == true) {
                folder = noteListViewModel.folders.value!![0]
            }

            if (folder != null) {
                if (twoPane) {
                    AppLogger.BASIC.d(TAG, "openNote searchText.length: ${searchText?.length} searchText: $searchText")
                    if (noteViewModel.selectedNoteWithFolder.value?.first?.richNote?.localId != note.richNote.localId
                            || !Objects.equals(searchText, mLastSearchText)) {
                        noteViewModel.selectedNoteWithFolder.value = Triple(note, folder, searchText)
                    }
                } else {
                    val bundle: Bundle? = viewHolder?.let {
                        val transitionActivityOptions: ActivityOptions =
                            ActivityOptions.makeSceneTransitionAnimation(
                                activity,
                                it.itemView.findViewById(R.id.note_item),
                                getString(R.string.transition_name_item)
                            )
                        transitionActivityOptions.toBundle()
                    }
                    val intent = QuickNoteViewRichEditActivity.createIntent(requireContext(),
                            note,
                            folder.guid,
                            folder.name)
                    AppLogger.BASIC.d(TAG, "openNote searchText.length: ${searchText?.length} searchText: $searchText")
                    intent.putExtra(QuickNoteViewRichEditActivity.EXTRA_SEARCH_TEXT, searchText)
                    intent.putExtra(WVNoteViewEditFragment.EXTRA_OPEN_NOTE, true)
                    if (binding?.noteList?.itemAnimator?.isRunning == false && !isInMultiWindowMode()) {
                        activity?.setResult(Activity.RESULT_OK)
                        startActivity(intent, bundle)
                        activity?.finish()
                    } else {
                        startActivity(intent)
                        activity?.overridePendingTransition(R.anim.anim_editpage_fade_in,
                                R.anim.anim_editpage_fade_out)
                    }
                }
            }

            mLastSearchText = searchText
            QuickNoteStatisticsUtils.setEventOpenNote(appContext)
        }
    }

    private fun openEncryptedNote() {
        if (currentEncrypGuid?.isNotEmpty() == true) {
            updateCheckedInfo(currentEncrypGuid!!, getPositionByGuid(currentEncrypGuid!!))
            openNote(currentEncrypGuid!!, null)
        }
    }

    private fun initiateObservers() {
        noteListViewModel.folders.observe(viewLifecycleOwner) { folders ->
            AppLogger.BASIC.d(TAG, "folder  observe noteListViewModel.folders")

            if (folders.isNotEmpty()) {
                val currentFolder: FolderInfo? = noteListViewModel.currentFolder.value
                if (currentFolder == null) {
                    val newFolder = folders[0].parseToFolderInfo(context, folders[0])
                    noteListViewModel.currentFolder.value = newFolder
                }
            }
        }

        noteListViewModel.currentFolder.observe(viewLifecycleOwner) { newFolder: FolderInfo ->
            AppLogger.BASIC.d(TAG, "folder  observe noteListViewModel.currentFolder")
            if (!isCurrentFolderFirstInit) {
                updateTitle()
                // correct toolbar menu
                correctToolbarMenu()
            }

            isCurrentFolderFirstInit = false
            val currentFolder: Folder? = adapter.getCurrentFolder()
            val isRecentDeleteFolder = FolderInfo.FOLDER_GUID_RECENT_DELETE == newFolder.guid
            val isFolderGuidChanged = currentFolder == null || newFolder.guid != currentFolder.guid
            val isFolderNameChanged = currentFolder == null || newFolder.name != currentFolder.name

            if (isFolderGuidChanged || isFolderNameChanged) {
                // 1.reload note list by given folder.
                AppLogger.BASIC.i(TAG, "folder has changed.")
                adapter.setCurrentFolder(newFolder.toFolder())
                resetCheckedInfo()
            }

            // correct navigationview menu
            if (isRecentDeleteFolder) {
                adapter.setCurrentFolder(newFolder.toFolder())
                if (mToolNavigationView == null || mToolNavigationViewSecondary == null) {
                    initToolNavigationMenu()
                }
                mToolNavigationView!!.inflateMenu(R.menu.menu_note_all_delete)
                mToolNavigationViewSecondary!!.inflateMenu(R.menu.menu_note_delete_list_edit)
                // 显示时， 需区分最近删除界面 是否为可编辑状态
                AppLogger.BASIC.i(TAG, "NavigationAnimatorHelper currentFolder.observe: " + isEditMode())
                mNavigationAnimatorHelper?.showToolNavigationWithoutAnim(isEditMode())
                sharedViewModel.isRecentDeleteFolder.value = true
            } else {
                sharedViewModel.isRecentDeleteFolder.value = false
                if (!isEditMode()) {
                    mNavigationAnimatorHelper?.hideToolNavigationWithoutAnim()
                }
            }

            layoutManager?.scrollToPositionWithOffset(0, 0)
            resetMainEmptyPage()
        }


        noteListViewModel.sortRule.observe(viewLifecycleOwner) { sortRule: Int? ->
            noteViewModel.sortRuleChanged.value = sortRule
            AppLogger.BASIC.d(TAG, "sortRule.observe( sortRule : $sortRule )")
            updateToolbarMenuBySortRule(sortRule)
        }

        noteListViewModel.richNoteItemList.observe(viewLifecycleOwner) { noteItems ->
            if (noteItems == null) {
                AppLogger.BASIC.e(TAG, "noteItems is null")
                return@observe
            }
            //修复bug 363281 ,在退出账号保留数据时，下次登录会重复触发mNoteItemList的observe，因为数据库自动同步有数据更新，此时
            //如果当前文件夹0条数据，会重复resetEmptyView，导致空页面动画重复执行，所以为了避免重复observe，在这里判断如果上次和这次
            //的笔记数量都是0，直接返回
            if (noteItems.isEmpty() && noteListCountPre == 0) {
                lifecycleScope.launch {
                    adapter.refreshInfoBoardAndFolderHeaderAndQuestionnaire(false)
                }
                changeSort = false
                if (FolderInfo.FOLDER_GUID_RECENT_DELETE == noteListViewModel.currentFolder.value?.guid && !isEditMode()) {
                    mToolNavigationView?.visibility = View.GONE
                    mToolNavigationViewSecondary?.visibility = View.GONE
                }
                return@observe
            }

            noteListCountPre = noteItems.size
            val toAdapterList = mutableListOf(*noteItems.toTypedArray())
            AppLogger.BASIC.d(TAG, "richNoteItemList changed noteItems size=${toAdapterList.size}, changeSort=$changeSort toAdapterList:${toAdapterList.size}")
            loadDataFinished = true

            var isCheckedItemExist = false
            var countNotes = 0
            if (toAdapterList.isNotEmpty()) {
                for (i in toAdapterList.indices) {
                    val noteItem = toAdapterList[i]
                    if (noteItem.viewType == RichNoteItem.TYPE_NOTE) {
                        countNotes++
                        val guid = noteItem.data?.richNote?.localId
                        if (guid != null) {
                            guidHashMap?.put(guid, i + adapter.getHeaderCount())
                        }

                        if (twoPane) {
                            //父级数据有变化,需要更新子级页面
                            if (guid == noteListViewModel.checkedGuid) {
                                isCheckedItemExist = true
                                val selectNote = noteViewModel.selectedNoteWithFolder.value?.first
                                val isEncryptedNoteFolder = FolderInfo.FOLDER_GUID_ENCRYPTED == noteListViewModel.currentFolder.value?.guid

                                if (isEncryptedNoteFolder) {
                                    if (noteItem.data.richNote != selectNote?.richNote) {
                                        val folderItem = FolderItem()
                                            .apply {
                                            this.guid = noteListViewModel.currentFolder.value?.guid ?: FolderInfo.FOLDER_GUID_NO_GUID
                                            this.name = noteListViewModel.currentFolder.value?.name ?: appContext.resources.getString(R.string.memo_all_notes)
                                        }
                                        noteViewModel.noteDataChanged.value = Pair(noteItem.data, folderItem)
                                    }
                                } else {
                                    val folder = noteListViewModel.folders.value?.find { it.guid == noteItem.data.richNote.folderGuid }
                                    val selectFolder = noteViewModel.selectedNoteWithFolder.value?.second

                                    if (folder != null && (noteItem.data.richNote != selectNote?.richNote || folder != selectFolder)) {
                                        //判断只有skinId不相同的时候，父级不更新子级
                                        if (noteItem.data.richNote.skinId != selectNote?.richNote?.skinId
                                            && noteItem.data.richNote.isSameRichNoteExcludeSkinId(selectNote?.richNote)) {
                                            return@observe
                                        }
                                        noteViewModel.noteDataChanged.value = Pair(noteItem.data, folder)
                                    }
                                }

                                val toppedTime = noteItem.data.richNote.topTime
                                noteListViewModel.selectionManager.updateTopped(guid, toppedTime)
                            }
                        }
                    }
                }

                AppLogger.BASIC.d(TAG, "isCheckedItemExist=$isCheckedItemExist,checkedGuid=${noteListViewModel.checkedGuid}")
                if (twoPane) {
                    //选中的笔记不在了(如删除,移动文件夹,加密等),更新子级页面为空页面
                    if (noteListViewModel.checkedGuid.isNotEmpty() && !isCheckedItemExist) {
                        resetCheckedInfo()
                    }
                }
            } else {
                resetCheckedInfo()
            }

            noteViewModel.noteCount.value = countNotes

            if (FolderInfo.FOLDER_GUID_RECENT_DELETE == noteListViewModel.currentFolder.value?.guid && !isEditMode()) {
                if (countNotes == 0) {
                    mToolNavigationView?.visibility = View.GONE
                    mToolNavigationViewSecondary?.visibility = View.GONE
                } else if (sharedViewModel.isSearch.value != true) {
                    mToolNavigationView?.visibility = View.VISIBLE
                }
            }

            if (changeSort) {
                noteModeSwitcher?.beginSwitchSortDelayedLayoutAnimation(noteListViewModel.sortRule.value ?: SORT_RULE_BY_CREATE_TIME) {
                    adapter.setNoteItems(toAdapterList, true)
                    val hasNotes = adapter.getNoteItemCount() != 0
                    correctSearchViewState()
                    initiateEmptyPageIfNeeded(hasNotes)
                    resetMainEmptyPage()
                    changeSort = false
                }
                return@observe
            }

            if (isInMultiWindowMode() && UIConfigMonitor.isFoldingModeOpen(activity)) { //折叠屏展开时分屏
                adapter.setNoteItems(toAdapterList, true)
            } else {
                if (toAdapterList.size <= DELETE_ANIMATION_TRANSITION
                        && adapter.getNoteItemCount() <= DELETE_ANIMATION_TRANSITION) {
                    val noteDiffCallBack = RichNoteDiffCallBack(adapter.getNoteItems(), noteItems)
                    val diffResult = DiffUtil.calculateDiff(noteDiffCallBack, true)
                    adapter.setNoteItems(toAdapterList, false)
                    diffResult.dispatchUpdatesTo(NoteListUpdateCallback((adapter as RecyclerView.Adapter<*>), adapter.getHeaderCount()))
                } else {
                    adapter.setNoteItems(toAdapterList, true)
                }
            }

            val hasNotes = adapter.getNoteItemCount() != 0
            correctSearchViewState()
            initiateEmptyPageIfNeeded(hasNotes)
            //首次加载笔记数据的时候，不进行云同步tip相关刷新，因为此时云同步状态未知
            if (mIsFirstLoadNoteList) {
                mIsFirstLoadNoteList = false
                resetMainEmptyPage()
            } else {
                resetMainEmptyPage()
            }
            lifecycleScope.launch {
                adapter.refreshInfoBoardAndFolderHeaderAndQuestionnaire(hasNotes)
            }

            //刷新选择项
            if (sharedViewModel.noteSelectionMode.value == true) {
                notifySelectionChange()
            }

            correctToolbarMenu()
            updateTitle()

            if (sharedViewModel.isSearch.value == true) {
                noteListViewModel.searchText.value = noteListViewModel.searchText.value
            }
        }

        noteListViewModel.selectedNotes.observe(viewLifecycleOwner, { data: Pair<Set<String>?, Boolean?> ->
            if (data.first != null && data.second != null) {
                // 1.correct title info
                mSelectItemSize = data.first!!.size
                if (!mIsAnimating && !noteListViewModel.isDeletingOrRecovering) {
                    correctTitleInfo(mSelectItemSize, isEditMode())
                }

                // 2.correct navigationview menu state
                correctNavigationViewMenuState(data.first!!.size, data.second!!)
            }
        })

        noteListViewModel.isAllNoteSelected.observe(viewLifecycleOwner, { isAllNoteSelected: Boolean? ->
            correctToolbarSelect()
            correctDialogShow()
        })

        noteMarginViewModel.mNotePlaceHolderViewHeight.observe(viewLifecycleOwner, { height: Int ->
            adapter.updatePlaceHolderViewHeight(height)
        })

        sharedViewModel.noteSelectionMode.observe(viewLifecycleOwner, { isSelectionMode ->

            if (isSelectionMode) {
                isSelectionModeFirstInit = false
            }

            if (isSelectionModeFirstInit) {
                isSelectionModeFirstInit = false
                return@observe
            }

            if (mToolNavigationView == null || mToolNavigationViewSecondary == null) {
                initToolNavigationMenu()
            }

            updateNavigationViewMenuWithAnim(isSelectionMode)
            updateBehavior(isSelectionMode)
            toolbarAnimation()
            titleAnimation()

            if (isSelectionMode) {
                adapter.enterSelectionMode()
                refreshCheckBox(MenuMultiSelectHelper.MenuMode.ENTER)
                val data: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotes.value
                if (data?.first != null) {
                    notifySelectionChange()
                }
                binding?.noteList?.setFadingEdgeLength(resources.getDimensionPixelOffset(R.dimen.color_navigation_list_fading_edge_length))
            } else {
                adapter.exitSelectionMode()
                refreshCheckBox(MenuMultiSelectHelper.MenuMode.LEAVE)
                binding?.noteList?.setFadingEdgeLength(0)
                correctSearchViewState()
            }
        })

        noteViewModel.notifyDetailSaveData.value = 0
        sharedViewModel.viewPagerScrollStateIdle.observe(viewLifecycleOwner) { isIdle ->
            if (isIdle) {
                val hasNotes =
                        noteListViewModel.currentFolder.value != null && noteListViewModel.currentFolder.value!!.notesCount > 0
                val isSearchMode = sharedViewModel.isSearch.value ?: false
                behavior?.setScaleEnable(hasNotes && !isSearchMode)
                if (sharedViewModel.currentTabIndex.value == 0 && behavior?.getScaleEnable() == true && behavior?.hasPrimaryTitle() == false) {
                    behavior?.updateToolbar()
                }
            } else {
                behavior?.setScaleEnable(false)
            }
        }

        sharedViewModel.noteMode.observe(viewLifecycleOwner) { isGrid ->
            AppLogger.BASIC.d(TAG, "initiateObservers noteMode = isGridMode : $isGridMode ; isGrid = $isGrid")
            if (isGridMode != isGrid) {
                isSwitchGrideModing = true
                noteModeSwitcher?.beginSwitchModeDelayedLayoutAnimation(isGrid) {
                    updateRecyclerViewPadding()
                    if (isGrid) {
                        behavior?.updateToolbar()
                    }
                    isSwitchGrideModing = false
                }
                isGridMode = isGrid
            }
        }
    }

    private fun correctDialogShow() {
        if (mDeleteDialog?.isShowing == true) {
            val data: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotes.value
            if (data?.first != null) {
                mNoteListHelper?.deleteNoteItems(data.first, isAllNoteSelected(), false)
            }
        }
    }

    private fun initRefreshAndPermissionObserver() {

        noteListViewModel.completeRefreshWithTipsAndDelay.observe(viewLifecycleOwner) { tipsAndDelay: Pair<String?, Int?> ->
            binding?.noteList?.postDelayed({
                binding?.refresh?.setRefreshCompleted()
            }, (tipsAndDelay.second ?: 0).toLong())

            AccessibilityUtils.broadcastAccessibilityContent(binding?.noteList, tipsAndDelay.first)
        }

        sharedViewModel.storagePermissionDenied.observe(viewLifecycleOwner) { denied: Boolean ->
            binding?.refresh?.visibility = View.VISIBLE
            val hasNotes = adapter.getNoteItemCount() > 0
            val isRecentDelete = FolderInfo.FOLDER_GUID_RECENT_DELETE == noteListViewModel.currentFolder.value?.guid
            binding?.subTitle?.visibility = if (hasNotes) View.VISIBLE else View.INVISIBLE
            AppLogger.BASIC.d(TAG, "NavigationAnimatorHelper storagePermissionDenied.observe: "
                    + (isRecentDelete && hasNotes) + " , isEditMode() = " + isEditMode())
            if (isRecentDelete && hasNotes) {
                mNavigationAnimatorHelper?.showToolNavigationWithoutAnim(isEditMode())
            }

            correctSearchViewState()
            resetMainEmptyPage()
        }

        noteListViewModel.syncEnable.observe(viewLifecycleOwner) { syncEnable: Boolean ->
            mSyncEnable = syncEnable
            if (null != guideManager) {
                SettingPresenter.queryNoteSyncCloudStateTask(context,
                        object : CloudSyncStateCallback {
                            override fun refreshViewState(syncState: Int) {
                                AppLogger.NOTE.d(TAG, "notelist queryNoteSyncCloudState state = $syncState")
                                if (guideManager?.getSyncSwitchState() != syncState || syncEnable != mSyncEnable) {
                                    guideManager?.updateSyncSwitchState(syncState)
                                }
                                if (adapter != null) {
                                    val hasNotes = adapter.getNoteItemCount() != 0
                                    resetMainEmptyPage()
                                }
                            }

                            override fun refreshModuleState(isSupport: Boolean) {}
                        })
            }
        }
    }

    private fun updateTitle() {
        val folderInfo: FolderInfo? = noteListViewModel.currentFolder.value
        val noteCount = folderInfo?.notesCount ?: 0
        val subtitle = resources.getQuantityString(com.oplus.note.baseres.R.plurals.n_note, noteCount, noteCount)
        binding?.subTitle?.text = subtitle

        if (isEditMode()) {
            binding?.subTitle?.visibility = View.INVISIBLE
            binding?.folderRotateView?.visibility = View.GONE
        } else {
            if (folderInfo == null) {
                binding?.mainTitle?.setText(R.string.memo_all_notes)
                binding?.folderRotateView?.contentDescription = resources.getString(R.string.memo_all_notes)
            } else {
                val displayName = FolderInfo.formatFolderName(context, folderInfo.guid, folderInfo.name)
                binding?.mainTitle?.text = displayName
                binding?.folderRotateView?.contentDescription = displayName
            }

//            binding?.toolbar?.title = ""

            if (noteCount > 0) {
                binding?.subTitle?.visibility = View.VISIBLE
            } else {
                binding?.subTitle?.visibility = View.INVISIBLE
            }

            binding?.folderRotateView?.visibility = View.VISIBLE
        }
    }

    private fun correctToolbarMenu() {
        val menu: Menu? = binding?.toolbar?.menu
        if ((menu == null) || (menu.size() == 0)) {
            return
        }
        val cancel = menu.findItem(R.id.cancel)
        val select = menu.findItem(R.id.select_all)
        val search = menu.findItem(R.id.note_searchView)
        val edit = menu.findItem(R.id.edit_note)
        val mode = menu.findItem(R.id.mode_note)
        val sortRule = menu.findItem(R.id.sort_rule)
        val toggleFinishedTodo = menu.findItem(R.id.toggle_finished_todo)
        val setting = menu.findItem(R.id.jump_setting)
        val emptyItem = menu.findItem(R.id.empty)
        if (cancel == null || select == null || search == null || edit == null
                || mode == null || toggleFinishedTodo == null || setting == null) {
            return
        }

        val isSelectionMode = isEditMode()
        val hasNotes = noteListViewModel.currentFolder.value != null && noteListViewModel.currentFolder.value!!.notesCount != 0
        val isAllNoteSelected = isAllNoteSelected()
        val isSearchMode = sharedViewModel.isSearch.value ?: false
        cancel.isVisible = false
        select.isVisible = false
        edit.isVisible = false
        mode.isVisible = false
        sortRule.isVisible = false
        search.isVisible = false
        search.setTitle(R.string.memo_search_hint)
        toggleFinishedTodo.isVisible = false
        setting.isVisible = false
        emptyItem.isVisible = false
        select.setTitle(if (isAllNoteSelected) R.string.unselect_all else R.string.select_all)
        select.setIcon(if (isAllNoteSelected) {
            com.support.appcompat.R.drawable.coui_btn_check_on_normal
        } else {
            com.support.appcompat.R.drawable.coui_btn_check_off_normal
        })
        behavior?.setScaleEnable(hasNotes && !isSearchMode)
    }

    private fun isEditMode(): Boolean {
        return sharedViewModel.noteSelectionMode.value ?: false
    }

    private fun isAllNoteSelected(): Boolean {
        return noteListViewModel.isAllNoteSelected.value ?: false
    }

    private fun initToolNavigationMenu() {
        mToolNavigationView = editMenuStub.value?.inflate() as? COUINavigationView
        mToolNavigationViewSecondary = editMenuStubSecondary.value?.inflate() as? COUINavigationView
        mToolNavigationView?.apply {
            setOnItemSelectedListener(onItemSelectedListener)
        }
        mToolNavigationViewSecondary?.apply {
            setOnItemSelectedListener(onItemSelectedListener)
            visibility = View.GONE
        }

        mNavigationAnimatorHelper = NavigationAnimatorHelper(requireContext())
        mNavigationAnimatorHelper?.initToolNavigationAnimator(mToolNavigationView!!)
        mNavigationAnimatorHelper?.initToolNavigationAnimatorSecondary(mToolNavigationViewSecondary!!)
    }

    private val onItemSelectedListener = NavigationBarView.OnItemSelectedListener { item ->
        AppLogger.BASIC.d(TAG, "onNavigationItemSelected twoPane=$twoPane,itemId=${item.itemId}")
       return@OnItemSelectedListener if (twoPane && noteViewModel.selectedNoteWithFolder.value != null) {
            //父子级结构,子级先保存数据,等子级数据保存完了再执行点击操作
            noteViewModel.notifyDetailSaveData.value = item.itemId
            true
        } else {
            handleNavigationItemSelected(item.itemId)
        }
    }

    private fun handleNavigationItemSelected(itemId: Int): Boolean {
        when (itemId) {
            R.id.note_move_folder -> {
                AppLogger.BASIC.d(TAG, "unsupported ops. note_move_folder")
            }
            R.id.note_encrypted -> {
                val data: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotes.value
                if (data?.first != null) {
                    val selectedNotes: Set<String> = HashSet(data.first)
                    mNoteListHelper?.encryptOrDecryptAsDefault(
                        this,
                        noteListViewModel.currentFolder.value,
                        selectedNotes,
                        false
                    )
                }
            }
            R.id.note_topped -> {
                val data: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotes.value
                if (data?.first != null) {
                    val isAllTopped = data.second != null && data.second!!
                    val selectedNotes: Set<String> = HashSet(data.first)
                    mNoteListHelper?.toppedNoteItems(selectedNotes, isAllTopped, noteListViewModel.currentFolder.value)
                    if (isAllNoteSelected()) {
                        if (isAllTopped) {
                            StatisticsUtils.setEventTopped(
                                context,
                                StatisticsUtils.TYPE_NOTE_ALL_UN_TOP
                            )
                        } else {
                            StatisticsUtils.setEventTopped(
                                context,
                                StatisticsUtils.TYPE_NOTE_ALL_TOP
                            )
                        }
                    }
                }
            }
            R.id.note_delete -> {
                val data: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotes.value
                if (data?.first != null) {
                    mNoteListHelper?.deleteNoteItems(data.first, isAllNoteSelected(), isNotAllowSyncEncryptNoteToCloud())
                }
            }
            R.id.delete_note_recover -> {
                val data: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotes.value
                if (data?.first != null) {
                    mNoteListHelper?.recoverNoteItems(data.first, isAllNoteSelected())
                }
                StatisticsUtils.setEventRecoverNote(context, StatisticsUtils.TYPE_RECOVER_LIST)
            }
            R.id.delete_note_remove_completely -> {
                val data: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotes.value
                if (data?.first != null) {
                    mNoteListHelper?.deleteMarkNoteItems(data.first, isAllNoteSelected(),
                        isNotAllowSyncEncryptNoteToCloud() or isDeleteEncryptNoteWhenNotAllowSyncToCloud(),false)
                }
            }
            R.id.delete_all -> {
                val noteSets: MutableSet<String> = HashSet()
                val richNoteItemList: List<RichNoteItem> = noteListViewModel.richNoteItemList.value
                        ?: return false
                for (richNoteItem in richNoteItemList) {
                    if (richNoteItem.viewType == RichNoteItem.TYPE_NOTE && richNoteItem.data != null) {
                        noteSets.add(richNoteItem.data.richNote.localId)
                    }
                }
                if (noteSets.isEmpty()) {
                    return false
                }
                val data = Pair<Set<String>, Boolean?>(noteSets, null)
                noteListViewModel.selectedNotes.value = data
                mNoteListHelper?.deleteMarkNoteItems(data.first, true,
                    isNotAllowSyncEncryptNoteToCloud() or isDeleteEncryptNoteWhenNotAllowSyncToCloud(),false)
            }
        }

        return true
    }

    private fun updateNavigationViewMenuWithAnim(isSelectionMode: Boolean) {
        val isRecentDelete = FolderInfo.FOLDER_GUID_RECENT_DELETE == noteListViewModel.currentFolder.value?.guid
        if (isRecentDelete) {
            if (isSelectionMode) {
                mNavigationAnimatorHelper?.showToolNavigationSecondary(twoPane)
            } else {
                if (noteListViewModel.richNoteItemList.value.isNullOrEmpty()) {
                    mToolNavigationViewSecondary?.visibility = View.GONE
                    mToolNavigationView?.visibility = View.GONE
                } else {
                    mToolNavigationView?.inflateMenu(R.menu.menu_note_all_delete)
                    mNavigationAnimatorHelper?.dismissToolNavigationSecondary()
                }
            }
        } else {
            if (isSelectionMode) {
                mToolNavigationView?.inflateMenu(R.menu.menu_note_list_edit)
                if (twoPane) {
                    mNavigationAnimatorHelper?.showToolNavigationTwo()
                } else {
                    mNavigationAnimatorHelper?.showToolNavigation()
                }
            } else {
                if (twoPane) {
                    mNavigationAnimatorHelper?.dismissToolNavigationTwo()
                } else {
                    mNavigationAnimatorHelper?.dismissToolNavigation()
                }
            }
        }
    }

    /**
     * Alpha animation of toolbar when switch between normal mode and edit mode
     */
    @SuppressLint("ObjectAnimatorBinding")
    private fun toolbarAnimation() {
        val toolbarOutAnimation = ObjectAnimator.ofFloat(binding?.toolbar, "alpha", 1f, 0f)
        val toolbarInAnimation = ObjectAnimator.ofFloat(binding?.toolbar, "alpha", 0f, 1f)
        val toolbarAnimatorSet = AnimatorSet()
        toolbarAnimatorSet.duration = ALPHA_DURATION
        toolbarAnimatorSet.interpolator = LinearInterpolator()
        toolbarAnimatorSet.play(toolbarOutAnimation).before(toolbarInAnimation)
        toolbarOutAnimation.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                mIsAnimating = false
                if (!isAdded) {
                    return
                }

                val isSelectionMode = isEditMode()
                if (isSelectionMode) {
                    correctTitleInfo(mSelectItemSize, true)
                }
                updateTitle()
                correctToolbarMenu()
                behavior?.apply {
                    val hasNotes = adapter.getNoteItemCount() > 0
                    setScaleEnable(hasNotes)
                    val isRunning = binding?.noteList?.itemAnimator?.isRunning
                    AppLogger.BASIC.d(TAG, "toolbarAnimationEnd, noteList itemAnimator isRunning=$isRunning")

                    if (isRunning == true) {
                        updateTitleMargin()
                    } else {
                        updateToolbar()
                    }
                }
            }
        })
        mIsAnimating = true
        behavior?.setScaleEnable(false)
        toolbarAnimatorSet.start()
    }

    /**
     * Alpha animation of primary title when switch between normal mode and edit mode
     */
    @SuppressLint("ObjectAnimatorBinding")
    private fun titleAnimation() {
        val alphaOutAnimation = ObjectAnimator.ofFloat(binding?.titleContainer, "alpha", 1f, 0f)
        val alphaInAnimation = ObjectAnimator.ofFloat(binding?.titleContainer, "alpha", 0f, 1f)
        val animationSet = AnimatorSet()
        animationSet.interpolator = LinearInterpolator()
        animationSet.duration = ALPHA_DURATION
        animationSet.play(alphaOutAnimation).before(alphaInAnimation)
        animationSet.start()
    }

    private fun correctTitleInfo(selectedSize: Int, isSelectionMode: Boolean) {
        if (isSelectionMode) {
            if (selectedSize == 0) {
                binding?.mainTitle?.setText(com.oplus.note.baseres.R.string.memo_select_note)
            } else {
                val isAllNoteSelected = (selectedSize == adapter.getNoteItemCount())
                binding?.mainTitle?.text =
                    if (isAllNoteSelected) {
                        getString(com.oplus.note.baseres.R.string.memo_note_select_all)
                    } else {
                        getString(com.oplus.note.baseres.R.string.memo_note_select_num, selectedSize.toString())
                    }
            }
        }
    }

    private fun correctNavigationViewMenuState(selectedCount: Int, topped: Boolean) {
        if (mToolNavigationView == null || mToolNavigationViewSecondary == null) {
            initToolNavigationMenu()
        }

        mToolNavigationView?.let { correctNavigationStateByMenu(selectedCount, topped, it) }
        mToolNavigationViewSecondary?.let { correctNavigationStateByMenu(selectedCount, topped, it) }

    }

    private fun correctNavigationStateByMenu(selectedCount: Int, topped: Boolean, mToolNavigationView: COUINavigationView) {
        var i = 0
        val size = mToolNavigationView.menu.size()
        while (i < size) {
            val item = mToolNavigationView.menu.getItem(i)
            when (item.itemId) {
                R.id.note_move_folder, R.id.note_delete, R.id.delete_note_recover, R.id.delete_note_remove_completely -> item.isEnabled = selectedCount > 0
                R.id.note_topped -> {
                    val flag = selectedCount > 0
                    item.isEnabled = flag
                    if (flag) {
                        correctToppedMenuState(item, topped)
                    } else {
                        correctToppedMenuState(item, false)
                    }
                }
                R.id.note_encrypted -> {
                    item.isEnabled = selectedCount > 0
                    correctEncryptMenuState(item)
                }
            }
            ++i
        }
    }

    private fun correctToppedMenuState(item: MenuItem, topState: Boolean) {
        item.setTitle(if (topState) R.string.option_note_cancel_toped else R.string.option_note_top)
        item.setIcon(if (topState) R.drawable.color_menu_ic_un_topped else R.drawable.color_menu_ic_topped)
    }

    private fun correctEncryptMenuState(item: MenuItem) {
        val currentFolderInfo: FolderInfo = getCurrentFolderInfo() ?: return
        if (TextUtils.equals(currentFolderInfo.guid, FolderInfo.FOLDER_GUID_ENCRYPTED)) {
            item.setTitle(com.oplus.note.baseres.R.string.set_unencrypted_to_folder)
            item.setIcon(com.oplus.note.baseres.R.drawable.note_ic_decrypt)
        } else {
            item.setTitle(com.oplus.note.baseres.R.string.set_encrypted_to_folder)
            item.setIcon(com.oplus.note.baseres.R.drawable.note_ic_encrypt)
        }
    }

    private fun correctToolbarSelect() {
        val menu: Menu? = binding?.toolbar?.menu
        if ((menu == null) || (menu.size() == 0)) {
            return
        }

        val select = menu.findItem(R.id.select_all) ?: return
        val isAllNoteSelected = isAllNoteSelected()
        select.setTitle(if (isAllNoteSelected) R.string.unselect_all else R.string.select_all)
        select.setIcon(if (isAllNoteSelected) {
            com.support.appcompat.R.drawable.coui_btn_check_on_normal
        } else {
            com.support.appcompat.R.drawable.coui_btn_check_off_normal
        })
    }

    private fun correctSearchViewState() {
        if (searchItem == null) {
            return
        }

        val hasNotes = adapter.getNoteItemCount() > 0
        val isAgreeUserNotice = PrivacyPolicyHelper.isAgreeUserNotice(context)
        val inSearchMode = sharedViewModel.isSearch.value ?: false
        val enable = !isEditMode() && hasNotes && isAgreeUserNotice || inSearchMode
        searchItem?.isEnabled = enable
    }

    fun getCurrentFolderInfo(): FolderInfo? {
        return noteListViewModel.currentFolder.value
    }

    private fun notifySelectionChange() {
        val selectedNotes: Set<String> = adapter.getSelectedNotes()
        val isAllTopped: Boolean = adapter.isAllSelectedNotesAreTopped()
        val data = Pair(selectedNotes, isAllTopped)
        noteListViewModel.selectedNotes.value = data
        val isAllNoteSelected = (selectedNotes.isNotEmpty()
                && selectedNotes.size == adapter.getNoteItemCount())
        if (isAllNoteSelected != noteListViewModel.isAllNoteSelected.value) {
            noteListViewModel.isAllNoteSelected.value = isAllNoteSelected
        }
    }

    private fun initNoteListHelper() {
        val noteListHelperCallBack = object : NoteListHelper.CallBack {
            override fun exitRefreshing(tips: String?, delay: Int) {
                noteListViewModel.completeRefreshWithTipsAndDelay.value = Pair(tips, delay)
            }

            override fun showTips(type: Int, arg: Bundle?) {
                if (mDialogFactory == null) {
                    initDialogFactory()
                }
                val dialog = mDialogFactory?.showDialog(type, arg)

                if (DialogFactory.TYPE_DIALOG_DELETE == type) {
                    mDeleteDialog = dialog!!
                }
            }

            override fun downloadSkin() {
                noteListViewModel.downloadSkin()
            }

            override fun setIsEncryptOrDecrypt(isEncryptOrDecrypt: Boolean) {
                mIsEncryptOrDecrypt = isEncryptOrDecrypt
            }

            override fun updateAdapterModeForListAndMenu(isGrid: Boolean) {
                switchAdapterMode(isGrid)
            }

            override fun turnToAllNoteFolder() {
                switchToAllNoteFolder()
            }
        }
        mNoteListHelper = NoteListHelper(noteListHelperCallBack)
        mNoteListHelper?.setMenuExecutorListener(object : ExecutorProgressListener {
            override fun onExecutorComplete(action: Int, selectedNotes: MutableSet<String>?) {
                if (action != MenuExecutor.ACTION_CLEAN_DB) {
                    sharedViewModel.noteSelectionMode.value = false
                    noteListViewModel.isDeletingOrRecovering = false
                }
            }

            override fun onMoveFolderComplete(
                currentFolderGuid: String?,
                destFolderGuid: String?,
                selectedNotes: MutableSet<String>?
            ) {
                // 速记列表页无加密笔记功能
            }
        })
        mNoteListHelper?.initData(activity, true)
    }

    /**
     * Switch staggered grid mode and list mode
     */
    private fun switchAdapterMode(isGrid: Boolean) {
        sharedViewModel.noteMode.value = isGrid
        updateToolbarMenuByMode(isGrid)
    }

    private fun updateToolbarMenuByMode(isGrid: Boolean) {
        binding?.toolbar?.menu?.findItem(R.id.mode_note)?.setTitle(if (isGrid) R.string.note_list_mode else R.string.note_grid_mode)
    }

    private fun updateToolbarMenuBySortRule(sortRule: Int?) {
        binding?.toolbar?.menu?.findItem(R.id.sort_rule)?.setTitle(
                if (sortRule == SortRule.SORT_RULE_BY_UPDATE_TIME) R.string.sort_rule_by_create_time
                else R.string.sort_rule_by_update_time)
    }

    private fun switchToAllNoteFolder() {
        try {
            val folders: List<FolderItem>? = noteListViewModel.folders.value
            if (folders != null) {
                val folder = folders[0]
                noteListViewModel.currentFolder.value = folder.parseToFolderInfo(context, folder)
            }
        } catch (ignore: Exception) {
        }
    }

    private fun initDialogFactory() {
        mDialogClickListener = object : DialogFactory.DialogOnClickListener {
            override fun onDialogClickButton(type: Int, index: Int) {
            }

            override fun onDialogClickPositive(type: Int) {
                if (null != mNoteListHelper) {
                    val data: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotes.value
                    when (type) {
                        DialogFactory.TYPE_DIALOG_DELETE_MARK -> {
                            if (data?.first != null) {
                                val selectedNotes: Set<String> = HashSet(data.first)
                                noteListViewModel.isDeletingOrRecovering = true
                                mNoteListHelper!!.noteListEdit(DialogFactory.TYPE_DIALOG_DELETE_MARK,
                                        noteListViewModel.currentFolder.value, selectedNotes, isAllNoteSelected(), true)
                            }
                        }
                        DialogFactory.TYPE_DIALOG_DELETE -> {
                            if (data?.first != null) {
                                val selectedNotes: Set<String> = HashSet(data.first)
                                noteListViewModel.isDeletingOrRecovering = true
                                mNoteListHelper!!.noteListEdit(DialogFactory.TYPE_DIALOG_DELETE_CONFIRM,
                                        noteListViewModel.currentFolder.value, selectedNotes, isAllNoteSelected(), true)
                            }
                        }
                        DialogFactory.TYPE_DIALOG_RECOVER -> {
                            if (data?.first != null) {
                                val selectedNotes: Set<String> = HashSet(data.first)
                                noteListViewModel.isDeletingOrRecovering = true
                                mNoteListHelper!!.noteListEdit(DialogFactory.TYPE_DIALOG_RECOVER,
                                        noteListViewModel.currentFolder.value, selectedNotes, isAllNoteSelected(), true)
                            }
                        }
                        DialogFactory.DIALOG_TYPE_DECLARE_ALERT_WINDOW_PERMISSION_REQUEST -> {
                            mCallBack?.startRefresh()
                        }
                    }
                }
            }

            override fun onDialogClickNegative(type: Int) {
                if (type == DialogFactory.DIALOG_TYPE_DECLARE_ALERT_WINDOW_PERMISSION_REQUEST) {
                    binding?.refresh?.setRefreshCompleted()
                }
            }

            override fun onDialogDismiss(type: Int) {
            }
        }
        mDialogFactory = DialogFactory(activity, mDialogClickListener)
    }

    private fun isInMultiWindowMode(): Boolean {
        return activity?.isInMultiWindowMode == true
    }

    private fun switchAdapterSortRule() {
        if (!isAdded) {
            return
        }

        var sortRule: Int? = noteListViewModel.sortRule.value
        if (sortRule == null) {
            sortRule = readSortRule()
        }
        sortRule = if (sortRule == SortRule.SORT_RULE_BY_UPDATE_TIME) {
            SortRule.SORT_RULE_BY_CREATE_TIME
        } else {
            SortRule.SORT_RULE_BY_UPDATE_TIME
        }
        val currentMode = SharedPreferencesUtil.getInstance().getInt(context,
                SharedPreferencesUtil.SHARED_PREFERENCES_NAME, SharedPreferencesUtil.HOME_PAGE_MODE_KEY)
        val isGrid = currentMode == SharedPreferencesUtil.HOME_PAGE_MODE_GRID_MODE
        StatisticsUtils.setEventSortRule(sortRule, isGrid)
        changeSort = true
        noteListViewModel.sortRule.value = sortRule
    }

    private fun updateBehavior(isEditMode: Boolean) {
        behavior?.setIsEditMode(isEditMode)
        var marginStart = 0
        if (isEditMode) {
            marginStart = supportTitleMarginStart
        }
        binding?.toolbar?.titleMarginStart = marginStart
    }

    private fun isNotAllowSyncEncryptNoteToCloud(): Boolean =
        adapter.isEncryptedFolder() && ConfigUtils.isNotAllowSyncEncryptNoteToCloud

    private fun isDeleteEncryptNoteWhenNotAllowSyncToCloud(): Boolean =
        adapter.isAllSelectedNotesAreEncrypt() && ConfigUtils.isNotAllowSyncEncryptNoteToCloud

    private fun resetMainEmptyPage() {
        if (emptyContentViewLazyLoader == null) {
            return
        }
        if (emptyContentViewLazyLoader?.isInitialized() == true) {
            val isRecentDelete = FolderInfo.FOLDER_GUID_RECENT_DELETE == adapter.getCurrentFolder()?.guid

            emptyContentViewLazyLoader?.switchImgEmptyContent(
                isRecentDelete && ConfigUtils.isOplusExportVersion
            )
            var isInMultiWindowMode = false
            if (OplusActivityManagerProxy.isOS15) {
                isInMultiWindowMode = OplusFlexibleWindowManagerProxy.isInMultiWindowMode(activity)
            } else {
                if (activity != null) {
                    isInMultiWindowMode = requireActivity().isInMultiWindowMode
                }
            }
            val hasNotes = adapter.getNoteItemCount() > 0
            val isSearchMode = sharedViewModel.isSearch.value ?: false
            val isEncryptedFolder = adapter.isEncryptedFolder()
            emptyContentViewLazyLoader?.resetMainEmptyPage(
                activity,
                isInMultiWindowMode,
                hasNotes,
                loadDataFinished,
                isSearchMode,
                mSyncEnable,
                isEncryptedFolder,
                isQuickNote = true,
                isRecentDelete = isRecentDelete
            )
        }
    }

    private fun updateRecyclerViewPadding() {
        if (!isAdded) {
            return
        }
        val padding = DensityHelper.getDefaultConfigDimension(R.dimen.quick_list_item_margin_horizontal)
        val paddingBottom = resources.getDimensionPixelOffset(R.dimen.note_edit_mode_padding_bottom)
        binding?.noteList?.setPadding(padding, 0, padding, paddingBottom)
    }

    fun resetCheckedInfo(): String {
        val tempGuid = noteListViewModel.checkedGuid
        noteListViewModel.checkedGuid = ""
        adapter.setCheckedGuid("")
        preCheckedGuid = ""
        noteViewModel.selectedNoteWithFolder.value = null
        return tempGuid
    }

    private fun updateCheckedInfo(guid: String, position: Int) {
        if (twoPane) {
            noteListViewModel.checkedGuid = guid
            adapter.setCheckedGuid(guid)
            //1.取消之前选中的item
            val preCheckedPosition = getPositionByGuid(preCheckedGuid)
            AppLogger.BASIC.d(TAG, "onItemClick preCheckedPosition=$preCheckedPosition, position=$position")
            val preItemImageView = findItemImageViewByGuid(preCheckedGuid)
            if (preItemImageView != null) {
                setItemBackground(preItemImageView, false)
            } else {
                if (preCheckedPosition > 0) {
                    adapter.notifyItemChanged(preCheckedPosition)
                }
            }

            //2.刷新当前按下的item
            preCheckedGuid = guid
            val itemImage = findItemImageViewByGuid(guid)
            if (itemImage != null) {
                setItemBackground(itemImage, true)
            } else {
                adapter.notifyItemChanged(position)
            }
        }
    }

    private fun findItemImageViewByGuid(guid: String): PressAnimView? {
        binding?.noteList?.apply {
            for (i in 0 until childCount) {
                val child = getChildAt(i)
                val itemImage: PressAnimView? = child?.findViewById(R.id.item_press_view_grid)
                if (itemImage != null && itemImage.getTag(R.id.tag_note_guid) == guid) {
                    return itemImage
                }
            }
        }

        return null
    }

    private fun setItemBackground(itemImage: PressAnimView, isPressed: Boolean) {
        activity?.let {
            val defaultDrawable = ContextCompat.getDrawable(it, R.drawable.bg_grid_item) as GradientDrawable
            if (isPressed) {
                defaultDrawable.setColor(COUIContextUtil.getAttrColor(it, com.support.appcompat.R.attr.couiColorPressBackground))
            } else {
                defaultDrawable.setColor(COUIContextUtil.getAttrColor(it, com.support.appcompat.R.attr.couiColorCardBackground))
            }

            itemImage.background = defaultDrawable
        }
    }

    private fun getPositionByGuid(guid: String): Int {
        return guidHashMap?.get(guid) ?: 0
    }

    inner class LocalReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent == null || !isAdded) {
                return
            }
            if (Constants.ACTION_SAVE_NOTE_COMPLETE == intent.action) {
                val guid = IntentParamsUtil.getStringExtra(intent, Constants.EXTRA_NOTE_GUID, "")

                if (twoPane && guid.isNotEmpty()) {
                    val position = getPositionByGuid(guid)
                    AppLogger.BASIC.d(TAG, "onReceive guid:$guid, position=$position")
                    if (position > 0) {
                        adapter.notifyItemChanged(position)
                    }
                }
            } else if (Constants.ACTION_SAVE_NOTE_FINISHED == intent.action) {
                AppLogger.BASIC.d(TAG, "onReceive itemId:${noteViewModel.notifyDetailSaveData.value}")
                if (twoPane && (sharedViewModel.noteSelectionMode.value == true || sharedViewModel.isRecentDeleteFolder.value == true)) {
                    handleNavigationItemSelected(noteViewModel.notifyDetailSaveData.value!!)
                }
            } else if (Constants.ACTION_SAVE_PICTURE_COMPLETE == intent.action) {
                val guid = IntentParamsUtil.getStringExtra(intent, Constants.EXTRA_NOTE_GUID, "")

                if (guid.isNotEmpty()) {
                    val position = getPositionByGuid(guid)
                    AppLogger.BASIC.d(TAG, "onReceive ACTION_SAVE_PICTURE_COMPLETE guid:$guid, position=$position")
                    if (position > 0) {
                        adapter.notifyItemChanged(position)
                    }
                }
            } else if (Constants.ACTION_DOWNLOAD_SKIN_COMPLETE == intent.action) {
                AppLogger.BASIC.d(TAG, "onReceive ACTION_DOWNLOAD_SKIN_COMPLETE")
                adapter.notifyDataSetChanged()
            }
        }
    }


    fun refreshCheckBox(selectMode: MenuMultiSelectHelper.MenuMode) {
        val listLayoutManager = binding?.noteList?.layoutManager as StaggeredGridLayoutManager
        val startArray = listLayoutManager.findFirstVisibleItemPositions(null)
        val endArray = listLayoutManager.findLastVisibleItemPositions(null)

        Arrays.sort(startArray)
        Arrays.sort(endArray)

        var start = startArray[0].coerceAtLeast(adapter.getHeaderCount())
        var end = endArray[endArray.size - 1]
        AppLogger.BASIC.d(TAG, "refreshCheckBox  start=$start,  end=$end")
        adapter.apply {
            for (i in start..end) {
                val holder =
                    binding?.noteList?.findViewHolderForLayoutPosition(i) as? NoteViewHolder
                        ?: continue
                val checkbox = if (getAdapterMode() == RichNoteListAdapter.ADAPTER_MODE_LIST) {
                    holder.mListCheckbox
                } else {
                    holder.mGridCheckbox
                }

                when (selectMode) {
                    MenuMultiSelectHelper.MenuMode.ENTER -> {
                        craeteAnimation(holder, true)?.start()
                        holder.mIsEditMode = true
                    }
                    MenuMultiSelectHelper.MenuMode.LEAVE -> {
                        holder.mIsEditMode = false
                        checkbox.isChecked = false
                        craeteAnimation(holder, false)?.start()
                    }
                    MenuMultiSelectHelper.MenuMode.SELECT_ALL -> {
                        checkbox.isChecked = true
                    }
                    MenuMultiSelectHelper.MenuMode.DE_SELECT_AL -> {
                        checkbox.isChecked = false
                    }
                }
            }

            notifyItemRangeChanged(0, start, RichNoteListAdapter.CHECKBOX_POSITION)
            notifyItemRangeChanged(end, itemCount - end, RichNoteListAdapter.CHECKBOX_POSITION)
        }
    }
}