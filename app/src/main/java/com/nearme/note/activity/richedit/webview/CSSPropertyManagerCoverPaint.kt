/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : CSSPropertyManager.kt
 * Description    : CSSPropertyManager.kt
 * Version        : 1.0
 * Date           : 2023/10/27
 * Author         : Chandler
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * Chandler     2023/10/27         1.0           create
 */
package com.nearme.note.activity.richedit.webview

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Typeface
import android.net.Uri
import android.provider.Settings
import android.util.Base64
import androidx.core.graphics.ColorUtils
import com.coui.appcompat.contextutil.COUIContextUtil
import com.nearme.note.skin.SkinData
import com.nearme.note.skin.api.SkinManager
import com.nearme.note.skin.bean.Skin
import com.nearme.note.skin.renderer.EditPageSkinRenderer
import com.nearme.note.util.DarkModeUtil
import com.oplus.note.osdk.proxy.OplusFontManagerProxy
import com.nearme.note.util.WindowInsetsUtil
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import com.oplus.note.osdk.proxy.OplusBuildProxy
import com.oplus.note.utils.FastMath
import com.oplus.notes.webviewcoverpaint.container.api.CombinedCardCss
import com.oplus.notes.webviewcoverpaint.container.common.CssHelper
import com.oplus.notes.webviewcoverpaint.container.web.AndroidResourcePathHandler
import com.oplus.notes.webviewcoverpaint.data.SkinCssParams
import com.oplus.notes.webviewcoverpaint.container.api.IWebViewContainerCoverpaint
import com.oplus.richtext.core.utils.RichUiHelper
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileNotFoundException
import kotlin.math.ceil

class CSSPropertyManagerCoverPaint(private val context: Context, private val webViewContainer: IWebViewContainerCoverpaint? = null) {
    companion object {
        const val TAG = "CSSPropertyManager"
        const val NAME_FONT_VARIATIONS_SETTINGS = "font_variation_settings"
        const val DEFAULT_FONT_VARIATIONS_SETTINGS = 550
        const val DEFAULT_CONTENT_PADDING_TOP = 4
        const val BASELINE_DENSITY = 160
        const val ALPHA_20 = 51
        const val ALPHA_30 = 77
        const val ALPHA_40 = 102
        const val WHITE = "#FFFFFF"
        const val LIGHT_MODE_APPLY = 0
        const val NIGHT_MODE_APPLY = 1

        /**
         *  0 ： 代表 Roboto字体
         *  1 ： 代表 op sans
         *  2 ： 代表 op sans，且打开了根据场景自动调整粗细；
         *  3 ： 代表 One Sans
         */
        const val FONT_VARIATIONS_STATUS_OPPO_SANS = 1
        const val FONT_VARIATIONS_STATUS_OPPO_SANS_AUTO = 2
        private const val TITLE_LINE_MULTI = 1.1f
        private const val CONTENT_LINE_MULTI = 1.0f
        private const val BITMAP_COMPRESS_QUALITY = 100

        private const val SEEK_BAR_OFFSET: Int = 100
        private const val MAX_FONT_WEIGHT: Int = 800
        private const val MAX_FONT_WEIGHT_V: Int = 566
        private const val FONT_VARIATION_END: Int = 1000
        private const val WGHT_OFFSET_200: Int = 200
        private const val WGHT_OFFSET_350: Int = 350

        private const val TABLE_BORDER_COLOR_IN_BLACK_RULE = "#FFFFFF66"
        private const val TABLE_DOT_COLOR_IN_BLACK_RULE  = "#FFFFFF8A"
        private const val TABLE_SCROLL_BAR_COLOR_IN_BLACK_RULE  = "#FFFFFF40"
        private const val TABLE_BORDER_COLOR_IN_LIGHT_RULE = "#0000001F"
        private const val TABLE_DOT_COLOR_IN_LIGHT_RULE = "#00000066"
        private const val TABLE_SCROLL_BAR_COLOR_IN_LIGHT_RULE = "#00000029"
    }

    private val boldWghtOffset: Int by lazy {
        if (OplusBuildProxy.isAboveOS150()) {
            if (webViewContainer?.isUsingTBLWebView() == true) {
                // TBLWebView的font-weight会和wght效果叠加，导致字重变粗。
                // 所以采用font-weight=600来加粗的方式兼容其它语言，不采用wght+200的方式。
                0
            } else {
                // 系统webview的font-weight和wght效果不叠加，采在OPPO Sans字体下，只有wght生效，所以采用wght+200。
                WGHT_OFFSET_200
            }
        } else {
            WGHT_OFFSET_350
        }
    }

    private val defaultDensity: Float by lazy {
        WindowInsetsUtil.getDefaultDensity().toFloat() / BASELINE_DENSITY.toFloat()
    }

    private val fontVariationSettings: Int by lazy {
        Settings.System.getInt(context.contentResolver, NAME_FONT_VARIATIONS_SETTINGS, DEFAULT_FONT_VARIATIONS_SETTINGS)
    }
    private val fontVariationStatus: Int by lazy {
        //int类型，第4位代表状态
        (fontVariationSettings and 0x00000f000) shr 12
    }
    private val fontVariationValues: Int by lazy {
        //int类型，后三位代表粗细度 wght
        val originWght = fontVariationSettings and 0x000000fff
        val finalWght = if (OplusBuildProxy.isAboveOS150()) {
            parseFontVariationAfterOS15(originWght)
        } else {
            originWght
        }
        finalWght
    }

    /**
     * op sans非标准的wght，区别标准的weight
     */
    val titleWght: Int? by lazy {
        getDefaultTitleWght()
    }

    /**
     * op sans非标准的wght，区别标准的weight
     */
    val contentWght: Int? by lazy {
        getDefaultContentWght()
    }
    val boldWght: Int? by lazy {
        getDefaultBoldWght()
    }
    val linkColor: Int by lazy {
        RichUiHelper.linkColor ?: COUIContextUtil.getAttrColor(context, com.coui.appcompat.R.attr.couiColorPrimaryText)
    }
    val thickness: Float by lazy {
        (RichUiHelper.getLinkLineHeight() ?: 0f) / defaultDensity
    }
    val textColorHighlight: Int by lazy {
        COUIContextUtil.getAttrColor(context, android.R.attr.textColorHighlight)
    }
    val colorPrimary: Int by lazy {
        COUIContextUtil.getAttrColor(context, com.coui.appcompat.R.attr.couiColorPrimary)
    }
    private val isFontVariationSettings by lazy {
        ((fontVariationStatus == FONT_VARIATIONS_STATUS_OPPO_SANS) || (fontVariationStatus == FONT_VARIATIONS_STATUS_OPPO_SANS_AUTO)) && !isFlipFont
    }

    /**
     * 判断是否是第三方字体
     * true 代表第三方字体
     * false 代表系统的三种字体
     */
    val isFlipFont by lazy {
        runCatching {
            OplusFontManagerProxy.isFlipFontUsed()
        }.getOrDefault(false)
    }

    private fun getDefaultTitleWght(): Int? {
        // 无级可变字体才返回wght，否则返回null
        return if (isFontVariationSettings) {
            // 标题wght需比内容多200
            Math.min(fontVariationValues + boldWghtOffset, FONT_VARIATION_END)
        } else {
            null
        }.also {
            AppLogger.BASIC.d(TAG, "getDefaultTitleWght: $it")
        }
    }

    private fun getDefaultContentWght(): Int? {
        // 无级可变字体才返回wght，否则返回null
        return if (isFontVariationSettings) {
            fontVariationValues
        } else {
            null
        }.also {
            AppLogger.BASIC.d(TAG, "getDefaultContentWeight: $it")
        }
    }

    private fun getDefaultBoldWght(): Int? {
        val result = if (isFontVariationSettings) {
            Math.min(fontVariationValues + boldWghtOffset, FONT_VARIATION_END)
        } else {
            null
        }
        AppLogger.BASIC.d(TAG, "getDefaultBoldWght: $result")
        return result
    }

    private inline fun setFont(contentFontPath: String, block: (String) -> Unit) {
        val font = contentFontPath.ifEmpty {
            /**
             * 若皮肤字体为空，先判断是否为三方字体
             * 若为三方字体，传入-1到WebView中
             * 否则传入获取到的字体值到WebView中
             */
            takeIf { isFlipFont }?.let { "-1" } ?: "$fontVariationStatus"
        }
        block(font)
    }

    /**
     *  预期表现：非轻量版本，修改系统显示大小和字体大小，详情页字体大小不变。轻量版本，跟随变化。
     *  通过配置WebViewSettings.textZoom = 100，使WebView不随系统字体大小改变。
     *  所以这里只处理修改系统显示大小变化，计算scale。
     */
    fun updateScaledDensity(isPaintEmpty: Boolean) {
        val scale = if (isPaintEmpty) {
            1.0f
        } else {
            defaultDensity * BASELINE_DENSITY / RichUiHelper.density.toFloat()
        }
        AppLogger.BASIC.d(TAG, "updateScaledDensity: defaultDensity=$defaultDensity, RichUiHelper.density=${RichUiHelper.density}" +
                    " isPaintEmpty=$isPaintEmpty scale=$scale")
        webViewContainer?.setDensityScale(scale)
    }

    fun getCssParamsFromSkinEditPage(
        context: Context,
        skinId: String,
        skinEditPage: Skin.EditPage,
        isSwitchToSystemFontFromThird: Boolean
    ): SkinCssParams {
        val titleCssParams = getTitleCssParams(skinId, skinEditPage.title, skinEditPage.background.titleBg)
        val contentCssParams = getContentCssParams(skinId, skinEditPage.content)
        val contentTextColor = SkinManager.getColor(skinEditPage.content.textColor)
        val checkboxCssParams = getCheckBoxCssParams(context, skinId, skinEditPage.checkbox, contentTextColor)
        val webCardCssParams = getWebCardCssParams(context)
        val combinedCardCssParams = getCombinedCardCss(context)

        val backgroundColor = getSkinBackgroundColor(skinId, skinEditPage)?.let { color ->
            CssHelper.convertToCssHexColor(SkinManager.getColor(color))
        }
        val aigcTextUiModeApplyCss = getAigcTextUiModeApplyCss(skinId)
        val tableCssParams = getTableCssParams(context, skinId)
        return SkinCssParams(
            titleSkinCss = titleCssParams,
            contentSkinCss = contentCssParams,
            checkBoxSkinCss = checkboxCssParams,
            webCardSkinCss = webCardCssParams,
            combinedCardCss = combinedCardCssParams,
            isSwitchToSystemFontFromThird = isSwitchToSystemFontFromThird,
            backgroundColorCss = backgroundColor,
            aigcTextUiModeApplyCss = aigcTextUiModeApplyCss,
            tableCss = tableCssParams
        )
    }

    private fun getCssAlignFromSkinConfig(align: String): String {
        return when (align) {
            "left" -> "start"
            "center" -> "center"
            "right" -> "end"
            else -> "start"
        }
    }

    /**
     * 获取文本的行高。
     * 参考 TextView中的getLineHeight()的实现。
     */
    private fun getLineHeight(typeFace: Typeface?, textSizeDp: Float, lineGapPx: Float, multi: Float): Int {
        val paint = Paint()
        typeFace?.let { paint.typeface = it }
        paint.textSize = SkinManager.getStableSize(textSizeDp)
        val lineHeight = paint.getFontMetricsInt(null) * multi + lineGapPx
        return FastMath.round(lineHeight)
    }

    private fun getTitleCssParams(
        skinId: String,
        titleConfig: Skin.EditPage.Title,
        titleBg: Skin.EditPage.Background.TitleBg
    ): SkinCssParams.TitleSkinCss {
        val textColor = SkinManager.getColor(titleConfig.textColor)
        val hintTextColor = ColorUtils.setAlphaComponent(textColor, ALPHA_30)
        val textSize = SkinManager.getTextSize(titleConfig.textSize)
        val lineHeight = getLineHeight(
            typeFace = null,
            textSizeDp = textSize,
            lineGapPx = SkinManager.getLineGap(titleConfig.lineGap),
            multi = TITLE_LINE_MULTI
        )

        /**
         * 若皮肤字体为空，先判断是否为三方字体
         * 若为三方字体，传入-1到WebView中
         * 否则传入获取到的字体值到WebView中
         */
        val titleFontPath =
            SkinManager.getFontRelativePath(skinId, titleConfig.font).takeIf { it != null } ?: if (isFlipFont) "-1" else "$fontVariationStatus"
        var titleBgPath = if (titleBg.value.isEmpty()) {
            ""
        } else {
            SkinManager.getRelativeUri(skinId, titleBg.value)
        }
        if (titleBgPath.startsWith(AndroidResourcePathHandler.ANDROID_RESOURCE_PREFIX)) {
            titleBgPath = titleBgPath.replace(AndroidResourcePathHandler.ANDROID_RESOURCE_PREFIX, AndroidResourcePathHandler.HANDLE_PATH_PREFIX)
        }
        return SkinCssParams.TitleSkinCss(
            align = getCssAlignFromSkinConfig(titleConfig.align),
            textColor = CssHelper.convertToCssHexColor(textColor),
            hintTextColor = CssHelper.convertToCssHexColor(hintTextColor),
            textSize = textSize.toString(),
            lineHeight = (lineHeight / SkinManager.getStableRatio()).toString(),
            paddingLeft = titleConfig.left,
            paddingTop = titleConfig.top,
            paddingRight = titleConfig.right,
            paddingBottom = titleConfig.bottom,
            fontPath = titleFontPath,
            bgType = titleBg.type,
            bgValue = titleBgPath
        )
    }

    private fun getContentCssParams(skinId: String, contentConfig: Skin.EditPage.Content): SkinCssParams.ContentSkinCss {
        val textColor = SkinManager.getColor(contentConfig.textColor)
        val textSize = SkinManager.getTextSize(contentConfig.textSize)
        val lineHeight = getLineHeight(
            typeFace = null,
            textSizeDp = textSize,
            lineGapPx = SkinManager.getLineGap(contentConfig.lineGap),
            multi = CONTENT_LINE_MULTI
        )
        val paddingTop = contentConfig.top.toIntOrNull() ?: 0

        /**
         * 若皮肤字体为空，先判断是否为三方字体
         * 若为三方字体，传入-1到WebView中
         * 否则传入获取到的字体值到WebView中
         */
        val contentFontPath =
            SkinManager.getFontRelativePath(skinId, contentConfig.font).takeIf { it != null } ?: if (isFlipFont) "-1" else "$fontVariationStatus"
        val contentPaddingTop = if (paddingTop == 0) {
            DEFAULT_CONTENT_PADDING_TOP
        } else {
            paddingTop
        }
        return SkinCssParams.ContentSkinCss(
            align = getCssAlignFromSkinConfig(contentConfig.align),
            textColor = CssHelper.convertToCssHexColor(textColor),
            textSize = textSize.toString(),
            lineHeight = (lineHeight / SkinManager.getStableRatio()).toString(),
            paddingLeft = contentConfig.left,
            paddingTop = contentPaddingTop.toString(),
            paddingRight = contentConfig.right,
            paddingBottom = contentConfig.bottom,
            fontPath = contentFontPath
        )
    }

    private fun getWebCardCssParams(context: Context): SkinCssParams.WebCardSkinCss {
        val backgroundColor = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorSurfaceWithCard)
        val borderColor = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorDivider)
        return SkinCssParams.WebCardSkinCss(
            bgColor = CssHelper.convertToCssHexColor(backgroundColor),
            borderColor = CssHelper.convertToCssHexColor(borderColor)
        )
    }

    private fun getCombinedCardCss(context: Context): CombinedCardCss {
        return CombinedCardCss(
            backgroundColor = CssHelper.convertToCssHexColor(context.getColor(R.color.voice_bar_bg)),
            backgroundColorChecked = CssHelper.convertToCssHexColor(context.getColor(R.color.voice_bar_bg_checked)),
            primaryTextColor = CssHelper.convertToCssHexColor(context.getColor(R.color.black)),
            secondaryTextColor = CssHelper.convertToCssHexColor(context.getColor(R.color.voice_bar_time)),
            contactCopyTextColor = CssHelper.convertToCssHexColor(context.getColor(com.support.appcompat.R.color.couiGreenTintControlNormal)),
            scheduleCopyTextColor = CssHelper.convertToCssHexColor(context.getColor(com.support.appcompat.R.color.couiBlueTintControlNormal)),
            contactButtonBackColor = CssHelper.convertToCssHexColor(context.getColor(com.support.appcompat.R.color.coui_color_secondary_green)),
            scheduleButtonBackColor = CssHelper.convertToCssHexColor(context.getColor(com.support.appcompat.R.color.coui_color_secondary_blue)),
            contactButtonTextColor = CssHelper.convertToCssHexColor(context.getColor(com.support.appcompat.R.color.coui_color_primary_text_green)),
            scheduleButtonTextColor = CssHelper.convertToCssHexColor(context.getColor(com.support.appcompat.R.color.coui_color_primary_text_blue))
        )
    }

    private fun getCheckBoxCssParams(
        context: Context,
        skinId: String,
        checkboxConfig: Skin.EditPage.Checkbox,
        contentTextColor: Int
    ): SkinCssParams.CheckBoxSkinCss? {
        kotlin.runCatching {
            return SkinCssParams.CheckBoxSkinCss(
                uncheckedCss = getUncheckCss(context, skinId, checkboxConfig.uncheck),
                checkedCss = getCheckCss(context, contentTextColor, skinId, checkboxConfig.check)
            )
        }.onFailure {
            AppLogger.BASIC.w(TAG, "getCheckBoxCss fail:${it.message}")
            if (it is FileNotFoundException) {
                throw FileNotFoundException("${it.message}")
            }
        }
        return null
    }

    private fun getUncheckCss(
        context: Context,
        skinId: String,
        uncheckConfig: Skin.EditPage.Checkbox.Uncheck
    ): SkinCssParams.CheckBoxSkinCss.UncheckedCss? {
        if (uncheckConfig.type == Skin.EditPage.Checkbox.Uncheck.TYPE_COLOR) {
            val fillColor = CssHelper.convertToCssHexColor(Color.TRANSPARENT)
            val strokeColorInt = if (skinId == SkinData.COLOR_SKIN_WHITE && DarkModeUtil.isDarkMode(context)) {
                ColorUtils.setAlphaComponent(Color.WHITE, ALPHA_40)
            } else {
                SkinManager.getColor(uncheckConfig.value)
            }
            val strokeColor = CssHelper.convertToCssHexColor(strokeColorInt)
            return SkinCssParams.CheckBoxSkinCss.UncheckedCss(
                pathColor = SkinCssParams.PathColor(fillColor = fillColor, strokeColor = strokeColor), drawableBase64Data = null
            )
        } else if (uncheckConfig.type == Skin.EditPage.Checkbox.Uncheck.TYPE_PICTURE) {
            val path = SkinManager.getUri(skinId, uncheckConfig.value)
            val bitmap = if (SkinManager.isEmbedSkin(skinId)) {
                context.contentResolver.openInputStream(Uri.parse(path))?.use {
                    BitmapFactory.decodeStream(it)
                }
            } else {
                if (File(path).exists()) {
                    BitmapFactory.decodeFile(path)
                } else {
                    throw FileNotFoundException("skin file:$path not exists!")
                }
            }
            if (bitmap != null) {
                val uncheckBase64Data = ByteArrayOutputStream().use {
                    bitmap.compress(Bitmap.CompressFormat.PNG, BITMAP_COMPRESS_QUALITY, it)
                    it.toByteArray()
                }.let {
                    "data:image/png;base64,${Base64.encodeToString(it, Base64.DEFAULT)}"
                }
                return SkinCssParams.CheckBoxSkinCss.UncheckedCss(
                    pathColor = null, drawableBase64Data = uncheckBase64Data
                )
            }
        }
        return null
    }

    private fun getCheckCss(
        context: Context,
        contentTextColor: Int,
        skinId: String,
        checkConfig: Skin.EditPage.Checkbox.Check
    ): SkinCssParams.CheckBoxSkinCss.CheckedCss? {
        if (checkConfig.type == Skin.EditPage.Checkbox.Check.TYPE_COLOR) {
            val strokeColor = CssHelper.convertToCssHexColor(Color.TRANSPARENT)
            val fillColorInt = if (skinId != SkinData.COLOR_SKIN_WHITE && !SkinData.isManualSkin(skinId)) {
                SkinManager.getColor(checkConfig.value)
            } else {
                COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary)
            }
            val fillColor = CssHelper.convertToCssHexColor(fillColorInt)
            val checkedTextColor = CssHelper.convertToCssHexColor(ColorUtils.setAlphaComponent(contentTextColor, ALPHA_30))
            return SkinCssParams.CheckBoxSkinCss.CheckedCss(
                pathColor = SkinCssParams.PathColor(fillColor = fillColor, strokeColor = strokeColor),
                drawableBase64Data = null,
                textColor = checkedTextColor
            )
        } else if (checkConfig.type == Skin.EditPage.Checkbox.Check.TYPE_PICTURE) {
            val path = SkinManager.getUri(skinId, checkConfig.value)
            val bitmap = if (SkinManager.isEmbedSkin(skinId)) {
                context.contentResolver.openInputStream(Uri.parse(path))?.use {
                    BitmapFactory.decodeStream(it)
                }
            } else {
                if (File(path).exists()) {
                    BitmapFactory.decodeFile(path)
                } else {
                    throw FileNotFoundException("skin file:$path not exists!")
                }
            }
            if (bitmap != null) {
                val checkBase64Data = ByteArrayOutputStream().use {
                    bitmap.compress(Bitmap.CompressFormat.PNG, BITMAP_COMPRESS_QUALITY, it)
                    it.toByteArray()
                }.let {
                    "data:image/png;base64,${Base64.encodeToString(it, Base64.DEFAULT)}"
                }
                val checkedTextColor = CssHelper.convertToCssHexColor(ColorUtils.setAlphaComponent(contentTextColor, ALPHA_30))
                return SkinCssParams.CheckBoxSkinCss.CheckedCss(
                    pathColor = null, drawableBase64Data = checkBase64Data, textColor = checkedTextColor
                )
            }
        }
        return null
    }

    /**
     *  根据皮肤，获取AigcText.vue内部元素属性亮暗模式
     *  @return
     */
    private fun getAigcTextUiModeApplyCss(skinId: String): Int {
        val uiMode = when (skinId) {
            // 正常适配亮暗色
            SkinData.COLOR_SKIN_WHITE -> if (DarkModeUtil.isDarkMode(context)) NIGHT_MODE_APPLY else LIGHT_MODE_APPLY
            // 只用暗色规则，亮色背景、内部元素/字体颜色用和暗色一样的。
            EditPageSkinRenderer.ONLINE_SKIN_4_ID -> NIGHT_MODE_APPLY
            // 只用亮色规则，暗色背景、内部元素/字体颜色用和亮色一样的
            else -> LIGHT_MODE_APPLY
        }
        AppLogger.BASIC.d(TAG, "getAigcTextUiModeApplyCss: ${if (uiMode == LIGHT_MODE_APPLY) "light-mode" else "night-mode"}")
        return uiMode
    }

    /**
     *  根据皮肤，获取详情页背景色
     *  @return
     */
    private fun getSkinBackgroundColor(skinId: String, configuration: Skin.EditPage): String? {
        var result: String? = null
        configuration.background.contentBg.apply {
            AppLogger.BASIC.d(TAG, "getSkinBackgroundColor: type=$type, skinId=$skinId, value=$value")
            when (type) {
                Skin.EditPage.Background.ContentBg.TYPE_PURE_COLOR, Skin.EditPage.Background.ContentBg.TYPE_GRADIENT_COLOR -> {
                    if (value.isNotEmpty()) {
                        result = value
                    }
                }

                Skin.EditPage.Background.ContentBg.TYPE_PICTURE -> {
                    val color = EditPageSkinRenderer.mOnlineSkinsBackground[skinId]?.backgroundColorCss
                    if (!color.isNullOrEmpty()) {
                        result = color
                    } else {
                        val isEmbedPersonalSkin = SkinData.getImageList().contains(skinId)
                        result = if (isEmbedPersonalSkin) WHITE else ""
                    }
                }
            }
        }
        AppLogger.BASIC.d(TAG, "getSkinBackgroundColor: result=$result")
        return result
    }

    /**
     *  在OS15及以上版本，对font variation进行转换。需要和设置模块的FontVariationUtils.java文件中方法实现保存一致！！！
     *  @param originalValue 从Settings中获取到的值
     *  @return 转换后的值
     */
    private fun parseFontVariationAfterOS15(originalValue: Int): Int {
        val originalRangeLength = (MAX_FONT_WEIGHT - SEEK_BAR_OFFSET).toDouble()
        val targetRangeLength = (MAX_FONT_WEIGHT_V - SEEK_BAR_OFFSET).toDouble()
        val scaleFactor = targetRangeLength / originalRangeLength

        val result = ceil((originalValue - SEEK_BAR_OFFSET) * scaleFactor + SEEK_BAR_OFFSET).toInt()
        AppLogger.BASIC.d(TAG, "parseFontVariationAfterOS15: originalValue=$originalValue, result=$result")
        return result
    }

    private fun getTableCssParams(context: Context, skinId: String): SkinCssParams.TableCss {
        val couiColorDividerColor = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorDivider)
        val couiColorLabelSecondaryVariantColor = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelSecondaryVariant)
        val couiColorLabelSecondaryColor = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelSecondary)
        val couiColorContainer16 = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorContainer16)
        val couiColorContainerTheme = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorContainerTheme)
        val couiColorBackground = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorBackground)
        val borderColor: String
        val dotColor: String
        val scrollBarColor: String
        val selectedColor: String = CssHelper.convertToCssHexColor(couiColorContainerTheme)
        var backgroundColor = ""
        when (skinId) {
            // 正常适配亮暗色
            SkinData.COLOR_SKIN_WHITE -> {
                if (DarkModeUtil.isDarkMode(context)) {
                    borderColor = CssHelper.convertToCssHexColor(couiColorLabelSecondaryVariantColor)
                    dotColor = CssHelper.convertToCssHexColor(couiColorLabelSecondaryVariantColor)
                    scrollBarColor = CssHelper.convertToCssHexColor(couiColorContainer16)
                } else {
                    borderColor = CssHelper.convertToCssHexColor(couiColorDividerColor)
                    dotColor = CssHelper.convertToCssHexColor(couiColorLabelSecondaryColor)
                    scrollBarColor = CssHelper.convertToCssHexColor(couiColorContainer16)
                }
            }
            // 只用暗色规则，边框颜色用和暗色一样的。
            EditPageSkinRenderer.ONLINE_SKIN_4_ID -> {
                borderColor = TABLE_BORDER_COLOR_IN_BLACK_RULE
                dotColor = TABLE_DOT_COLOR_IN_BLACK_RULE
                scrollBarColor = TABLE_SCROLL_BAR_COLOR_IN_BLACK_RULE
            }
            // 只用亮色规则，边框颜色用和亮色一样的。
            else -> {
                borderColor = TABLE_BORDER_COLOR_IN_LIGHT_RULE
                dotColor = TABLE_DOT_COLOR_IN_LIGHT_RULE
                scrollBarColor = TABLE_SCROLL_BAR_COLOR_IN_LIGHT_RULE
            }
        }

        when (skinId) {
            SkinData.COLOR_SKIN_HORIZON_LINE,
            SkinData.COLOR_SKIN_GRID_LINE,
            SkinData.COLOR_SKIN_GRID_DOT -> backgroundColor = CssHelper.convertToCssHexColor(couiColorBackground)
        }

        return SkinCssParams.TableCss(
            borderColor = borderColor,
            dotColor = dotColor,
            scrollBarColor = scrollBarColor,
            selectedColor = selectedColor,
            backgroundColor = backgroundColor,
        )
    }
}