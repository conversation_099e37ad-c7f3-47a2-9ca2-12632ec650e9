/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : WVAttachmentUtils.kt
 * Description    : WVAttachmentUtils.kt
 * Version        : 1.0
 * Date           : 2024/03/05
 * Author         : PengFei.Ma
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * PengFei.Ma     2024/03/05        1.0           create
 */
package com.nearme.note.activity.richedit.webview

import android.content.Context
import androidx.lifecycle.lifecycleScope
import com.google.gson.Gson
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.activity.richedit.NoteViewRichEditViewModel.Companion.TYPE_VOICE_NORMAL
import com.nearme.note.activity.richedit.NoteViewRichEditViewModel.Companion.TYPE_VOICE_PLAY
import com.nearme.note.activity.richedit.entity.Data
import com.nearme.note.activity.richedit.entity.RichData
import com.nearme.note.speech.utils.AudioFileUtil
import com.nearme.note.thirdlog.CombinedCardHelper.getTimeText
import com.oplus.note.R
import com.oplus.note.audioplayer.AudioPlayerManager
import com.oplus.note.data.CombinedCard
import com.oplus.note.external.MediaFileInfo
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.utils.DateAndTimeUtils
import com.oplus.notes.webview.container.api.ContactCardAttr
import com.oplus.notes.webview.container.api.FileCardData
import com.oplus.notes.webview.container.api.IWebViewContainer
import com.oplus.notes.webview.container.api.ImageInfo
import com.oplus.notes.webview.container.api.PaintAttr
import com.oplus.notes.webview.container.api.RecordAttr
import com.oplus.notes.webview.container.api.RecordAttrNew
import com.oplus.notes.webview.container.api.ScheduleCardAttr
import com.oplus.notes.webview.container.api.VideoData
import com.oplus.notes.webviewcoverpaint.container.api.IWebViewContainerCoverpaint
import com.oplusos.vfxsdk.doodleengine.PaintView
import kotlinx.coroutines.launch

object WVAttachmentUtils {
    @JvmStatic
    fun insertPaint(
        context: Context,
        paintAttachment: Attachment,
        picAttachment: Attachment?,
        webViewContainer: IWebViewContainer?,
        addToPrevGroup: Boolean = false,
        insertToEndInNonEditMode: Boolean = true,
    ) {
        webViewContainer?.let {
            val tips = context.getString(R.string.doodle_click_more)
            val picture = picAttachment?.picture
            val paintUrl = paintAttachment.relativePath()
            var needShowTips = false
            if (picture != null) {
                needShowTips = PaintView.isThereMore(picture.width, picture.height, paintAttachment.absolutePath(context)) == 1
            }
            val paintAttr = PaintAttr(
                paintId = paintAttachment.attachmentId,
                paintSrc = paintUrl,
                attachId = picAttachment?.attachmentId ?: "",
                imageSrc = picAttachment?.relativePath() ?: "",
                imageWidth = picture?.width ?: 0,
                imageHeight = picture?.height ?: 0,
                tips = tips,
                needShowTips = needShowTips,
            )
            it.insertPaint(paintAttr, addToPrevGroup, insertToEndInNonEditMode = insertToEndInNonEditMode)
        }
    }

    @JvmStatic
    fun insertImage(
        picAttachment: Attachment?,
        webViewContainer: IWebViewContainer?,
        isSrcExit: Boolean = true,
        placeholderRelativePath: String? = null,
        addToPrevGroup: Boolean = false,
        insertToEndInNonEditMode: Boolean = true,
        insertComplete: (() -> Unit)? = null
    ) {
        val imageInfo = ImageInfo(
            src = picAttachment?.relativePath() ?: "",
            attachId = picAttachment?.attachmentId ?: "",
            width = picAttachment?.picture?.width ?: 0,
            height = picAttachment?.picture?.height ?: 0,
            placeholderRelativePath = placeholderRelativePath,
            srcExist = isSrcExit,
        )
        webViewContainer?.insertImage(imageInfo, addToPrevGroup, insertToEndInNonEditMode = insertToEndInNonEditMode) {
            insertComplete?.invoke()
        }
    }
    @JvmStatic
    fun insertImageCoverPaint(
        picAttachment: Attachment?,
        webViewContainer: IWebViewContainerCoverpaint?,
        isSrcExit: Boolean = true,
        placeholderRelativePath: String? = null,
        addToPrevGroup: Boolean = false,
        insertToEndInNonEditMode: Boolean = true,
        insertComplete: (() -> Unit)? = null
    ) {
        val imageInfo = com.oplus.notes.webviewcoverpaint.container.api.ImageInfo(
            src = picAttachment?.relativePath() ?: "",
            attachId = picAttachment?.attachmentId ?: "",
            width = picAttachment?.picture?.width ?: 0,
            height = picAttachment?.picture?.height ?: 0,
            placeholderRelativePath = placeholderRelativePath,
            srcExist = isSrcExit,
        )
        webViewContainer?.insertImage(imageInfo, addToPrevGroup, insertToEndInNonEditMode = insertToEndInNonEditMode) {
            insertComplete?.invoke()
        }
    }

    @JvmStatic
    fun insertContactCard(
        context: Context,
        richData: RichData,
        data: Data,
        subAttachment: Attachment?,
        webViewContainer: IWebViewContainer?,
        addToPrevGroup: Boolean = false
    ) {
        val attachId = data.attachment?.attachmentId ?: return
        richData.speechLogInfo?.combinedCard?.apply {
            Gson().fromJson(this, CombinedCard::class.java)?.cardContacts?.forEach { contact ->
                if (contact.attId == subAttachment?.attachmentId) {
                    val contactCardAttr = ContactCardAttr(
                        attachId = attachId,
                        cardName = context.getString(com.oplus.note.baseres.R.string.contact),
                        copy = context.getString(com.oplus.note.baseres.R.string.card_copy),
                        name = contact.name ?: "",
                        phone = contact.phone ?: "",
                        position = contact.position ?: "",
                        company = contact.company ?: "",
                        call = context.getString(com.oplus.note.baseres.R.string.contact_call),
                        save = context.getString(com.oplus.note.baseres.R.string.contact_save),
                        picWidth = data.attachment?.picture?.width ?: 0,
                        picHeight = data.attachment?.picture?.height ?: 0,
                        src = data.attachment?.relativePath() ?: ""
                    )

                    webViewContainer?.insertContactCard(contactCardAttr, addToPrevGroup)
                }
            }
        }
    }

    @JvmStatic
    fun insertScheduleCard(
        context: Context,
        richData: RichData,
        data: Data,
        subAttachment: Attachment?,
        webViewContainer: IWebViewContainer?,
        addToPrevGroup: Boolean = false
    ) {
        val attachId = data.attachment?.attachmentId ?: return
        richData.speechLogInfo?.combinedCard?.apply {
            Gson().fromJson(this, CombinedCard::class.java)?.cardSchedules?.forEach { schedule ->
                if (schedule.attId == subAttachment?.attachmentId) {
                    val scheduleCardAttr = ScheduleCardAttr(
                        attachId = attachId,
                        cardName = context.getString(com.oplus.note.baseres.R.string.schedule),
                        copy = context.getString(com.oplus.note.baseres.R.string.card_copy),
                        name = schedule.event ?: "",
                        time = getTimeText(context, schedule),
                        address = schedule.location ?: "",
                        addEvent = context.getString(com.oplus.note.baseres.R.string.schedule_add),
                        navigate = context.getString(com.oplus.note.baseres.R.string.schedule_dest),
                        picWidth = data.attachment?.picture?.width ?: 0,
                        picHeight = data.attachment?.picture?.height ?: 0,
                        src = data.attachment?.relativePath() ?: ""
                    )
                    webViewContainer?.insertScheduleCard(scheduleCardAttr, addToPrevGroup)
                }
            }
        }
    }

    @JvmStatic
    fun insertRecordCard(
        fragment: WVNoteViewEditFragment,
        richData: RichData,
        picAttachment: Attachment?,
        subAttachment: Attachment,
        webViewContainer: IWebViewContainer?,
        isReadyState: Boolean = false,
        insertToEndInNonEditMode: Boolean = true,
        addToPrevGroup: Boolean = false
    ) {
        val attachId = picAttachment?.attachmentId ?: return
        val isSpeechAudio = subAttachment.type == Attachment.TYPE_SPEECH_AUDIO || subAttachment.type == Attachment.TYPE_IDENTIFY_VOICE
        val isAsrAudio = subAttachment.type == Attachment.TYPE_IDENTIFY_VOICE
        val asrAttachId = if (subAttachment.type == Attachment.TYPE_IDENTIFY_VOICE) {
            subAttachment.extra?.asrAttachId ?: ""
        } else ""
        val palyState = if (isReadyState) {
            TYPE_VOICE_NORMAL
        } else {
            val isPlaying = AudioPlayerManager.isPlaying(fragment.audioPlayViewModel.uuid)
            if (isPlaying) TYPE_VOICE_PLAY else TYPE_VOICE_NORMAL
        }
        fragment.lifecycleScope.launch {
            initAudioAttachment(fragment, richData, subAttachment) { audioName, info ->
                val recordAttr = RecordAttr(
                    src = picAttachment.relativePath(),
                    attachId = attachId,
                    title = audioName,
                    hasCallLogs = info.hasCallLogs,
                    hasMarks = info.hasMarks,
                    currentTime = info.currentTime,
                    duration = info.duration,
                    recordId = subAttachment.attachmentId,
                    picWidth = picAttachment.picture?.width ?: 0,
                    picHeight = picAttachment.picture?.height ?: 0,
                    isSpeechAudio = isSpeechAudio,
                    isAsrAudio = isAsrAudio,
                    asrAttachId = asrAttachId,
                    state = palyState
                )
                webViewContainer?.insertRecordCard(
                    recordAttr, addToPrevGroup, insertToEndInNonEditMode = insertToEndInNonEditMode
                )
            }
        }
    }

    @JvmStatic
    fun saveRecorderCard(
        fragment: WVNoteViewEditFragment,
        picAttachment: Attachment?,
        subAttachment: Attachment,
    ): RecordAttrNew? {
        val attachId = picAttachment?.attachmentId ?: return null
        val isSpeechAudio = true
        val isAsrAudio = true
        val asrAttachId = subAttachment.extra?.asrAttachId ?: ""
        val audioDuration = subAttachment.extra?.audioDuration ?: 0
        AppLogger.BASIC.d(TAG, "audioDuration:$audioDuration")
        val palyState = TYPE_VOICE_NORMAL
        val title = fragment.resources.getString(com.oplus.note.baseres.R.string.speech_record_audio_content)
        val hasCallLogs = fragment.context?.let {
            hasThirdLog(subAttachment.getAsrAttachmentPath(it))
        } ?: false

        val recordAttr = RecordAttrNew(
            src = picAttachment.relativePath(),
            attachid = attachId,
            title = title,
            hasCallLogs = hasCallLogs,
            hasMarks = false,
            currentTime = 0,
            duration = audioDuration,
            recordid = subAttachment.attachmentId,
            picwidth = picAttachment.picture?.width ?: 0,
            picheight = picAttachment.picture?.height ?: 0,
            isSpeechAudio = isSpeechAudio,
            isAsrAudio = isAsrAudio,
            asrAttachId = asrAttachId,
            state = palyState,
        )
        return recordAttr
    }

    @JvmStatic
    fun insertFileCard(
        fragment: WVNoteViewEditFragment?,
        subAttachment: Attachment?,
        webViewContainer: IWebViewContainer?,
        addToPrevGroup: Boolean = false,
        thumbnail: String,
        insertToEndInNonEditMode: Boolean = true,
        insertBlock: (() -> Unit)? = null
    ) {
        val ctx = fragment?.context ?: return
        val attachId = subAttachment?.subAttachment?.associateAttachmentId ?: ""
        val fileCard = FileCardData(
            "/${subAttachment?.richNoteId}/${attachId}_thumb.png",
            attachId,
            subAttachment?.attachmentId ?: "",
            subAttachment?.fileName,
            subAttachment?.absolutePath(appContext),
            subAttachment?.getFileSize(appContext) ?: 0L,
            DateAndTimeUtils.timeInMillis2Date(ctx, AudioFileUtil.getFileModifiedTime(subAttachment?.absolutePath(ctx)), true),
            subAttachment?.type.toString(),
            subAttachment?.url,
            docThumbnail = thumbnail
        )
        webViewContainer?.insertFileCard(fileCard, addToPrevGroup, insertToEndInNonEditMode) {
            insertBlock?.invoke()
        }
    }

    @JvmStatic
    fun updateFileCardThumbnail(
        subAttachment: Attachment?,
        thumbnail: String,
        webViewContainer: IWebViewContainer?
    ) {
        webViewContainer?.updateDocThumbnail(
            subAttachment?.subAttachment?.associateAttachmentId ?: "",
            subAttachment?.attachmentId ?: "",
            thumbnail
        ) {
        }
    }

    @Suppress("LongParameterList")
    @JvmStatic
    fun insertVideoPlaceHolder(
        picAttachment: Attachment?,
        webViewContainer: IWebViewContainer?,
        addToPrevGroup: Boolean,
        coverPath: String,
        fileInfo: MediaFileInfo,
        duration: Long,
        insertToEndInNonEditMode: Boolean = true,
        insertComplete: (() -> Unit)? = null
    ) {
        //视频文件首帧图作为附件ID
        val attachId = picAttachment?.attachmentId ?: ""
        val videoData = VideoData(
            picSrc = coverPath,
            videoSrc = null,
            attachId = attachId,
            videoAttachId = "",
            width = picAttachment?.picture?.width ?: 0,
            height = picAttachment?.picture?.height ?: 0,
            addToNoteTime = "",
            timeStamp = 0L,
            fileName = "",
            fileSize = fileInfo.fileSize,
            duration = duration,
        )
        webViewContainer?.insertVideoPlaceHolder(videoData, addToPrevGroup, insertToEndInNonEditMode) {
            insertComplete?.invoke()
        }
    }

    @Suppress("LongParameterList")
    @JvmStatic
    fun updateVideo(
        context: Context,
        picAttachment: Attachment?,
        subAttachment: Attachment?,
        duration: Long,
        webViewContainer: IWebViewContainer?,
        addToPrevGroup: Boolean = false,
        insertToEndInNonEditMode: Boolean = true,
        insertComplete: (() -> Unit)? = null,
    ) {
        //视频文件首帧图作为附件ID
        val attachId = subAttachment?.subAttachment?.associateAttachmentId ?: ""
        val videoData = VideoData(
            picSrc = picAttachment?.relativePath(),
            videoSrc = subAttachment?.relativePath(),
            attachId = attachId,
            videoAttachId = subAttachment?.attachmentId ?: "",
            width = picAttachment?.picture?.width ?: 0,
            height = picAttachment?.picture?.height ?: 0,
            addToNoteTime = DateAndTimeUtils.timeInMillis2Date(context, subAttachment?.extra?.createTime ?: 0L, true),
            timeStamp = subAttachment?.extra?.createTime ?: 0L,
            fileName = subAttachment?.fileName ?: "",
            fileSize = subAttachment?.getFileSize(context) ?: 0L,
            duration = duration,
        )
        webViewContainer?.updateVideo(videoData, addToPrevGroup, insertToEndInNonEditMode) {
            insertComplete?.invoke()
        }
    }


    @JvmStatic
    fun insertExternalAudioCard(
        context: Context?,
        picAttachment: Attachment,
        subAttachment: Attachment,
        voiceFileDuration: Long,
        webViewContainer: IWebViewContainer?,
        addToPrevGroup: Boolean = false,
        insertToEndInNonEditMode: Boolean = true
    ) {
        val attachId = picAttachment.attachmentId
        val playState = TYPE_VOICE_NORMAL
        val recordAttr = RecordAttr(
            src = picAttachment.relativePath(),
            attachId = attachId,
            title = subAttachment.fileName ?: "",
            hasCallLogs = false,
            hasMarks = false,
            currentTime = 0,
            duration = voiceFileDuration,
            recordId = subAttachment.attachmentId,
            picWidth = picAttachment.picture?.width ?: 0,
            picHeight = picAttachment.picture?.height ?: 0,
            isSpeechAudio = false,
            isAsrAudio = false,
            asrAttachId = "",
            state = playState,
            fileSize = context?.let { subAttachment.getFileSize(it) } ?: 0,
            fileDate = DateAndTimeUtils.timeInMillis2Date(context, subAttachment.extra?.createTime ?: 0L, true),
            fileName = subAttachment.fileName ?: "",
            isExternal = true
        )
        webViewContainer?.insertRecordCard(recordAttr, addToPrevGroup, insertToEndInNonEditMode)
    }
}