/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - AIGCTextMenuPreferenceFragment
 ** Description:
 **         v1.0:   Create AIGCTextMenuPreferenceFragment file
 **
 ** Version: 1.0
 ** Date: 2024/05/09
 ** Author: Ji<PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2024/5/9   1.0      Create this module
 ********************************************************************************/
package com.nearme.note.activity.richedit.aigc.panel

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.preference.Preference
import androidx.recyclerview.widget.COUIPanelPreferenceLinearLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnScrollListener
import com.coui.appcompat.preference.COUIPreferenceFragment
import com.nearme.note.aigc.panel.AIGCTwoMenuPreference
import com.oplus.note.R
import com.oplus.note.logger.AppLogger

class AIGCTextMenuPreferenceFragment : COUIPreferenceFragment() {

    var listener: Preference.OnPreferenceClickListener? = null
    var outDividerView: View? = null
    var outBottomFadeView: View? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        (view as ViewGroup).removeView(view.findViewById<View>(R.id.appbar_layout))
        listView.apply {
            clipToPadding = true
            isVerticalScrollBarEnabled = false
            addOnScrollListener(object : OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    val firstItem = (layoutManager as? LinearLayoutManager)?.findFirstCompletelyVisibleItemPosition() ?: 0
                    outDividerView?.visibility = if (firstItem > 0) View.VISIBLE else View.INVISIBLE
                    val lastItem = (layoutManager as? LinearLayoutManager)?.findLastCompletelyVisibleItemPosition() ?: 0
                    val itemCount = adapter?.itemCount ?: 0
                    outBottomFadeView?.visibility = if (lastItem == itemCount - 1) View.INVISIBLE else View.VISIBLE
                }
            })
        }
        return view
    }

    override fun onCreateRecyclerView(
        inflater: LayoutInflater,
        parent: ViewGroup,
        savedInstanceState: Bundle?
    ): RecyclerView {
        return super.onCreateRecyclerView(inflater, parent, savedInstanceState)
            .apply {
                layoutManager = COUIPanelPreferenceLinearLayoutManager(context)
            }
    }

    override fun onCreatePreferences(
        savedInstanceState: Bundle?,
        rootKey: String?
    ) {
        setPreferencesFromResource(R.xml.preference_aigc_text_menu, rootKey)
        findPreference<AIGCTwoMenuPreference>(KEY_TWO_MENU)?.apply {
            onPreferenceClickListener = listener
        }
        findPreference<Preference>(getString(com.oplus.note.baseres.R.string.action_polish))?.apply {
            onPreferenceClickListener = listener
        }
        findPreference<Preference>(getString(com.oplus.note.baseres.R.string.action_extend_writing))?.apply {
            onPreferenceClickListener = listener
        }
        findPreference<Preference>(getString(com.oplus.note.baseres.R.string.action_composition))?.apply {
            onPreferenceClickListener = listener
        }
        findPreference<Preference>(getString(com.oplus.note.baseres.R.string.action_extend_page))?.apply {
            onPreferenceClickListener = listener
        }
        findPreference<Preference>(getString(com.oplus.note.baseres.R.string.action_reduce_page))?.apply {
            onPreferenceClickListener = listener
        }
        findPreference<Preference>(getString(com.oplus.note.baseres.R.string.action_more_colloquially))?.apply {
            onPreferenceClickListener = listener
        }
        findPreference<Preference>(getString(com.oplus.note.baseres.R.string.action_more_formally))?.apply {
            onPreferenceClickListener = listener
        }
        findPreference<Preference>(getString(com.oplus.note.baseres.R.string.action_organise))?.apply {
            onPreferenceClickListener = listener
        }
        AppLogger.DEBUG.d(TAG) { "onCreatePreferences listener:$listener" }
    }

    companion object {
        private const val TAG = "AIGCTextMenuPreferenceFragment"
        private const val KEY_TWO_MENU = "key_two_menu"
    }
}