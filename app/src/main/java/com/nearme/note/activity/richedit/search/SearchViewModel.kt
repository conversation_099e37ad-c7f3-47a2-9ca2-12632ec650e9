/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - SearchViewModel.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  yanglinlong       2023/5/17      1.0     create file
 ****************************************************************/
package com.nearme.note.activity.richedit.search

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.nearme.note.activity.richedit.entity.Data
import com.nearme.note.activity.richedit.entity.RichData
import com.oplus.note.data.third.ThirdLogParagraph
import com.oplus.note.logger.AppLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

class SearchViewModel : ViewModel() {

    val searchResult = MutableLiveData<SearchResult?>()

    /**将标题和内容放到一个 list 中搜索，和 Adapter 的 position对应，方便后续处理*/
    val searchDataList = ArrayList<Data>()
    var positionIndex: Int = -1

    fun hasSearchResult(): Boolean {
        return searchResult.value != null && searchResult.value?.resultCount!! > 0
    }

    fun searchRichData(searchList: MutableList<String>, richData: RichData?) {
        if (richData == null) {
            return
        }
        var result = searchResult.value
        if (result == null) {
            result = SearchResult()
        } else {
            result.reset()
        }
        if (searchList.isNotEmpty()) {
            result.searchList = searchList
            GlobalScope.launch(Dispatchers.IO) {
                val start = System.currentTimeMillis()
                searchTitleAndContent(result, richData)
                AppLogger.BASIC.d("SearchOperationController", "search cost time ${System.currentTimeMillis() - start}")
                searchResult.postValue(result)
            }
        } else {
            searchResult.value = result
        }
    }

    /** 搜索内容，将内容放到一个 list 中搜索，和 Adapter 的 position对应，方便后续处理 */
    fun searchThirdLogData(searchList: MutableList<String>?, thirdLogs: List<ThirdLogParagraph>?) {
        if (searchList.isNullOrEmpty() || thirdLogs == null) {
            return
        }
        val result = SearchResult()
        if (searchList.isNotEmpty()) {
            result.searchList = searchList
            GlobalScope.launch(Dispatchers.IO) {
                searchContent(result, thirdLogs)
                searchResult.postValue(result)
            }
        } else {
            searchResult.value = result
        }
    }

    /** 搜索列表内容处理记录*/
    private fun searchContent(result: SearchResult, thirdLogs: List<ThirdLogParagraph>) {
        thirdLogs.forEachIndexed { index, data ->
            result.searchList.forEach { searchTxt ->
                val paragraph = data.paragraph
                if (TextUtils.isEmpty(paragraph)) {
                    return@forEachIndexed
                }
                var i = 0
                var list: MutableList<Pair<Int, Int>>? = result.contentResultMap[index]
                while (i < (paragraph.length)) {
                    val indexStr = paragraph.indexOf(searchTxt, i, true)
                    if (indexStr == -1) {
                        break
                    } else {
                        if (positionIndex == -1) positionIndex = index
                        if (list == null) {
                            result.resultPositionList.add(index)
                            list = ArrayList()
                            result.contentResultMap[index] = list
                        }
                        result.resultCount++
                        i = indexStr + searchTxt.length
                        list.add(Pair(indexStr, i))
                    }
                }
            }
        }
    }

    /** 搜索标题和内容，将标题和内容放到一个 list 中搜索，和 Adapter 的 position对应，方便后续处理 */
    private fun searchTitleAndContent(result: SearchResult, richData: RichData) {
        searchDataList.apply {
            clear()
            add(richData.title)
            addAll(richData.items)
        }
        searchDataList.forEachIndexed { index, data ->
            result.searchList.forEach { searchTxt ->
                if (data.type != Data.TYPE_TEXT || TextUtils.isEmpty(data.text)) {
                    return@forEachIndexed
                }
                var i = 0
                var list: MutableList<Pair<Int, Int>>? = result.contentResultMap[index]
                while (i < (data.text?.length ?: 0)) {
                    val indexStr = data.text?.indexOf(searchTxt, i, true) ?: -1
                    if (indexStr == -1) {
                        break
                    } else {
                        if (list == null) {
                            result.resultPositionList.add(index)
                            list = ArrayList()
                            result.contentResultMap[index] = list
                        }
                        result.resultCount++
                        i = indexStr + searchTxt.length
                        list.add(Pair(indexStr, i))
                    }
                }
            }
        }
    }

    fun cancelSearch() {
        searchResult.value = null
    }

    /**
     * @return true 上下切换成功，false 上下切换失败
     * */
    fun showCurrentSelectSearchResult(previous: Boolean?): Boolean {
        searchResult.value?.apply {
            // 没有搜到结果
            if (resultCount <= 0 || resultPositionList.isEmpty()) {
                return false
            }
            if (previous == true) {
                // 点击<
                if (resultIndex == 0) {
                    // 当前是第一个搜索结果，跳转到最后一个
                    resultIndex = resultCount - 1
                    indexOfPositionList = resultPositionList.size - 1
                    val lastContentResultList = contentResultMap[resultPositionList.last()]
                    indexOfMapEachList = if (!lastContentResultList.isNullOrEmpty()) {
                        lastContentResultList.size - 1
                    } else {
                        0
                    }
                    return true
                } else {
                    resultIndex--
                    indexOfMapEachList--
                }
            } else if (previous == false) {
                // 点击>
                if (resultIndex == resultCount - 1) {
                    // 当前是最后一个搜索结果，跳转到第一个
                    resultIndex = 0
                    indexOfPositionList = 0
                    indexOfMapEachList = 0
                    return true
                } else {
                    resultIndex++
                    indexOfMapEachList++
                }
            }
            if (indexOfMapEachList < 0) {
                indexOfPositionList--
                val key = resultPositionList.getOrNull(indexOfPositionList)
                indexOfMapEachList = if (contentResultMap.containsKey(key)) {
                    (contentResultMap[resultPositionList.getOrNull(indexOfPositionList)]?.size ?: 1) - 1
                } else {
                    -1
                }
            } else if (indexOfMapEachList >= (contentResultMap[resultPositionList[indexOfPositionList]]?.size ?: 0)) {
                indexOfPositionList++
                indexOfMapEachList = 0
            }
            return true
        }
        return false
    }
}