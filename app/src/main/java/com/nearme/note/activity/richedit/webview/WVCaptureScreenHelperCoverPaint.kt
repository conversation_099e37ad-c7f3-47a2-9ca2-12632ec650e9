/**
 * Copyright (C), 2010-2030, OPLUS Mobile Comm Corp., Ltd.
 * File           : WVCaptureScreenHelper.kt
 * Description    : description
 * Version        : 1.0
 * Date           : 2024/2/27
 * Author         : XinYang.Hu
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * XinYang.Hu     2024/2/27        1.0           create
 */
package com.nearme.note.activity.richedit.webview

import android.graphics.Bitmap
import android.graphics.Bitmap.Config
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.PorterDuff
import android.text.TextUtils
import android.view.View
import com.heytap.tbl.webkit.WebView
import com.nearme.note.MyApplication
import com.nearme.note.paint.coverdoodle.CoverDoodlePresenter
import com.nearme.note.paint.coverdoodle.CoverDoodlePresenterCoverPaint
import com.nearme.note.util.ConfigUtils
import com.nearme.note.view.helper.UiHelper
import com.oplus.note.logger.AppLogger
import com.oplus.notes.webviewcoverpaint.container.api.IWebViewContainerCoverpaint
import com.oplus.richtext.editor.view.CoverPaintView
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.async
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import java.io.File

class WVCaptureScreenHelperCoverPaint {
    companion object {
        private const val TAG = "WVCaptureScreenHelper"
        private const val BITMAP_PRODUCER_DELAY_TIME = 500L
        private const val BITMAP_COMPRESS_QUALITY = 100
    }

    private val cpuCores = Runtime.getRuntime().availableProcessors()
    private var bitmapSaverChannel: Channel<Pair<Bitmap, String>>? = null
    private val webViewCaptureMutex = Mutex()
    private var isWebViewCaptureRunning = false
    private var coverDoodlePresenter: CoverDoodlePresenterCoverPaint? = null

    private val capturePath: String by lazy {
        val context = MyApplication.appContext
        var newPackagePath = context.filesDir.parent
        if (TextUtils.isEmpty(newPackagePath)) {
            // In case of return null, hardcode data/data
            newPackagePath = "/data/data/" + context.packageName
        }
        "$newPackagePath/capture/"
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private fun initBitmapSaverChannel() {
        if (bitmapSaverChannel == null || bitmapSaverChannel!!.isClosedForSend || bitmapSaverChannel!!.isClosedForReceive) {
            bitmapSaverChannel = Channel(Channel.BUFFERED)
        }
    }

    private fun startBitmapSaverConsumer(scope: CoroutineScope) = scope.launch(Dispatchers.Default) {
        try {
            while (true) {
                bitmapSaverChannel?.receive()?.let { data ->
                    val bitmap = data.first
                    val savePath = data.second
                    saveBitmap(bitmap, savePath)
                }
            }
        } catch (e: CaptureSuccessCause) {
            // do nothing
        }
    }

    private fun startBitmapSaverProducer(
        scope: CoroutineScope,
        webView: WebView,
        titleHeight: Int,
        contentHeight: Int,
        isTitleEmpty: Boolean,
        paintView: CoverPaintView?
    ) = scope.async {
        if (webView.scrollY != 0) {
            webView.scrollTo(0, 0)
            delay(BITMAP_PRODUCER_DELAY_TIME)
        }
        val onePageMaxHeight = webView.measuredHeight
        val webViewWidth = webView.measuredWidth
        val totalHeight = titleHeight + contentHeight
        var remainingHeight = totalHeight
        var captureIndex = 0
        var captureScrollY = 0
        val canvas = Canvas()
        val restoreValue = canvas.save()
        AppLogger.BASIC.d(TAG, "startBitmapSaverProducer measuredHeight=${onePageMaxHeight} webViewWidth=$webViewWidth")
        while (remainingHeight > 0) {
            webViewCaptureMutex.withLock {
                AppLogger.BASIC.d(TAG, "captureIndex:$captureIndex")
                if (captureScrollY != webView.scrollY) {
                    /*
                    当 webView 内容超过10页时，概率性会出现在截取第二屏时，页面跳到底部，导致部分截图失败出现空白。
                    跳转到底部的原因是 webkit 回调到overScrollBy接口中的参数异常，参数为webkit中传递过来，暂时未找到根本原因, 先在此处进行 scrollY 校准
                    */
                    AppLogger.BASIC.w(TAG, "captureScrollY not match! captureScrollY:$captureScrollY, viewScrollY:${webView.scrollY}")
                    webView.scrollTo(0, captureScrollY)
                    delay(BITMAP_PRODUCER_DELAY_TIME)
                }
                val needCutTitle = (captureIndex == 0) && isTitleEmpty && (paintView?.isStrokeEmpty ?: true)
                val supportOverlayPaint = ConfigUtils.isSupportOverlayPaint
                val needClearTitle = (captureIndex == 0) && isTitleEmpty && supportOverlayPaint
                val currCaptureHeight = remainingHeight.coerceAtMost(onePageMaxHeight)
                val bitmapHeight = if (needCutTitle) {
                    // 当不需要绘制标题区域时，bitmap高度也减去标题区域的高度
                    currCaptureHeight - titleHeight
                } else {
                    currCaptureHeight
                }
                val bitmap = if (webViewWidth > 0 && bitmapHeight > 0) {
                    Bitmap.createBitmap(webViewWidth, bitmapHeight, Config.ARGB_8888)
                } else {
                    null
                }
                bitmap?.let {
                    canvas.setBitmap(it)
                }
                canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)
                val dy = if (needCutTitle) {
                    // 不绘制标题区域的内容
                    -titleHeight.toFloat()
                } else {
                    -(totalHeight - remainingHeight).toFloat()
                }
                canvas.translate(0f, dy)
                webView.draw(canvas)
                canvas.restoreToCount(restoreValue)
                if (needClearTitle) {
                    // 叠图模式下，需要保留标题栏区域，但是 webview 在draw时会把默认标题给绘制上去，因此需要将标题栏区域的内容清除。
                    canvas.clipRect(0, 0, webViewWidth, titleHeight)
                    canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)
                }
                val savePath = "${capturePath}capture$captureIndex.png"
                bitmap?.let {
                    bitmapSaverChannel?.send(Pair(it, savePath))
                }
                captureIndex++
                remainingHeight -= currCaptureHeight
                captureScrollY += remainingHeight.coerceAtMost(currCaptureHeight)
                webView.scrollTo(0, captureScrollY)
            }
            delay(BITMAP_PRODUCER_DELAY_TIME)
        }
        bitmapSaverChannel?.close(CaptureSuccessCause())
        return@async captureIndex
    }

    private suspend fun saveBitmap(bitmap: Bitmap, savePath: String) = withContext(Dispatchers.IO) {
        kotlin.runCatching {
            val file = File(savePath)
            file.createNewFile()
            file.outputStream().use {
                bitmap.compress(Bitmap.CompressFormat.PNG, BITMAP_COMPRESS_QUALITY, it)
            }
        }.onFailure {
            AppLogger.BASIC.w(TAG, "saveBitmap fail! err:${it.message}")
        }
    }

    private fun clearCapturePath() {
        kotlin.runCatching {
            File(capturePath).run {
                if (!exists()) {
                    mkdirs()
                } else {
                    listFiles()?.forEach {
                        it?.deleteRecursively()
                    }
                }
            }
        }
    }

    fun captureCurrentScreen(view: View?): Bitmap? {
        if (view == null) {
            return null
        }
        view.isDrawingCacheEnabled = true
        view.buildDrawingCache()
        if (view.drawingCache == null) {
            return null
        }
        val bitmap = Bitmap.createBitmap(view.drawingCache, 0, 0, view.measuredWidth, view.measuredHeight)
        view.isDrawingCacheEnabled = false
        return bitmap
    }

    suspend fun pauseCaptureWebView() {
        kotlin.runCatching {
            if (isWebViewCaptureRunning) {
                webViewCaptureMutex.lock()
                AppLogger.BASIC.d(TAG, "pauseCaptureWebView")
            }
        }.onFailure {
            AppLogger.BASIC.w(TAG, "pauseCaptureWebView fail! err:${it.message}")
        }
    }

    fun resumeCaptureWebView() {
        kotlin.runCatching {
            if (isWebViewCaptureRunning && webViewCaptureMutex.isLocked) {
                webViewCaptureMutex.unlock()
                AppLogger.BASIC.d(TAG, "resumeCaptureWebView")
            }
        }.onFailure {
            AppLogger.BASIC.w(TAG, "resumeCaptureWebView fail! err:${it.message}")
        }
    }

    @Suppress("TooGenericExceptionCaught")
    fun captureWebView(
        doodlePresenter: CoverDoodlePresenterCoverPaint?,
        webViewContainer: IWebViewContainerCoverpaint,
        webView: WebView?,
        isTitleEmpty: Boolean,
        paintView: CoverPaintView?,
        callback: CaptureCallback?
    ) {
        if (webView == null || doodlePresenter == null) {
            return
        }
        coverDoodlePresenter = doodlePresenter
        isWebViewCaptureRunning = true
        AppLogger.BASIC.i(TAG, "capture start")
        callback?.onCaptureStart { scope ->
            doCaptureWebView(scope, webViewContainer, webView, isTitleEmpty, paintView, callback)
        }
    }

    private fun doCaptureWebView(
        scope: CoroutineScope,
        webViewContainer: IWebViewContainerCoverpaint,
        webView: WebView,
        isTitleEmpty: Boolean,
        paintView: CoverPaintView?,
        callback: CaptureCallback?
    ) {
        scope.launch {
            try {
                val editorsHeight = coverDoodlePresenter?.getTitleAndContentEditorHeight(webViewContainer, webView.scale)
                val titleHeight = editorsHeight?.first ?: 0
                val contentHeight = editorsHeight?.second ?: 0
                AppLogger.BASIC.d(TAG, "doCaptureWebView editorsHeight=$editorsHeight")
                /*
                截屏时需要禁用图片的淡入淡出动画，否则会截到模糊图；截屏完成后需恢复动画。
                禁用动画需要放在getTitleAndContentEditorHeight之后，否则会导致getTitleAndContentEditorHeight获取的值异常，原因可能是禁用过程中导致属性变化
                 */
                webViewContainer.enableImageAnimation(false)
                clearCapturePath()
                initBitmapSaverChannel()
                val consumerJobs = List(cpuCores) { startBitmapSaverConsumer(scope) }
                val producerJob = startBitmapSaverProducer(scope, webView, titleHeight, contentHeight, isTitleEmpty, paintView)
                val result = producerJob.await()
                consumerJobs.forEach {
                    it.join()
                }
                AppLogger.BASIC.i(TAG, "capture end, result:$result")
                // 截屏完成后，webView需滚动到顶部. 否则会出现返回到上一页面时，页面内容滚动，涂鸦不滚动的问题。bugId:7061376
                webView.scrollTo(0, 0)
                callback?.onCaptureEnd(result)
            } catch (e: CaptureCancelCause) {
                AppLogger.BASIC.w(TAG, "captureWebView user cancel capture")
                // 截屏完成后，webView需滚动到顶部. 否则会出现返回到上一页面时，页面内容滚动，涂鸦不滚动的问题。bugId:7061376
                webView.scrollTo(0, 0)
                callback?.onCaptureCancel()
            } catch (e: Exception) {
                AppLogger.BASIC.w(TAG, "captureWebView exception:${e.message}")
                // 截屏完成后，webView需滚动到顶部. 否则会出现返回到上一页面时，页面内容滚动，涂鸦不滚动的问题。bugId:7061376
                webView.scrollTo(0, 0)
                callback?.onCaptureError(e)
            } finally {
                callback?.onCaptureFinish()
                webViewContainer.enableImageAnimation(true)
                isWebViewCaptureRunning = false
            }
        }
    }

    fun cancelCapture() {
        if (isWebViewCaptureRunning) {
            bitmapSaverChannel?.cancel(CaptureCancelCause())
        }
    }

    fun isWebViewCapturing(): Boolean {
        return isWebViewCaptureRunning
    }

    suspend fun saveTableScreenShotBitmap(bitmap: Bitmap) {
        val savePath = "${capturePath}capture0.png"
        clearCapturePath()
        saveBitmap(bitmap, savePath)
    }

    inner class CaptureCancelCause : CancellationException("Capture cancel by user")

    inner class CaptureSuccessCause : Throwable("Capture success")

    interface CaptureCallback {
        fun onCaptureStart(callback: (scope: CoroutineScope) -> Unit)
        fun onCaptureEnd(captureCount: Int)
        fun onCaptureCancel()
        fun onCaptureError(e: Exception)
        fun onCaptureFinish()
    }
}