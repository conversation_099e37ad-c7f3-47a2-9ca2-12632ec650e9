package com.nearme.note.activity.richedit

import android.app.Activity
import android.appwidget.AppWidgetManager
import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.util.Log
import android.widget.Toast
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.nearme.note.MyApplication
import com.nearme.note.activity.edit.NoteViewEditActivity
import com.nearme.note.activity.notebook.NoteBookViewModel
import com.nearme.note.activity.richedit.TransparentActivity.Companion.PROCESS_TEXT_KEY
import com.nearme.note.activity.richedit.entity.Data
import com.nearme.note.activity.richedit.entity.RichData
import com.nearme.note.activity.richedit.entity.countOfAudioVideoAndDoc
import com.nearme.note.activity.richedit.entity.findIdentifyVoiceItems
import com.nearme.note.activity.richedit.entity.findSpeechAudioItem
import com.nearme.note.activity.richedit.entity.findVoiceItems
import com.nearme.note.activity.richedit.entity.getContentCount
import com.nearme.note.activity.richedit.entity.hasTableTag
import com.nearme.note.activity.richedit.entity.isContentEmpty
import com.nearme.note.activity.richedit.entity.isEmpty
import com.nearme.note.activity.richedit.entity.reachImageLimit
import com.nearme.note.activity.richedit.thirdlog.NoteBinder
import com.nearme.note.activity.richedit.webview.WVNoteViewEditFragment
import com.nearme.note.activity.richedit.webview.skin.SkinChange
import com.nearme.note.appwidget.WidgetUtils
import com.nearme.note.appwidget.notewidget.NoteAppWidgetViewModel
import com.nearme.note.appwidget.notewidget.NoteWidgetInfoMap
import com.nearme.note.appwidget.notewidget.NoteWidgetProvider
import com.nearme.note.cardwidget.provider.NoteCardWidgetProvider
import com.nearme.note.common.Constants
import com.nearme.note.common.feedbacklog.FeedbackLog
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.DeleteFolderCacheHolder
import com.nearme.note.db.FolderUtil
import com.nearme.note.db.NoteInfoDBUtil
import com.nearme.note.db.NotesProvider
import com.nearme.note.db.NotesProviderPresenter
import com.nearme.note.db.PresetNoteUtils
import com.nearme.note.external.ExternalCreateNoteManager
import com.nearme.note.external.InCallUINoteManager
import com.nearme.note.external.OcrScannerManager
import com.nearme.note.external.action.ExternalFileStatisticUtils
import com.nearme.note.logic.NoteSyncProcess
import com.nearme.note.logic.RecoverFolderHelper.getToastMessage
import com.nearme.note.logic.ThumbFileManager
import com.nearme.note.main.MainActivity
import com.nearme.note.main.note.ChangeToPaintFolderModel
import com.nearme.note.model.RichNoteRepository
import com.nearme.note.model.toRichNoteWithAttachments
import com.nearme.note.model.toRichNoteWithAttachmentsFromHtml
import com.nearme.note.paint.PaintFragment
import com.nearme.note.skin.SkinData
import com.nearme.note.skin.api.SkinManager
import com.nearme.note.util.AlarmUtils
import com.nearme.note.util.CloudSyncTrigger
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.DataStatisticsHelper
import com.nearme.note.util.FileUtil
import com.nearme.note.util.FolderStatisticUtils
import com.nearme.note.util.IntentParamsUtil
import com.nearme.note.util.NoteSearchManagerWrapper
import com.nearme.note.util.StatisticsUtils
import com.nearme.note.util.XorEncryptUtils
import com.nearme.note.util.postMeetingIntentionExitedIfNeed
import com.nearme.note.util.refreshCard
import com.nearme.note.view.helper.UiHelper
import com.oplus.cloud.agent.SyncAgentContants
import com.oplus.cloud.sync.note.AnchorManager
import com.oplus.cloud.utils.PrefUtils
import com.oplus.cloudkit.view.CloudKitSyncGuidManager
import com.oplus.note.R
import com.oplus.note.data.Entities
import com.oplus.note.data.Entity
import com.oplus.note.export.ExportAgentFactory
import com.oplus.note.logger.AppLogger
import com.oplus.note.notebook.internal.NoteBookData
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderFactory
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.repo.note.entity.PageResult
import com.oplus.note.repo.note.entity.Picture
import com.oplus.note.repo.note.entity.RichNote
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.note.repo.note.entity.SpeechLogInfo
import com.oplus.note.semantic.api.SemanticFactory
import com.oplus.note.utils.toast
import com.oplus.notes.webviewcoverpaint.container.api.IWebViewContainer
import com.oplus.notes.webviewcoverpaint.container.api.NoteDataInTipTap
import com.oplus.notes.webviewcoverpaint.container.api.NoteHtmlData
import com.oplus.notes.webviewcoverpaint.container.api.NoteHtmlDataError
import com.oplus.notes.webviewcoverpaint.data.clipboard.PasteAttach
import com.oplus.notes.webviewcoverpaint.data.clipboard.PasteAttachResult
import com.oplus.notes.webviewcoverpaint.data.clipboard.PasteUtils
import com.oplus.notes.webviewcoverpaint.container.api.IWebViewContainerCoverpaint
import com.oplus.richtext.core.parser.HtmlParser
import com.oplus.richtext.editor.utils.RichStatisticsUtils
import com.oplus.richtext.editor.view.focus.FocusInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Dispatchers.Main
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import okio.ByteString.Companion.encode
import org.jsoup.Jsoup
import java.io.File
import java.util.UUID
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

class NoteViewRichEditViewModelCoverPaint : ViewModel() {
    companion object {
        const val TAG = "NoteViewRichEditViewModel"
        const val TYPE_VOICE_NORMAL = 0 //语音默认状态
        const val TYPE_VOICE_RECORDING = 1 //语音录制
        const val TYPE_VOICE_PLAY = 2   //语音播放
        const val LINE_BREAK = "\n"
        const val DELAY_TIME = 400L

        const val LONG_CLICK_TYPE_IMAGE = "image"
        const val LONG_CLICK_TYPE_CARD = "card"
        const val LONG_CLICK_TYPE_RECORD = "record"
        const val LONG_CLICK_TYPE_PAINT = "paint"
        const val LONG_CLICK_TYPE_CONTACT_CARD = "contactCard"
        const val LONG_CLICK_TYPE_SCHEDULE_CARD = "scheduleCard"
        const val LONG_CLICK_TYPE_FILECARD = "filecard"
        const val SELECT_TYPE_TABLE = "table"
        const val LONG_CLICK_TYPE_VIDEO = "videoCard"
        const val LONG_CLICK_TYPE_EXTERNAL_AUDIO = "external_audio"
        const val GETING_HTML = 0
        const val GETING_ERROR = -1
        const val NOT_GETING_HTML = 1

        private const val GET_WEB_VIEW_DATA = "getWebViewData"
    }
    var isInSaving = false
    var hasSaved = false
    var hasUpdateToDb = true
    val mInMultiWindowPrimaryHorizontal = MutableLiveData<Boolean>()
    var hashCode = 0
    var inMultiWindowBottom = false

    var mRichData: RichData? = null
    val mRichNoteLive = MutableLiveData<RichData>()
    /**
     * 用于监听当前VM对应界面显示的RichNoteWithAttachments变化
     */
    val richNoteObservable: LiveData<RichNoteWithAttachments?> by lazy {
        mRichNoteLive.switchMap { repository.getByLocalIdAsLiveData(it.getNoteGuid()) }
    }

    val dataController by lazy {
        DataOperatorController()
    }
    var mGUID: String? = null
    var mNote: RichNoteWithAttachments? = null
    var sPeechLogInfo: SpeechLogInfo? = null
    var isSummaryEdited: Boolean? = null
    var mTwoPanelChangedRichNote = MutableLiveData<Pair<RichNote?, Boolean>>()

    var isFromWidget: Boolean = false
    var isEncrypt = false
    var encryptCallback: (() -> Unit)? = null
    var emergencyCallBack: ((guid: String?) -> Unit)? = null
    var fromOutsideVoice = false

    var dialogType: Int = 0
    var dialogExtra: Bundle? = null
    var isCreateDialog = false
    var closeRebuildDialog = false
    var entities: Entities? = null
    val entiesLiveData = MutableLiveData<Entities>()
    // 涂鸦的预览状态
    var previewStatus: Boolean = true

    private val repository = RichNoteRepository
    var mIsCreateNote = true
        set(value) {
            field = value
            if (!value) {
                isFirstCreateNote = false
            }
        }
    var mDeletedAttachmentList: MutableList<String> = CopyOnWriteArrayList()

    val mCurrentUiMode = UiMode()

    /**if speech dialog input return true*/
    var isVoiceInput = false

    /**if speech attachment length not 0 return true*/
    var isVoiceAttachment = false

    // 通话传入的数据，通话id
    private var phoneCallId: String? = null

    var mFirstOriginalRichNote: RichNote? = null

    var mOriginalRichNote: RichNote? = null
    var mOriginalRichData: RichData? = null
    private var isOnlyAttrInfoChanged = false
    private var mIntent: Intent? = null
    var isFirstCreateNote = true
    var isKeyBoardAct = false
    val focusInfo = FocusInfo(-1, 0, 0)
    val mFullScreenMode = MutableLiveData(true)
    var needHideInputWhenOcr = MutableLiveData(false)
    var mIsFromGlobalMenu = false
    /**
     * 在某些场景下，从三分应用分享文本到便签，进入笔记详情页面后直接点击返回按钮，没有保存笔记
     * 所以在onCreate的时候添加到undo里面
     */
    var needAddToUndo = false
    var mIsFromPencil = false
    val mRecreate = MutableLiveData(false)
    var mHintIndex = -1
    var mHintSize = -1
    var mHintStart = -1
    var isInsertAttachFromEditMode: Boolean? = null
    var isSavePaintCompleted = true
    var createVoice = false
    var isFirstWindowInsetsCall = true
    var isTwoPageSkinConfigChange = false

    /**
     * 仅在[RichNote.state]为[RichNote.STATE_NEW]以及[mCurrentUiMode]为[MODE_VIEW]的笔记下起作用
     */
    var isNewNoteModified = MutableLiveData(false)

    var isSummaryRetyBeyondSenvenMills = MutableLiveData(false)

    var mVoiceType = MutableLiveData(TYPE_VOICE_NORMAL) //语音便签是否为录制状态

    /**
     * 保存SDK识别到的链接结果
     */
    var mPageResultList: MutableLiveData<ArrayList<PageResult>> = MutableLiveData()

    /**内容是否有修改*/
    var mNoteChanged = false

    var mIsFromQuickNoteViewAdd = false
    var clearPaintViewCallback: (() -> Unit)? = null
    /**保存了笔记但未进行云同步*/
    var saveNoteAndNotSync = false

    private val isPadOrExport = UiHelper.isDevicePad() || !ConfigUtils.isSupportSkinSettings
    private val _skinChange = MutableStateFlow(SkinChange(skinId = ""))
    val skinChange: StateFlow<SkinChange> = _skinChange
    val titleLineHeight = MutableLiveData<Float>()
    val contentLineHeight = MutableLiveData<Float>()
    // 从tiptap获取的标题的json数据
    var tiptapTitleJson: String? = null
    // 从tiptap获取的正文的json数据
    var tiptapContentJson: String? = null

    var fromNoteList = false

    /**
     * 手动拖动选中的文字
     */
    var aigcSelectText: String? = null

    /**
     * 是否是点击底部工具栏AI按钮触发的AIGC操作
     */
    var isAIGCFromToolBar = false

    /**
     * 记录当前是否在AIGC流程中，在流程中则需要拦截clearFocus操作
     */
    var aigcInProcess = false

    /**
     * 记录语音输入的时候异常退出的时候的语音卡片相关的信息
     */
    var recordAttrString: String? = null

    /**
     * 记录正在录制时，异常保存(切换暗色等重建)的音频卡片的id
     */
    var picAttachmentId: String? = null
    /**
     * 记录正在录制时，异常保存(切换暗色等重建)的原文文件地址
     */
    var lrcPath: String? = null

    var contentHasTable = false

    var navigationEnable: Boolean = true
    var recordDisable = false
    var split = false
    var removedRecordIds = ArrayList<String>()
    private val getHtmlIng = MutableStateFlow<Int>(NOT_GETING_HTML)
    val exeGetHtmlIng: StateFlow<Int> get() = getHtmlIng
    private fun setGetHtmlValue(value: Int) {
        CoroutineScope(Main).launch {
            getHtmlIng.emit(value)
        }
    }

    fun clearTitleAndContentJson() {
        AppLogger.BASIC.d(TAG, "clearTitleAndContentJson")
        this.tiptapTitleJson = null
        this.tiptapContentJson = null
    }

    fun setEmergencyCallBackListener(listener: ((String?) -> Unit)?) {
        emergencyCallBack = listener
    }

    fun parseIntent(
        intent: Intent,
        activity: Activity,
        localIdFromSavedState: String?,
        createNewQuick: Boolean = false,
        canOpenEncryptedNote: Boolean = true,
        callback: (() -> Unit)? = null
    ) {
        this.mIntent = intent

        if (intent.action == NoteViewEditActivity.ACTION_VOICE_CREATE_NEW_NOTE) {
            isVoiceInput = true
            fromOutsideVoice = true
        }

        if (IntentParamsUtil.getIntExtra(intent, QuickNoteViewRichEditActivity.EXTRA_QUICK_NOTE_TYPE, 0) == QuickNoteViewRichEditActivity.TYPE_PENCIL) {
            mIsFromPencil = true
            if (IntentParamsUtil.getBooleanExtra(intent, WVNoteViewEditFragment.EXTRA_NOTE_CREATE_AGAIN, false)) {
                mCurrentUiMode.enterOverlayMode()
            }
        }

        isFromWidget = IntentParamsUtil.getIntExtra(intent, NoteViewEditActivity.EXTRA_OPEN_TYPE, 0) == NoteViewEditActivity.OPEN_TYPE_WIDGET
        //from appcard
        if (!isFromWidget) {
            isFromWidget = TextUtils.equals("OPEN_TYPE_WIDGET", IntentParamsUtil.getStringExtra(intent, NoteViewEditActivity.EXTRA_OPEN_TYPE, ""))
        }
        if (isFromWidget) {
            StatisticsUtils.setEventNoteWidgetToNoteViewEditActivity(MyApplication.appContext)
            intent.removeExtra(NoteViewEditActivity.EXTRA_OPEN_TYPE)
        }

        phoneCallId = InCallUINoteManager.parsePhoneCallId(intent)

        if (createNewQuick || activity is QuickNoteViewRichEditActivity) {
            mRichData = null
        }
        if (isCreateNote(intent) || createNewQuick) {
            resolveEmptyRichNote(activity, intent, localIdFromSavedState, createNewQuick)
        } else {
            resolveRichNote(
                activity,
                intent,
                localIdFromSavedState = localIdFromSavedState,
                canOpenEncryptedNote = canOpenEncryptedNote,
                callback = callback
            )
            if (!phoneCallId.isNullOrEmpty()) {
                StatisticsUtils.setEventNotifyNoteFromPhone(MyApplication.appContext)
            }
        }
    }

    fun parseIntent(
        intent: Intent,
        twoPane: Boolean = false,
        isQuickNote: Boolean = false,
        callback: (() -> Unit)? = null
    ) {
        if (!isCreateNote(intent) || isQuickNote) {
            val note = parserNoteForIntent(intent)
            if (note != null) {
                resolveNoteIfNeed(note, intent, twoPane, callback)
                return
            }
        }
    }

    private var preNoteHtmlData: NoteHtmlData? = null // 记录当前编辑器中数据，用于判断内容是否有修改

    fun updateTitleAndContentDataFromJS(webContainer: IWebViewContainerCoverpaint?) {
        if (webContainer == null) {
            return
        }
        viewModelScope.launch {
            preNoteHtmlData = getWebViewData(webContainer)?.htmlData
        }
    }

    fun updateTitleAndContentDataFromJS(data: NoteHtmlData) {
        preNoteHtmlData = data
    }

    /**
     * 判断内容在JS端是否已修改
     */
    suspend fun isTitleOrContentChangedInJs(webContainer: IWebViewContainerCoverpaint?): Boolean {
        if (webContainer == null) {
            return false
        }
        val isTitleOrContentChanged = preNoteHtmlData != getWebViewData(webContainer)?.htmlData
        val isSkinChanged = mOriginalRichNote?.skinId != mRichData?.metadata?.skinId

        AppLogger.BASIC.d(TAG, "isTitleOrContentChanged: $isTitleOrContentChanged, $isSkinChanged")
        return isTitleOrContentChanged || isSkinChanged
    }

    fun notifyNoteDataChanged(intent: Intent, updateCurrentFolderCallback: (() -> Unit)? = null)  {
        if (isCreateNote(intent)) {
            return
        }

        if (isInSaving) {
            AppLogger.BASIC.d(TAG, "notifyNoteDataChanged saving data, return")
            return
        }

        val note = parserNoteForIntent(intent) ?: return

        mGUID = note.richNote.localId

        viewModelScope.launch {
            val richNote: RichNote = note.richNote
            val isTitleChanged =
                if ((mRichData?.metadata?.title != richNote.title)) true
                else if (HtmlParser.isEmpty(richNote.rawTitle) && HtmlParser.isEmpty(mRichData?.metadata?.rawTitle)) false
                else mRichData?.metadata?.rawTitle != richNote.rawTitle
            val isContentChanged =
                if (mRichData?.metadata?.htmlText != richNote.htmlText) true
                else {
                    if (HtmlParser.isEmpty(richNote.rawText) && HtmlParser.isEmpty(mRichData?.metadata?.rawText)) false
                    else mRichData?.metadata?.rawText != richNote.rawText
                }
            val isSkinChanged = mRichData?.metadata?.skinId != richNote.skinId
            val oldAttachment = note?.attachments?.find { it.type == Attachment.TYPE_COVER_PICTURE }
            val isDoodleChanged = mRichData?.coverPictureAttachment?.attachmentId != oldAttachment?.attachmentId
            val shouldNotifyDataChange = (isTitleChanged || isContentChanged || isSkinChanged || isDoodleChanged)

            AppLogger.BASIC.d(TAG, "notifyNoteDataChanged isTitleChanged=$isTitleChanged, isContentChanged=$isContentChanged" +
                    " ,isSkinChanged=$isSkinChanged, shouldNotifyDataChange=$shouldNotifyDataChange")

            val length = mRichData.getContentCount()
            if ((mTwoPanelChangedRichNote.value?.first != richNote) || shouldNotifyDataChange) {
                if (shouldNotifyDataChange) {
                    mRichData = repository.convert(note)
                }
                AppLogger.BASIC.d(TAG, "notifyNoteDataChanged mRichData ContentCharCount=$length")
                mRichData?.setContentCharCount(length)
                mTwoPanelChangedRichNote.value = Pair(richNote, shouldNotifyDataChange)
            }

            resetOriginalRichData()
            updateCurrentFolderCallback?.invoke()

            ThumbFileManager.ensureRichNoteFolderExist(note.richNote.localId)
            resetOriginalRichNote()
            mRichData?.metadata?.apply { mFirstOriginalRichNote = copy() }
            emergencyCallBack?.invoke(mRichData?.getNoteGuid())
        }
    }

    fun shouldShowTransition(): Boolean {
        val folderNotChange = TextUtils.equals(mRichData?.metadata?.folderGuid, mFirstOriginalRichNote?.folderGuid)
        val noteRecover = mFirstOriginalRichNote?.recycleTime ?: 1 > 0 && mRichData?.metadata?.recycleTime ?: 0 == 0L
        return folderNotChange && !noteRecover
    }

    @VisibleForTesting
    fun isCreateNote(intent: Intent): Boolean {
        val hasNote = (parserNoteForIntent(intent) != null)
        val hasGuid = intent.hasExtra(NoteViewEditActivity.EXTRA_NOTE_GUID)
        val shouldOpenNote = hasNote || hasGuid
        val viewMode = IntentParamsUtil.getBooleanExtra(intent, NoteViewEditActivity.EXTRA_VIEW_MODE, shouldOpenNote)

        val hasOcrContent = OcrScannerManager.hasOcrContent(intent)
        val hasSoundRecordContent = IntentParamsUtil.containsKey(intent, NoteViewEditActivity.TYPE_EXTRA_CONTENT)
        val hasGlobalMenuContent = getGlobalMenuContent(intent).isNotEmpty()
        val shouldCreateNoteWithGivenContent = hasOcrContent || hasSoundRecordContent || hasGlobalMenuContent

        createVoice = hasSoundRecordContent
        mIsCreateNote = !viewMode || shouldCreateNoteWithGivenContent
        if (mIsCreateNote) {
            DataStatisticsHelper.noteUserOps(TAG, "01010201", "")
        }
        return mIsCreateNote
    }

    fun resolveRichNote(
        activity: Activity,
        intent: Intent,
        localIdFromSavedState: String? = "",
        canOpenEncryptedNote: Boolean = true,
        callback: (() -> Unit)? = null
    ) {

        val note = parserNoteForIntent(intent)

        if (note != null) {
            resolveNoteIfNeed(note, intent, callback = callback)
            return
        }

        var noteGuid = IntentParamsUtil.getStringExtra(intent, NoteViewEditActivity.EXTRA_NOTE_GUID)
        if (noteGuid.isNullOrEmpty()) {
            noteGuid = localIdFromSavedState
        }
        if (!TextUtils.isEmpty(noteGuid)) {
            resolveGuidIfNeeded(activity, noteGuid, canOpenEncryptedNote = canOpenEncryptedNote)
            return
        }

        AppLogger.BASIC.e(TAG, "View mode: note guid is null.")
        finishWithError(activity)
    }

    fun resolveNoteIfNeed(
        note: RichNoteWithAttachments,
        intent: Intent,
        twoPane: Boolean = false,
        callback: (() -> Unit)? = null
    ) {
        mNote = note
        mGUID = note.richNote.localId
        AppLogger.BASIC.d(TAG, "resolveNoteIfNeed twoPane=$twoPane, isInSaving=$isInSaving")


        if (twoPane && isInSaving) {
            return
        }
        split = IntentParamsUtil.getBooleanExtra(intent, WVNoteViewEditFragment.KEY_FROM_SPLIT, false)
        viewModelScope.launch {
            if (twoPane) {
                if (mRichData.isEmpty() || mRichData.isContentEmpty()) {
                    mRichData = repository.convert(note)
                    checkAndFixHtmlTextError(note)
                }
            } else {
                mRichData = repository.convert(note)
                checkAndFixHtmlTextError(note)
            }
            changeSkin(skinId = mRichData?.metadata?.skinId)
            mRichNoteLive.value = mRichData
            resetOriginalRichData()

            ThumbFileManager.ensureRichNoteFolderExist(note.richNote.localId)
            resetOriginalRichNote()
            mRichData?.metadata?.apply { mFirstOriginalRichNote = copy() }
            emergencyCallBack?.invoke(mRichData?.getNoteGuid())
            sPeechLogInfo = mRichData?.speechLogInfo
            kotlin.runCatching {
                val gson = GsonBuilder()
                    .excludeFieldsWithoutExposeAnnotation()
                    .create()
                entities = gson.fromJson(sPeechLogInfo?.entity, Entities::class.java)
                entiesLiveData.postValue(entities)
            }
            isSummaryEdited = note.isSummaryEdit()
            withContext(Main) {
                callback?.invoke()
            }
        }
    }

    fun resolveGuidIfNeeded(activity: Activity, noteGuid: String, canOpenEncryptedNote: Boolean = true, callback: (() -> Unit)? = null) {
        mGUID = noteGuid
        viewModelScope.launch {
            mRichData = repository.findNoDelete(noteGuid)
            if (mRichData == null) {
                finishWithError(activity)
                return@launch
            }
            if (!canOpenEncryptedNote) {
                mRichData?.getFolderGuid()?.let { folderGuid ->
                    val folder = repository.findFolder(folderGuid)
                    AppLogger.BASIC.e(TAG, "resolveGuidIfNeeded in folder?.isEncrypted=${folder?.isEncrypted}")
                    if (folder?.isEncrypted == true) {
                        if (activity is NoteViewRichEditActivity) {
                            val wvFragment =
                                activity.supportFragmentManager.findFragmentByTag(WVNoteViewEditFragment.TAG)
                            if (wvFragment != null) {
                                (wvFragment as WVNoteViewEditFragment).encryptedActivityResultProcessor.startEncrypt()
                            } else {
                                finishWithEncryptedNote(activity)
                            }
                        } else {
                            finishWithEncryptedNote(activity)
                        }
                        return@launch
                    }
                }
            }

            changeSkin(skinId = mRichData?.metadata?.skinId)
            mRichNoteLive.value = mRichData
            resetOriginalRichData()

            ThumbFileManager.ensureRichNoteFolderExist(noteGuid)
            resetOriginalRichNote()
            emergencyCallBack?.invoke(mRichData?.getNoteGuid())
            sPeechLogInfo = mRichData?.speechLogInfo
            kotlin.runCatching {
                val gson = GsonBuilder()
                    .excludeFieldsWithoutExposeAnnotation()
                    .create()
                entities = gson.fromJson(sPeechLogInfo?.entity, Entities::class.java)
                entiesLiveData.postValue(entities)
            }
            isSummaryEdited = mRichData?.isSummaryEdited()
            callback?.let {
                withContext(Dispatchers.Main) {
                    callback.invoke()
                }
            }
        }
    }

    private fun resetOriginalRichData() {
        mOriginalRichData = mRichData?.deepCopy()
    }

    private fun handleGuid(activity: Activity, intent: Intent, mLoaclId: String?, createNewQuick: Boolean = false) {
        if (activity is QuickNoteViewRichEditActivity) {
            if (createNewQuick) {
                isNewNoteModified.value = false
            }
            var folderName = activity.application.getString(R.string.quick_note)
            CoroutineScope(Dispatchers.IO).launch {
                val folder =
                    AppDatabase.getInstance().foldersDao().findByGuid(FolderInfo.FOLDER_GUID_QUICK)
                if (folder == null) {
                    val guid = FolderInfo.FOLDER_GUID_QUICK
                    val result = FolderUtil.insertFolderNameSync(
                        activity.applicationContext,
                        folderName,
                        guid,
                        FolderInfo.FOLDER_UNENCRYPTED,
                        NoteBookData.IMG_COVER_PURE_YELLOW
                    )
                    if (FolderUtil.CODE_INSERT_FOLDER_EXISTED == result) {
                        val manualQuickFolders = AppDatabase.getInstance().foldersDao()
                            .findNotDeletedFolderByName(folderName)
                        var index = 0
                        if (manualQuickFolders != null) {
                            for (fo in manualQuickFolders) {
                                if (guid != fo.guid) {
                                    val newFolderName = fo.name + (index + 1)
                                    FolderUtil.updateFolderNameSync(
                                        activity,
                                        fo.guid,
                                        newFolderName,
                                        fo.extra.getCover()
                                    )
                                    index++
                                }
                            }
                        }
                        FolderUtil.insertFolderNameSync(
                            activity.applicationContext,
                            folderName,
                            guid,
                            FolderInfo.FOLDER_UNENCRYPTED,
                            NoteBookData.IMG_COVER_PURE_YELLOW
                        )
                        withContext(Dispatchers.Main) {
                            handleEmptyRichNote(activity, intent, mLoaclId, guid)
                            if (FolderStatisticUtils.isDefaultFolder(guid)) {
                                StatisticsUtils.setEventCreateSystemFolder(
                                    FolderStatisticUtils.getFolderType(
                                        guid
                                    )
                                )
                            }
                        }
                    } else if (FolderUtil.CODE_INSERT_FOLDER_OK == result) {
                        withContext(Dispatchers.Main) {
                            handleEmptyRichNote(activity, intent, mLoaclId, guid)
                            if (FolderStatisticUtils.isDefaultFolder(guid)) {
                                StatisticsUtils.setEventCreateSystemFolder(
                                    FolderStatisticUtils.getFolderType(
                                        guid
                                    )
                                )
                            }
                        }
                    } else {
                        Log.i(TAG, "insert failed!")
                    }
                } else {
                    folderName = folder.name
                    withContext(Dispatchers.Main) {
                        handleEmptyRichNote(activity, intent, mLoaclId, folder.guid)
                    }
                }
            }
        } else {
            val folderGuid = IntentParamsUtil.getStringExtra(
                intent,
                NotesProvider.COL_NOTE_FOLDER_GUID,
                FolderInfo.FOLDER_GUID_NO_GUID
            ).let { wrapFolderGuid(it) }
            handleEmptyRichNote(activity, intent, mLoaclId, folderGuid)
        }
    }

    private fun wrapFolderGuid(guid: String): String {
        // 在全部笔记下，创建笔记时需修改folder guid为未分类
        return if (guid == FolderInfo.FOLDER_GUID_ALL) {
            FolderInfo.FOLDER_GUID_NO_GUID
        } else guid
    }

    private fun handleEmptyRichNote(
        activity: Activity,
        intent: Intent,
        mLoaclId: String?,
        folderGuid: String
    ) {
        val items = mutableListOf(Data.emptyInstance())
        var title = Data.emptyInstance()
        resolveCallPhoneInfoIfNeeded(intent)?.apply {
            items.add(0, this)
        }
        resolveOcrContentIfNeeded(intent)?.apply {
            items.clear()
            items.add(this)
            val len = this.text?.length ?: 0
            focusInfo.updateFocusContent(1, len, len)
        }
        resolveSoundRecordContentIfNeeded(activity, intent)?.apply {
            title = first

            items.clear()
            items.add(second)
            val len = second.text?.length ?: 0
            focusInfo.updateFocusContent(1, len, len)
        }

        resolveGlobalMenuIfNeeded(intent)?.apply {
            items.clear()
            items.add(this)
            val len = this.text?.length ?: 0
            focusInfo.updateFocusContent(1, len, len)
        }

        if (mLoaclId == null) {
            mRichData = RichData(
                metadata = RichNote(folderGuid = folderGuid).apply {
                    mGUID = localId
                    viewModelScope.launch(Dispatchers.IO) {
                        ThumbFileManager.ensureRichNoteFolderExist(localId)
                    }
                },
                title = title,
                items = items,
                extItems = items.toMutableList(),
                coverPictureAttachment = null
            )
        } else {
            mRichData = RichData(
                metadata = RichNote(folderGuid = folderGuid, localId = mLoaclId).apply {
                    viewModelScope.launch(Dispatchers.IO) {
                        ThumbFileManager.ensureRichNoteFolderExist(mLoaclId)
                    }
                },
                title = title,
                items = items,
                extItems = items.toMutableList(),
                coverPictureAttachment = null
            )
        }
        changeSkin(skinId = mRichData?.metadata?.skinId)
        mRichNoteLive.value = mRichData
        resetOriginalRichData()
        resetOriginalRichNote()
    }

    private fun resolveEmptyRichNote(activity: Activity, intent: Intent, mLoaclId: String?, createNewQuick: Boolean = false) {
        handleGuid(activity, intent, mLoaclId, createNewQuick)
    }

    @VisibleForTesting
    fun resolveCallPhoneInfoIfNeeded(intent: Intent): Data? {
        val phoneContact = InCallUINoteManager.parsePhoneContacts(intent)
        if (!TextUtils.isEmpty(phoneContact)) {
            val hintText = InCallUINoteManager.resolvePhoneCallHintMessage(
                MyApplication.appContext,
                phoneContact,
                intent
            )
            if (hintText.isNotEmpty()) {
                return Data(
                    type = Data.TYPE_PHONE_HINT_TEXT,
                    text = SpannableStringBuilder(hintText)
                )
            }
        }
        return null
    }

    private fun resolveOcrContentIfNeeded(intent: Intent): Data? {
        val ocrResult = OcrScannerManager.getOcrContent(intent)
        AppLogger.BASIC.d(TAG, "init intent data ocrResult")
        if (!TextUtils.isEmpty(ocrResult)) {
            return Data(type = Data.TYPE_TEXT, text = SpannableStringBuilder(ocrResult))
        }
        return null
    }

    @VisibleForTesting
    fun resolveSoundRecordContentIfNeeded(activity: Activity, intent: Intent?): Pair<Data, Data>? {
        //sound record
        if (ExternalCreateNoteManager.hasContent(intent)) {
            val title = ExternalCreateNoteManager.parseTitle(intent)
            val content = ExternalCreateNoteManager.parseContent(intent)
            val packageName = ExternalCreateNoteManager.parsePackage(intent)
            if (TextUtils.isEmpty(packageName)) {
                AppLogger.BASIC.e("Note", NoteViewEditActivity.TYPE_EXTRA_PACKAGE_NAME + NotesProviderPresenter.MESSAGE_NOT_NULL_OR_ZERO_LENGTH)
                activity.setResult(Activity.RESULT_CANCELED, ExternalCreateNoteManager.createPackagenameErrorResultIntent())
                activity.finish()
            }
            if (TextUtils.isEmpty(content)) {
                AppLogger.BASIC.e("Note", NoteViewEditActivity.TYPE_EXTRA_CONTENT + NotesProviderPresenter.MESSAGE_NOT_NULL_OR_ZERO_LENGTH)
                activity.setResult(Activity.RESULT_CANCELED, ExternalCreateNoteManager.createContentErrorResultIntent())
                activity.finish()
            }
            StatisticsUtils.setEventInsertNote(packageName, content.length)

            val titleData = Data(type = Data.TYPE_TEXT, text = SpannableStringBuilder(title))
            val contentData = Data(type = Data.TYPE_TEXT, text = SpannableStringBuilder(content))
            return Pair(titleData, contentData)
        }
        return null
    }

    private fun resolveGlobalMenuIfNeeded(intent: Intent): Data? {
        var content = getGlobalMenuContent(intent)
        if (content.isNotEmpty()) {
            content += LINE_BREAK
            return Data(
                type = Data.TYPE_TEXT,
                text = SpannableStringBuilder(content)
            )
        }
        return null
    }

    fun getGlobalMenuContent(intent: Intent): String {
        return intent.getStringExtra(PROCESS_TEXT_KEY) ?: ""
    }

    private suspend fun findFolder(guid: String) = withContext(Dispatchers.IO) {
        return@withContext AppDatabase.getInstance().foldersDao().findByGuid(guid)
    }

    @VisibleForTesting
    fun finishWithError(activity: Activity) {
        activity.toast(R.string.note_not_exist)
        if (isFromWidget) {
            activity.startActivity(Intent(activity, MainActivity::class.java))
        }
        activity.finish()
    }

    private fun finishWithEncryptedNote(activity: Activity) {
        activity.toast(R.string.can_view_in_the_encrypt_note_book)
        activity.finish()
    }

    fun topped() {
        mRichData?.metadata?.apply {
            topTime = if (topTime == 0L) {
                System.currentTimeMillis()
            } else {
                0
            }
        }
        mRichData?.metadata?.apply {
            updateTime = System.currentTimeMillis()
        }
    }

    fun encrypt(
        lifecycleScope: LifecycleCoroutineScope?,
        isDecrypt: Boolean,
        callback: (() -> Unit)? = null,
        updateCurrentFolderCallback: ((folderId: String) -> Unit)? = null
    ) {
        encryptCallback = callback
        mRichData?.metadata?.apply {
            if (isDecrypt) {
                isEncrypt = false
                this.isLocal = false
                this.folderGuid = FolderFactory.getUncategorizedFolderGuid()
                StatisticsUtils.setEventEncryptedNoteDecrypt(0)
            } else {
                isEncrypt = true
                if (!ConfigUtils.isNeedToSyncPrivateNote) {
                    this.isLocal = true
                }
                this.folderGuid = FolderFactory.getDefaultEncryptedFolderGuid()
                StatisticsUtils.setEventEncryptedNoteEncrypt(0)
            }
            updateCurrentFolderCallback?.invoke(folderGuid)
            updateEncrypt(lifecycleScope, isDecrypt)
        }
    }

    private fun updateEncrypt(
        lifecycleScope: LifecycleCoroutineScope?,
        isDecrypt: Boolean
    ) {
       lifecycleScope?.launch(Dispatchers.IO) {
           val encrypted = if (isDecrypt) 0 else 1
           val encryptPre = if (isDecrypt) 1 else 0
           AppLogger.BASIC.d(TAG, "updateNoteEncrypt:$encrypted")

           mRichData?.metadata?.apply {
               encryptedPre = encryptPre
               this.encrypted = encrypted
           }
       }
    }

    fun updateNoteEncrypt(
        lifecycleScope: LifecycleCoroutineScope?,
        preFolder: Folder?,
        destInfo: Folder,
    ) {
        lifecycleScope?.launch(Dispatchers.IO) {
            val encryptPre = preFolder?.encrypted ?: 1
            val encrypted = destInfo.encrypted
            AppLogger.BASIC.d(TAG, "preFolder: $preFolder destInfo:$destInfo")
            mRichData?.metadata?.apply {
                encryptedPre = encryptPre
                this.encrypted = encrypted
            }
        }
    }


    fun recycled(activity: Activity, recycledCallBack: ((guid: String) -> Unit)? = null) {
        mRichData?.metadata?.apply {
            recycleTime = System.currentTimeMillis()
            AlarmUtils.resetSystemAlarms(AlarmUtils.ControllerType.NOTE)
            notifyPhoneWithNoteGuid("")
            notifyExistWidget(activity, localId)
            recycledCallBack?.invoke(localId)
        }
    }

    fun recoverNote(
        activity: Activity,
        mCoverEmpty: Boolean,
        insertProcessing: Boolean,
        recoveCallBack: ((guid: String) -> Unit)? = null,
        updateCurrentFolderCallback: ((folderId: String) -> Unit)? = null
    ) {
        viewModelScope.launch {
            mRichData?.metadata?.apply {
                recycleTime = 0
                val folder = getRecoverFolderGuid(folderGuid)
                val folderId = folder.first
                val message = getToastMessage(folder.first, folderGuid)
                folderGuid = folderId
                updateCurrentFolderCallback?.invoke(folderId)
                saveInternal(activity, mCoverEmpty, insertProcessing = insertProcessing)
                recoveCallBack?.invoke(localId)
                Toast.makeText(activity, message, Toast.LENGTH_SHORT).show()
            }
        }
    }

    private suspend fun getRecoverFolderGuid(guid: String): Pair<String, String> {
        val currentFolder = findFolder(guid)
        val isDeletedEncryptedFolder = DeleteFolderCacheHolder.isDeletedEncryptedFolder(guid)
        /**
         * 如果笔记本存在，则恢复到原笔记本；
         * 若笔记本已不存在，但是之前的笔记本是加密的，则恢复到默认的加密笔记本，否则恢复到未分类笔记本
         */
        return currentFolder?.let {
            Pair(it.guid, it.name)
        } ?: Pair(
            if (isDeletedEncryptedFolder) {
                FolderInfo.FOLDER_GUID_ENCRYPTED
            } else {
                FolderInfo.FOLDER_GUID_NO_GUID
            }, ""
        )
    }

    fun deleted(activity: Activity, deletedCallBack: ((guid: String) -> Unit)? = null) {
        GlobalScope.launch(Dispatchers.Main) {
            mRichData?.metadata?.apply {
                val isCloudSyncClose = NoteSyncProcess.isCloudSyncSwitchClose(activity)
                if (isCloudSyncClose) {
                    //物理删除RichNote
                    val result = repository.physicallyDeleted(localId)
                    AppLogger.OPS.d(TAG, "delete directly: $result, $localId, ${rawTitle?.encode()?.base64()}")
                } else {
                    //逻辑删除RichNote
                    var result = repository.maskDeleted(localId)
                    AppLogger.OPS.d(TAG, "delete marked: $result, $localId, ${rawTitle?.encode()?.base64()}")

                    //物理删除Attachments
                    result = repository.deleteAttachments(localId)
                    AppLogger.BASIC.d(TAG, "deleted attachments result: $result")
                    CloudSyncTrigger.sendDataChangedBroadcast(activity)
                }
                //删除文件
                deletedFile(localId)
                //清除锚点
                clearAnchor(activity, isCloudSyncClose, state)
                AlarmUtils.resetSystemAlarms(AlarmUtils.ControllerType.NOTE)

                notifyPhoneWithNoteGuid("")
                notifyExistWidget(activity, localId)
                deletedCallBack?.invoke(localId)
            }
        }
    }

    private suspend fun deletedFile(guid: String) = withContext(Dispatchers.IO) {
        FileUtil.deleteDirectory(ThumbFileManager.getFolderPathInSD(guid))
        FileUtil.deleteDirectory(FileUtil.getFolderPathInData(MyApplication.appContext, guid))
    }

    @VisibleForTesting
    fun checkValid(activity: Activity, isUIChangeWithDB: Boolean): Boolean {
        val valid = mRichData?.run {
            if (metadata.state != RichNote.STATE_NEW && isUIChangeWithDB) {
                mRichData = reNewLocalId(false)
                AppLogger.BASIC.d(TAG, "Data conflict between UI and DB, give UI Data a new localID = ${mRichData?.metadata?.localId}")
                false
            } else {
                true
            }
        }
        return valid ?: true
    }

    fun setIsInSaving(activity: Activity, fromParent: Boolean) {
        if (fromParent) {
            notifySaveInternalFinished(activity)
        }
        isInSaving = false
    }

    @VisibleForTesting
    suspend fun saveInternal(
        activity: Activity,
        isCoverPaintEmpty: Boolean = false,
        isCoverDoodleChanged: Boolean = false,
        isAddWidget: Boolean = false,
        fromParent: Boolean = false,
        syncImmediately: Boolean = true,
        noteWithAttachments: RichNoteWithAttachments? = null,
        notebookViewModel: NoteBookViewModel? = null,
        insertProcessing: Boolean,
        isChangeCallback: ((Boolean) -> Unit)? = null,
        isFromEdit: Boolean = false
    ) {
        val isRichDataEmpty = mRichData.isEmpty()
        val hasTableData = mRichData.hasTableTag()
        AppLogger.BASIC.i(TAG, "saveInternal insertProcessing:$insertProcessing mRichData.isEmpty:$isRichDataEmpty," +
                "recycleTime:${mRichData?.getRecycleTime()} hasTableData=$hasTableData")
        if (insertProcessing || (isRichDataEmpty && !hasTableData)) {
            mIsFromQuickNoteViewAdd = false
            setIsInSaving(activity, fromParent)
            return
        }
        if (syncImmediately) {
            hasSaved = true
        }
        mRichData?.apply {
            isInSaving = true
            val richNoteWithAttachments = noteWithAttachments ?: repository.convert(this)
            richNoteWithAttachments.apply {
                if (this.richNote.folderGuid == FolderInfo.FOLDER_GUID_ALL) {
                    this.richNote.folderGuid = FolderInfo.FOLDER_GUID_NO_GUID
                }
                mNoteChanged = isNoteChanged(richNote)
                isChangeCallback?.invoke(mNoteChanged)
                if (mNoteChanged || isCoverDoodleChanged) {
                    RichNoteSaveTransitionHelper.catchUiSaveState(this.richNote.localId)
                    hasUpdateToDb = true

                    val moveOutFromPaintFolder = richNote.extra?.moveOutFromPaintFolder
                    val isEncryptedNote = notebookViewModel?.isCurrentDetailFolderEncrypted() ?: false
                    AppLogger.BASIC.d(
                        TAG,
                        "saveInternal isCoverPaintEmpty =$isCoverPaintEmpty moveOutFromPaintFolder=$moveOutFromPaintFolder " +
                                "isEncrypt=$isEncryptedNote"
                    )
                    if (!isCoverPaintEmpty && moveOutFromPaintFolder != 1 && richNote.folderGuid != FolderInfo.FOLDER_GUID_PAINT
                        && !isEncryptedNote
                    ) {
                        //有涂鸦内容的笔记，保存时移动到手写笔记本里
                        RichNoteRepository.createAndGetPaintFolderIfNeed(activity)?.let { paintFolder ->
                            richNote.folderGuid = FolderInfo.FOLDER_GUID_PAINT
                            notebookViewModel?.updateCurrentDetailFolder(paintFolder)
                            val paintNoteCount = repository.getPaintFolderNotesCount()
                            AppLogger.BASIC.d(TAG, "saveInternal move to paint folder paintNoteCount=$paintNoteCount")
                            ChangeToPaintFolderModel.noteChangeToPaintFolder.postValue(
                                ChangeToPaintFolderModel(richNote.localId, paintFolder, activity.taskId, activity.hashCode(), paintNoteCount)
                            )
                        }
                    }

                    AppLogger.BASIC.d(TAG, "Save to db, content changed! saveInternal isOnlyAttrInfoChanged $isOnlyAttrInfoChanged")
                    FeedbackLog.D.userLog("modify rich note.", this.richNote.localId)
                    val richNoteWithAttachmentsInDb = repository.getByLocalId(this.richNote.localId)
                    val isAlreadyDelete = richNoteWithAttachmentsInDb == null
                    val isAlreadyChanged = richNoteWithAttachmentsInDb != null && richNoteWithAttachmentsInDb.richNote.timestamp > metadata.timestamp
                    val isUIChangeWithDB = isAlreadyChanged || isAlreadyDelete
                    val valid = if (fromParent) true else checkValid(activity, isUIChangeWithDB)
                    CloudKitSyncGuidManager.editFolderGuids = listOf(this.richNote.folderGuid)
                    AppLogger.BASIC.d(TAG, "checkValid = $valid, isAlreadyDelete=$isAlreadyDelete,isAlreadyChanged=$isAlreadyChanged," +
                            "fromParent=$fromParent")
                    if (!valid) {
                        repository.convert(mRichData!!).apply {
                            richNoteWithAttachmentsInDb?.attachments?.forEach { attachment ->
                                val thumbFileInDbPath = ThumbFileManager.getThumbFilePathInData(activity, this.richNote.localId, attachment.attachmentId)
                                AppLogger.BASIC.d(TAG, "saveInternal thumbFileInDbPath=$thumbFileInDbPath")
                                restoreRemovedAttachment(thumbFileInDbPath)
                            }
                            AppLogger.BASIC.d(TAG, "insertBySuspend valid:$valid")
                            repository.insertBySuspendForDetailPage(this)
                            notifyAndResetData(
                                richNote,
                                activity,
                                isAddWidget,
                                this,
                                isFromEdit = isFromEdit
                            )
                        }
                    } else {
                        if (!isOnlyAttrInfoChanged) {
                            richNote.updateTime = System.currentTimeMillis()
                            ExportAgentFactory.getDocAgent()?.updateNoteInfoIfNeed(activity.applicationContext, richNote.localId, richNote.updateTime)
                        }
                        if ((mIsCreateNote && !isNewNoteModified.value!!)) {
                            AppLogger.BASIC.d(TAG, "insertBySuspend valid:$valid")
                            repository.insertBySuspendForDetailPage(this)
                            isNewNoteModified.value = true
                            viewModelScope.launch(Dispatchers.IO) {
                                if (FolderStatisticUtils.isDefaultFolder(richNote.folderGuid)) {
                                    StatisticsUtils.setEventCreateSystemNotes(
                                        FolderStatisticUtils.getFolderType(
                                            richNote.folderGuid
                                        ), RichNoteRepository.getCurrentFolderNotesCount(richNote.folderGuid), ""
                                    )
                                }
                            }
                        } else {
                            // NOTE: 若内存记录globalId为空，但是数据库记录不为空，说明数据库记录被云同步场景修改过globalId，需要保留数据库记录中的globalId.
                            if (TextUtils.isEmpty(richNote.globalId) && !TextUtils.isEmpty(richNoteWithAttachmentsInDb?.richNote?.globalId)) {
                                richNote.globalId = richNoteWithAttachmentsInDb?.richNote?.globalId
                            }
                            setNoteChanged(this)
                        }
                        AppLogger.BASIC.d(TAG, "saveInternal recycleTime:${metadata.recycleTime}")
                        if (metadata.recycleTime != 0L) {
                            resetData()
                        }
                        notifyAndResetData(
                            richNote,
                            activity,
                            isAddWidget,
                            this,
                            isFromEdit = isFromEdit
                        )
                    }
                } else {
                    if (isAddWidget) {
                        notifyNoteWidget(activity, true, this)
                    } else if (FolderInfo.FOLDER_GUID_ENCRYPTED == richNote.folderGuid) {
                        notifyNoteWidget(activity, false, this)
                    }
                    AppLogger.BASIC.d(TAG, "Save to db,saveInternal no content changed!")
                }
                //私密笔记不上云，新建或者修改时也不触发云同步
                val disAllowSync = !ConfigUtils.isNeedToSyncPrivateNote && FolderInfo.FOLDER_GUID_ENCRYPTED == richNote.folderGuid

                if (syncImmediately && hasUpdateToDb && !disAllowSync && !isVoiceInput) {
                    CloudSyncTrigger.sendDataChangedBroadcast(activity)
                    saveNoteAndNotSync = false
                } else if (!syncImmediately && hasUpdateToDb && !disAllowSync) {
                    saveNoteAndNotSync = true
                }

                AppLogger.BASIC.d(TAG, "--putStringSet--${getDeleteAttachment()}")
                PrefUtils.putStringSet(activity.applicationContext, PrefUtils.KEY_RICH_NOTE_DELETED_PICTURE, getDeleteAttachment())
                if (fromParent) {
                    notifySaveInternalFinished(activity)
                }
                isInSaving = false

                if (mIsFromQuickNoteViewAdd) {
                    clearPaintViewCallback?.invoke()
                }
                refreshCard(MyApplication.appContext, richNote.localId, richNote.folderGuid, true)
                postMeetingIntentionExitedIfNeed(MyApplication.appContext)
            }
        }
        NoteSearchManagerWrapper.notifyDataChange(isFullQuery = false)
    }

    private fun resetData() {
        isNewNoteModified.value = false
        val richNote = if (mRichData != null) {
            mRichData!!.metadata.copy().apply {
                recycleTime = 0
                htmlText = ""
                rawText = ""
                text = ""
                title = ""
                rawTitle = ""
            }
        } else {
            val folderGuid = FolderInfo.FOLDER_GUID_QUICK
            RichNote(folderGuid = folderGuid).apply {
                mGUID = localId
                viewModelScope.launch(Dispatchers.IO) {
                    ThumbFileManager.ensureRichNoteFolderExist(localId)
                }
            }
        }

        val items = mutableListOf(Data.emptyInstance())
        val title = Data.emptyInstance()
        mRichData = RichData(
            metadata = richNote,
            title = title,
            items = items,
            coverPictureAttachment = null
        )
//        mRichNoteLive.value = mRichData
    }

    suspend fun setNoteChanged(richNoteWithAttachments: RichNoteWithAttachments) {
        val richNote = richNoteWithAttachments.richNote
        if (ConfigUtils.isSupportPresetNotes && PresetNoteUtils.isPresetNote(richNote)) {
            if (!richNoteWithAttachments.hasSyncedToCloud()) {
                richNote.isPreset = false
                repository.updateForDetailPage(richNoteWithAttachments, updateTimestamp = false)
            } else {
                val newRichData = repository.reNewRichData(richNoteWithAttachments, true)
                val reNew = newRichData.toRichNoteWithAttachments()
                if (isEncrypt) {
                    reNew.richNote.isLocal = true
                }
                reNew.richNote.isPreset = false
                repository.insertBySuspendForDetailPage(reNew)

                richNote.state = RichNote.STATE_MODIFIED
                richNote.isLocal = false
                richNote.deleted = true
                repository.update(richNoteWithAttachments, updateTimestamp = false)

                mRichData = newRichData
                if (isEncrypt) {
                    encryptCallback?.invoke()
                    encryptCallback = null
                }
            }

            if (isEncrypt) {
                Thread {
                    NoteInfoDBUtil.deleteNote(richNote.localId, false)
                }.start()
            }
        } else {
            val isLocal = FolderFactory.isDefaultEncryptedFolder(richNote.folderGuid)
            AppLogger.BASIC.d(TAG, "saveInternal, isLocal =$isLocal,isNeedToSyncPrivateNote=${ConfigUtils.isNeedToSyncPrivateNote}")
            if (!ConfigUtils.isNeedToSyncPrivateNote && isEncrypt && isLocal) {
                if (!richNoteWithAttachments.hasSyncedToCloud()) {
                    richNote.isLocal = true
                    repository.updateForDetailPage(richNoteWithAttachments, updateTimestamp = false)
                } else {
                    val newRichData = repository.reNewRichData(richNoteWithAttachments, true)
                    val reNew = newRichData.toRichNoteWithAttachments()
                    reNew.richNote.isLocal = true
                    repository.insertBySuspendForDetailPage(reNew)

                    richNote.state = RichNote.STATE_MODIFIED
                    richNote.isLocal = false
                    richNote.deleted = true
                    repository.update(richNoteWithAttachments, updateTimestamp = false)

                    mRichData = newRichData
                    encryptCallback?.invoke()
                    encryptCallback = null
                }

                Thread {
                    NoteInfoDBUtil.deleteNote(richNote.localId, false)
                }.start()
            } else {
                repository.updateForDetailPage(richNoteWithAttachments, updateTimestamp = false)
            }
        }
    }

    fun notifyAndResetData(
        richNote: RichNote,
        activity: Activity,
        isAddWidget: Boolean,
        richNoteWithAttachments: RichNoteWithAttachments,
        isFromEdit: Boolean = false
    ) {
        AlarmUtils.resetSystemAlarms(AlarmUtils.ControllerType.NOTE)
        //send broadcast to notify Phone App that there is a note saved to db, so it can pass the guid to view this note
        notifyPhoneWithNoteGuid(richNote.localId)
        notifyNoteWidget(activity, isAddWidget, richNoteWithAttachments)
        notifyDataChanged()
        resetOriginalRichData()
        resetOriginalRichNote()
        notifySaveNoteComplete(
            activity,
            richNote.localId,
            richNote.extra?.featureList,
            isFromEdit = isFromEdit
        )
        clearTitleAndContentJson()
    }

    @Suppress("LongParameterList")
    fun save(
        activity: Activity,
        isCoverPaintEmpty: Boolean = false,
        isCoverDoodleChanged: Boolean = false,
        isAddWidget: Boolean = false,
        fromParent: Boolean = false,
        syncImmediately: Boolean = true,
        noteWithAttachments: RichNoteWithAttachments? = null,
        notebookViewModel: NoteBookViewModel? = null,
        insertProcessing: Boolean,
        isChangeCallback: ((Boolean) -> Unit)? = null,
        isFromEdit: Boolean = false
    ) {
        isInSaving = true
        GlobalScope.launch(Dispatchers.Main) {
            saveInternal(
                activity, isCoverPaintEmpty, isCoverDoodleChanged, isAddWidget, fromParent, syncImmediately,
                noteWithAttachments, notebookViewModel, insertProcessing, isChangeCallback, isFromEdit = isFromEdit
            )
        }
    }

    var deleteAllContent = false
    fun verifyDataForRecycle(isCoverPaintEmpty: Boolean? = null, contentHasTable: Boolean? = null): Boolean {
        Log.i(TAG, "verifyDataForRecycle mRichData.isEmpty:${mRichData.isEmpty()} mOriginalRichData.isEmpty:${mOriginalRichData.isEmpty()} " +
                "isCoverPaintEmpty $isCoverPaintEmpty,recycleTime= ${mRichData?.getRecycleTime()} " +
                "contentHasTable=$contentHasTable  mOriginalRichData=${mOriginalRichData.hasTableTag()}")
        if (mRichData != null && mRichData!!.getRecycleTime() > 0) {
            return true
        }
        // 删除界面所有内容，触发“删除到最近删除”操作
        mRichData?.let {
            /**内容均为空，不需要保存*/
            if (mRichData.isEmpty() && mOriginalRichData.isEmpty()) {
                if (mOriginalRichData.hasTableTag() && contentHasTable == false) {
                    doDataRecycle(it)
                    return true
                } else {
                    Log.i(TAG, "verifyDataForRecycle return")
                    return false
                }
                /**rich data为空，original data不为空 将original data 赋值给 rich data 并标记删除*/
            } else if (mRichData.isEmpty() && isCoverPaintEmpty == true && !mOriginalRichData.isEmpty() && contentHasTable != true) {
                doDataRecycle(it)
                return true
            }
        }

        return false
    }

    private fun doDataRecycle(it: RichData) {
        AppLogger.BASIC.i(TAG, "doDataRecycle")
        mOriginalRichData?.apply {
            deleteAllContent = true
            //给recyclertime赋值，置入最近删除
            metadata.recycleTime = System.currentTimeMillis()
            //此条笔记整体移入最近删除，退出时不删除mOriginalRichData包含的图片和涂鸦文件（bug: 1501699）
            webItems.forEach { webItem ->
                if (webItem.type == Data.TYPE_ATTACHMENT) {
                    mDeletedAttachmentList = mDeletedAttachmentList.filterNot { path ->
                        path.contains(webItem.attachment!!.attachmentId)
                    } as MutableList<String>
                }
            }

            subAttachments.forEach { paint ->
                mDeletedAttachmentList = mDeletedAttachmentList.filterNot { path -> path.contains(paint.attachmentId) } as MutableList<String>
            }

            mRichData = it.copy(
                metadata = metadata,
                title = title,
                /**重新赋值时需要保持items内存地址不改变，改变会导致adapter更新数据失效*/
                /*items = it.items.apply {
                    clear()
                    addAll(items)
                },*/
                subAttachments = it.subAttachments.apply {
                    clear()
                    addAll(subAttachments)
                },
                coverPictureAttachment = coverPictureAttachment,
                webItems = it.webItems.apply {
                    clear()
                    addAll(webItems)
                },
            )
            val length = this.getContentCount()
            mRichData?.setContentCharCount(length)
        }
    }

    @VisibleForTesting
    fun isNoteChanged(richNote: RichNote): Boolean {
        if (mOriginalRichNote == null) {
            AppLogger.BASIC.d(TAG, "saveInternal isNoteChanged  mOriginalRichNote==null")
            resetOriginalRichNote()
        }
        return richNote.isChanged()
    }

    fun isNoteChanged(richData: RichData?, paintChange: Boolean, block: (Boolean, RichNoteWithAttachments?) -> Unit) {
        if (richData == null) {
            block.invoke(false, null)
            return
        }
        if (paintChange) {
            block.invoke(true, null)
            return
        }
        if (mDeletedAttachmentList.isNotEmpty()) {
            block.invoke(true, null)
            return
        }
        if (tiptapTitleJson != null || tiptapContentJson != null) {
            block.invoke(true, null)
            return
        }
        if (mOriginalRichNote == null) {
            block.invoke(true, null)
        } else {
            GlobalScope.launch(Dispatchers.IO) {
                val richNoteWithAttachments = repository.convert(richData)
                val isNoteChanged = richNoteWithAttachments.richNote.isChanged() || isWebItemsChanged()
                block.invoke(isNoteChanged, richNoteWithAttachments)
            }
        }
    }
    private fun areHtmlEqualByBody(originHtml: String?, html: String): Boolean {
        if (originHtml == null) {
            return false
        }
        val docOri = Jsoup.parse(originHtml)
        val doc = Jsoup.parse(html)
        val bodyOri = docOri.body().html()
        val body = doc.body().html()
        return bodyOri == body
    }

    private fun areRawEqualByDivContent(originRaw: String?, raw: String): Boolean {
        if (originRaw == null) {
            return false
        }
        val docOri = Jsoup.parse(originRaw)
        val doc = Jsoup.parse(raw)
        val divOri = docOri.selectFirst("div")?.html()
        val div = doc.selectFirst("div")?.html()
        return divOri == div
    }

    /**
     * 判断富文本笔记内容是否发生变化
     * @param originalRichNote 原始富文本笔记
     * @param htmlText 当前的 HTML 文本
     * @param rawText 当前的原始文本
     * @return 如果内容发生变化返回 true，否则返回 false
     */
    private fun isRichNoteContentChanged(originalRichNote: RichNote?, htmlText: String, rawText: String): Boolean {
        // 首先比较 HTML 文本是否相等
        if (!areHtmlTextsEqual(originalRichNote?.htmlText, htmlText)) {
            return true
        }
        // 如果 HTML 文本相等，再比较raw_text原始文本
        return !areRawTextsEqual(originalRichNote?.rawText, rawText)
    }

    /**
     * 判断两个 HTML 文本是否相等，通过比较 <body> 标签内的内容
     * @param htmlText1 第一个 HTML 文本
     * @param htmlText2 第二个 HTML 文本
     * @return 如果相等返回 true，否则返回 false
     */
    private fun areHtmlTextsEqual(htmlText1: String?, htmlText2: String): Boolean {
        return areHtmlEqualByBody(htmlText1, htmlText2)
    }

    /**
     * 判断两个原始文本是否相等，考虑文本为空的情况
     * @param rawText1 第一个原始文本
     * @param rawText2 第二个原始文本
     * @return 如果相等返回 true，否则返回 false
     */
    private fun areRawTextsEqual(rawText1: String?, rawText2: String): Boolean {
        // 如果两个文本都为空，则认为相等
        if (HtmlParser.isEmpty(rawText1) && HtmlParser.isEmpty(rawText2)) {
            return true
        }
        // 否则比较 <div> 标签内的内容
        return areRawEqualByDivContent(rawText1, rawText2)
    }

    private fun RichNote.isChanged(): Boolean {
        val isTitleChanged =
            if (TextUtils.isEmpty(mOriginalRichNote?.title) && TextUtils.isEmpty(title)) {
                //原始标题为null，转换后标题数据为""，默认未修改
                false
            } else if ((mOriginalRichNote?.title != title)) {
                true
            } else if (HtmlParser.isEmpty(rawTitle) && HtmlParser.isEmpty(mOriginalRichNote?.rawTitle)) {
                false
            } else {
                mOriginalRichNote?.rawTitle != rawTitle
            }
        // 判断富文本内容是否发生变化
        val isContentChanged: Boolean = isRichNoteContentChanged(mOriginalRichNote, htmlText, rawText)
        val isFolderChanged = mOriginalRichNote?.folderGuid != folderGuid
        val isTopChanged = mOriginalRichNote?.topTime != topTime
        val isSkinChanged = mOriginalRichNote?.skinId != skinId
        val isAlarmChanged = mOriginalRichNote?.alarmTime != alarmTime
        val isRecyclerTimeChanged = mOriginalRichNote?.recycleTime != recycleTime
        isOnlyAttrInfoChanged = !isTitleChanged && !isContentChanged && (isFolderChanged || isTopChanged)
        AppLogger.BASIC.d(TAG,
            "Save to db, isTitleChanged: $isTitleChanged," +
                    " isContentChanged: $isContentChanged, isFolderChanged: $isFolderChanged, isTopChanged: $isTopChanged," +
                    " isSkinChanged: $isSkinChanged, isAlarmChanged: $isAlarmChanged, isRecyclerTimeChanged: $isRecyclerTimeChanged "
        )
        return isTitleChanged || isContentChanged || isFolderChanged || isTopChanged || isSkinChanged || isAlarmChanged || isRecyclerTimeChanged
    }

    fun isMetadataChanged(): Boolean {
        val richNote = mRichData?.metadata ?: return false
        val isFolderChanged = mOriginalRichNote?.folderGuid != richNote.folderGuid
        val isTopChanged = mOriginalRichNote?.topTime != richNote.topTime
        val isSkinChanged = mOriginalRichNote?.skinId != richNote.skinId
        val isAlarmChanged = mOriginalRichNote?.alarmTime != richNote.alarmTime
        val isRecyclerTimeChanged = mOriginalRichNote?.recycleTime != richNote.recycleTime
        AppLogger.BASIC.d(TAG, "[isMetadataChanged],isFolderChanged: $isFolderChanged, " + "isTopChanged: $isTopChanged," +
                " isSkinChanged: $isSkinChanged, isAlarmChanged: $isAlarmChanged, isRecyclerTimeChanged: $isRecyclerTimeChanged ")
        return isFolderChanged || isTopChanged || isSkinChanged || isAlarmChanged || isRecyclerTimeChanged
    }

    private fun isWebItemsChanged(): Boolean {
        val result =  mOriginalRichData?.webItems?.toSet() != mRichData?.webItems?.toSet()
        AppLogger.BASIC.d(TAG, "isWebItemsChanged: $result")
        return result
    }

    private fun resetOriginalRichNote() {
        AppLogger.BASIC.d(TAG, "resetOriginalRichNote")
        mRichData?.metadata?.apply { mOriginalRichNote = copy() }
    }

    private fun clearAnchor(activity: Activity, isCloudSyncClose: Boolean, state: Int) {
        val shouldClearAnchor = isCloudSyncClose && (state != RichNote.STATE_NEW)
        if (shouldClearAnchor) {
            AnchorManager(activity).clearAnchors(SyncAgentContants.DataType.RICH_NOTE)
        }
    }

    /**
     * send notification to notify phone app with note guid
     */
    private fun notifyPhoneWithNoteGuid(guid: String) {
        InCallUINoteManager.notifyPhoneWithNoteGuid(MyApplication.appContext, phoneCallId, guid)
    }

    private fun notifyNoteWidget(activity: Activity?, isAddWidget: Boolean, richNoteWithAttachments: RichNoteWithAttachments) {
        activity?.apply {
            val widgetId = IntentParamsUtil.getIntExtra(mIntent, NoteWidgetProvider.NOTE_WIDGET_ID, AppWidgetManager.INVALID_APPWIDGET_ID)
            if (widgetId != AppWidgetManager.INVALID_APPWIDGET_ID) {
                mIntent?.removeExtra(NoteWidgetProvider.NOTE_WIDGET_ID)
                // 更新widgetId对应笔记
                AppLogger.BASIC.d(TAG, "notify widgetId : " + widgetId + " , guid : " + richNoteWithAttachments.richNote.localId)
                NoteWidgetInfoMap.getInstance(this).replaceGuid(widgetId, richNoteWithAttachments.richNote.localId)
                WidgetUtils.sendNoteDataChangedBroadcast(this, richNoteWithAttachments.richNote.localId)
                if (isAddWidget) {
                    Toast.makeText(activity, R.string.rich_note_widget_added_toast, Toast.LENGTH_SHORT).show()
                }
            } else if (isAddWidget) {
                // 添加桌面插件
                NoteAppWidgetViewModel.noteOfPendingAddToWidget = richNoteWithAttachments
                val success = WidgetUtils.addWidget(this, WidgetUtils.getNoteWidgetCompentName())
                if (!success) {
                    NoteAppWidgetViewModel.noteOfPendingAddToWidget = null
                }
            } else {
                //通知笔记插件 内容已变更
                notifyExistWidget(this, richNoteWithAttachments.richNote.localId)
            }
        }
    }

    private fun notifyExistWidget(activity: Activity, localId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            if (WidgetUtils.hasExistNoteWidget(activity, localId)) {
                WidgetUtils.sendNoteDataChangedBroadcast(activity, localId)
            }
        }
    }

    private fun notifyDataChanged() {
        MyApplication.appContext.contentResolver.notifyChange(NotesProvider.DATA_CHANGE_URI, null)
        MyApplication.appContext.contentResolver.notifyChange(NotesProvider.NOTE_DATA_CHANGE_URI, null)
        MyApplication.appContext.contentResolver.notifyChange(NotesProvider.NOTE_DATA_CHANGE_URI_NEW, null)
    }

    fun addWidgetRichNoteWithAttachments(callback: (() -> Unit)? = null) {
        viewModelScope.launch {
            NoteAppWidgetViewModel.noteOfPendingAddToWidget = repository.convert(mRichData!!)
            callback?.invoke()
        }
    }

    fun getDeleteAttachment(): Set<String>? {
        if (mDeletedAttachmentList.isEmpty()) {
            return null
        }

        val picSet: MutableSet<String> = HashSet()

        mDeletedAttachmentList.forEach { picPath ->
            picSet.add(picPath)
        }

        return picSet
    }

    fun restoreRemovedAttachment(path: String?) {
        if (mDeletedAttachmentList.isEmpty() || (path == null)) {
            return
        }

        mDeletedAttachmentList.remove(path)
    }

    fun copyRichData(): RichData? {
        return mRichData?.halfDeepCopy()
    }

    suspend fun covertRichData(): RichNoteWithAttachments? {
        mRichData?.apply {
            return repository.convert(this, true)
        }
        return null
    }

    @VisibleForTesting
    fun notifySaveNoteComplete(context: Context, guid: String, featureList: List<String>?,isFromEdit: Boolean = false) {
        val intent = Intent(Constants.ACTION_SAVE_NOTE_COMPLETE)
        intent.putExtra(Constants.EXTRA_NOTE_GUID, guid)
        intent.putExtra(Constants.SAVE_NOTE_FEATURE_LIST, featureList.toString())
        intent.putExtra(Constants.IS_FROM_EDIT, isFromEdit)
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent)
        NoteCardWidgetProvider.instance.postUIToCard(false)
    }

    private fun notifySaveInternalFinished(context: Context) {
        val intent = Intent(Constants.ACTION_SAVE_NOTE_FINISHED)
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent)
    }

    fun cacheImageFile(): File {
        mRichData?.coverPictureAttachment?.attachmentId
        val file = File(MyApplication.appContext.cacheDir, "paint_image_${mRichData?.getNoteGuid()}.png")
        return file
    }

    fun cacheDataFile(): File {
        val file = File(MyApplication.appContext.cacheDir, "paint_data_${mRichData?.getNoteGuid()}.paint")
        return file
    }

    fun clearCache() {
        Log.i(TAG, "clearCache")
        try {
            cacheImageFile().delete()
            cacheDataFile().delete()
        } catch (ignore: Exception) {
            AppLogger.BASIC.d(PaintFragment.TAG, "delete quick cache failed: ${ignore.message}")
        }
    }

    fun checkNeedSyncNote(context: Context?) {
        context?.apply {
            if (saveNoteAndNotSync) {
                saveNoteAndNotSync = false
                CloudSyncTrigger.sendDataChangedBroadcast(context)
            }
        }
    }

    fun reFindDoodle(block: (richData: RichData?) -> Unit) {
        mGUID?.let {
            viewModelScope.launch(Dispatchers.IO) {
                delay(DELAY_TIME)
                val richData = repository.find(it)
                block.invoke(richData)
            }
        }
    }

    /**
     * https://odocs.myoas.com/docs/Wr3DVZKEQNHnlWkJ
     * richNote.text 不为空，但转换后 mRichData item 中没有文字，认定是出现异常的数据，将 richNote.text 放到 item.text 中
     * */
    var fixedHtmlTextError = false
    private fun checkAndFixHtmlTextError(note: RichNoteWithAttachments?) {
        if (mRichData == null || note == null || TextUtils.isEmpty(note.richNote.text)) {
            return
        }
        var dataError = false
        if (mRichData?.items.isNullOrEmpty()) {
            val item = Data(Data.TYPE_TEXT, SpannableStringBuilder(note.richNote.text))
            mRichData?.items?.add(item)
            mRichData?.webItems?.clear()
            mRichData?.webItems?.add(item)
            dataError = true
        } else if (mRichData?.items?.size == 1) {
            val data = mRichData?.items?.get(0) ?: return
            if (TextUtils.isEmpty(data.text)) {
                data.text = SpannableStringBuilder(note.richNote.text)
                dataError = true
                mRichData?.webItems?.clear()
                mRichData?.webItems?.add(data)
            }
        }
        if (dataError) {
            note.attachments?.forEachIndexed { index, attachment ->
                if (attachment.type == Attachment.TYPE_PICTURE) {
                    val path = attachment.absolutePath(MyApplication.appContext)
                    val file = File(path)
                    if (file.exists() && file.length() > 0) {
                        if (index == 0) {
                            mRichData?.items?.add(Data(type = Data.TYPE_TEXT, SpannableStringBuilder("")))
                        }
                        mRichData?.items?.add(Data(type = Data.TYPE_ATTACHMENT, attachment = attachment))
                        mRichData?.items?.add(Data(type = Data.TYPE_TEXT, SpannableStringBuilder("")))
                    }
                }
            }
            fixedHtmlTextError = true
            mRichData?.metadata?.rawText = ""
            mRichData?.metadata?.htmlText = ""
            //通过toRichNoteWithAttachmentsFromHtml重新生成htmlText
            mRichData?.toRichNoteWithAttachmentsFromHtml()
            AppLogger.BASIC.d(TAG, "checkAndFixHtmlTextError find error data")

            //数据纠错埋点
            DataStatisticsHelper.noteUserOps(TAG, "01010209", mRichData?.metadata)
        }
    }

    suspend fun getWebViewData(webContainer: IWebViewContainerCoverpaint): NoteDataInTipTap? = suspendCancellableCoroutine { continuation ->
        setGetHtmlValue(GETING_HTML)
        kotlin.runCatching {
            webContainer.getTextAndHtml {
                setGetHtmlValue(NOT_GETING_HTML)
                val noteData = Gson().fromJson(it, NoteDataInTipTap::class.java)
                if (noteData?.text == null && noteData?.htmlData == null) {
                    // 如果是获取数据异常则埋点上报
                    AppLogger.BASIC.d(TAG, "getWebViewData, data error")
                    val noteDataError = Gson().fromJson(it, NoteHtmlDataError::class.java)
                    StatisticsUtils.setTextAndHtmlError(
                        noteDataError.bridgeName,
                        noteDataError.errorMessage
                    )
                }
                continuation.resume(noteData)
            }
        }.onFailure {
            AppLogger.BASIC.d(TAG, "getWebViewData ${it.message}")
            StatisticsUtils.setTextAndHtmlError(GET_WEB_VIEW_DATA, it.message)
            continuation.resumeWithException(it)
            setGetHtmlValue(GETING_ERROR)
        }
        continuation.invokeOnCancellation {
            setGetHtmlValue(GETING_ERROR)
            AppLogger.BASIC.w(TAG, "getWebViewNoteData canceled")
        }
    }

    suspend fun selectRangeTextAIGC(from: Int, to: Int, webContainer: IWebViewContainer): String = suspendCancellableCoroutine { continuation ->
        webContainer.selectRangeTextAIGC(from, to) { text ->
            continuation.resume(text)
        }
        continuation.invokeOnCancellation {
            AppLogger.BASIC.w(TAG, "selectRangeText canceled:${it?.message}")
        }
    }

    suspend fun selectAndGetAllText(webContainer: IWebViewContainer): String = suspendCancellableCoroutine { continuation ->
        webContainer.selectAndGetAllText { text ->
            continuation.resume(text)
        }
        continuation.invokeOnCancellation {
            AppLogger.BASIC.w(TAG, "selectAndGetAllText canceled:${it?.message}")
        }
    }
    suspend fun getAllHTML(webContainer: IWebViewContainerCoverpaint): String =
        suspendCancellableCoroutine { continuation ->
            webContainer.getAllHTML { text ->
                continuation.resume(text)
            }
            continuation.invokeOnCancellation {
                AppLogger.BASIC.w(TAG, "getAllHTML canceled:${it?.message}")
            }
        }
    suspend fun selectAndGetForwardAllText(webContainer: IWebViewContainer): String = suspendCancellableCoroutine { continuation ->
        webContainer.selectAndGetForwardAllText { text ->
            continuation.resume(text)
        }
        continuation.invokeOnCancellation {
            AppLogger.BASIC.w(TAG, "selectAndGetForwardAllText canceled:${it?.message}")
        }
    }

    suspend fun getWebViewAttachments(webContainer: IWebViewContainer): String = suspendCancellableCoroutine { continuation ->
        webContainer.getAttachments {
            continuation.resume(it)
        }
        continuation.invokeOnCancellation {
            AppLogger.BASIC.w(TAG, "getWebViewAttachments canceled")
        }
    }

    fun handleScheduleEntity(fragment: WVNoteViewEditFragment): Boolean {
        if (entities?.entities?.isEmpty() == true) {
            return false
        }

        val scheduleEntities = Entities(ArrayList())
        entities?.entities?.apply {
            for (i in this.size - 1 downTo 0) {
                if (this[i].type == Entity.SCHEDULE && this[i].scheduleTime == null) {
                    scheduleEntities.entities.add(this.removeAt(i))
                }
            }
        }

        if (scheduleEntities.entities.isEmpty()) {
            return false
        }

        SemanticFactory.get()?.apply {
            val result = initModel(MyApplication.appContext)
            AppLogger.THIRDLOG.d(TAG, "50010602,semantic init $result")
            if (result) {
                var removeCount = 0
                CoroutineScope(Dispatchers.IO).launch {
                    scheduleEntities.entities.forEach { entity ->
                        val semantic = process(MyApplication.appContext, entity.name)
                        AppLogger.BASIC.d(TAG, "semantic schedule ${XorEncryptUtils.enOrDecrypt(entity.name, 1)} $semantic")
                        if (semantic == null) {
                            removeCount++
                        } else {
                            if (semantic.isNoTimeDateValid()) {
                                entity.scheduleTime = semantic.translate2Date().time.toString()
                                if (!semantic.getTimeInitialed()) {
                                    entity.type = Entity.NO_TIME_SCHEDULE
                                }
                                entities?.entities?.add(entity)
                                AppLogger.THIRDLOG.d(TAG, "50010602,viewModel entityScheduleTime:${entity.scheduleTime}")
                            } else {
                                removeCount++
                            }
                        }
                    }

                    AppLogger.THIRDLOG.d(TAG, "50010602,removeCount:$removeCount")
                    onEntityChanged(fragment, entities)
                    withContext(Dispatchers.Main) {
                        AppLogger.BASIC.d(TAG, "processScheduleEntity")
                        fragment.setSummaryEntity()
                    }
                }

                return true
            }
        }

        return false
    }

    private fun onEntityChanged(fragment: WVNoteViewEditFragment, entities: Entities?) {
        kotlin.runCatching {
            val noteId = mGUID ?: return
            val entitiesString = GsonBuilder().excludeFieldsWithoutExposeAnnotation().create().toJson(entities).toString()
            RichNoteRepository.updateSpeechLogEntity(noteId = noteId, entitiesString)
            AppLogger.BASIC.d(TAG, "speechLogInfo is null ${mRichData?.speechLogInfo == null} entities size ${entities?.entities?.size}")
        }.onFailure {
            AppLogger.BASIC.e(TAG, "onEntityChanged fail $it")
        }
    }

    fun parserNoteForIntent(intent: Intent): RichNoteWithAttachments? {
        val bundle = intent.extras ?: Bundle()
        var note: RichNoteWithAttachments? = null
        if (bundle.containsKey(WVNoteViewEditFragment.ARGUMENTS_EXTRA_NOTE)) {
            note = IntentParamsUtil.getParcelableExtra(intent, WVNoteViewEditFragment.ARGUMENTS_EXTRA_NOTE)
            if (note == null) {
                AppLogger.BASIC.e(TAG, "parcelable note is null")
                val noteBinder = bundle.getBinder(WVNoteViewEditFragment.ARGUMENTS_EXTRA_NOTE)
                note = (noteBinder as? NoteBinder)?.getBitMap()
            }
        }
        AppLogger.BASIC.e(TAG, "note not null:${note != null}")
        return note
    }

    fun changeSkin(skinId: String?, forceChange: Boolean = false, withAnim: Boolean = false, withRichNoteLiveInvoke: Boolean = false) {
        if (skinId.isNullOrEmpty()) {
            AppLogger.BASIC.w(TAG, "changeSkin error, id is empty!")
            return
        }
        mRichData?.metadata?.skinId = skinId
        val realSkinId = if (isPadOrExport && (!SkinData.isAddManualSkin || !SkinManager.isEmbedSkin(skinId))) {
            SkinData.COLOR_SKIN_WHITE
        } else {
            skinId
        }
        viewModelScope.launch {
            _skinChange.emit(
                SkinChange(
                    skinId = realSkinId, forceChange = forceChange, withAnim = withAnim, withRichNoteLiveInvoke = withRichNoteLiveInvoke
                )
            )
        }
    }

    fun getAllVoices(): MutableList<Data> {
        val listAudioItems = mutableListOf<Data>()
        mRichData?.findSpeechAudioItem()?.apply {
            listAudioItems.add(this)
        }
        mRichData?.findVoiceItems()?.apply {
            listAudioItems.addAll(this.toList())
        }
        mRichData?.findIdentifyVoiceItems()?.apply {
            listAudioItems.addAll(this.toList())
        }
        return listAudioItems
    }

    fun pasteAttach(context: Context?, pasteAttach: PasteAttach): String? = runBlocking {
        context ?: return@runBlocking null
        val richData = mRichData ?: return@runBlocking null
        if (richData.reachImageLimit()) {
            viewModelScope.launch {
                context.toast(R.string.toast_excceed_limit_of_attrs)
            }
            return@runBlocking null
        }
        val src = pasteAttach.src
        val fileUri = if (src.startsWith('/')) {
            Uri.fromFile(File(src))
        } else {
            Uri.parse(src)
        }
        when (pasteAttach.type) {
            LONG_CLICK_TYPE_IMAGE -> {
                val attachId = UUID.randomUUID().toString()
                val picAttachment = Attachment(attachmentId = attachId, type = Attachment.TYPE_PICTURE, richNoteId = richData.getNoteGuid())
                val destPath = picAttachment.absolutePath(context)
                var copyResult = false
                val saveJob = viewModelScope.async(Dispatchers.IO) {
                    copyResult = PasteUtils.copyFileFromUri(context, fileUri, destPath)
                    if (copyResult) {
                        val option = BitmapFactory.Options()
                        option.inJustDecodeBounds = true
                        BitmapFactory.decodeFile(destPath, option)
                        picAttachment.picture = Picture(option.outWidth, option.outHeight)
                        AppLogger.BASIC.i(TAG, "pasteAttach, width:${option.outWidth}, height:${option.outHeight}")
                    }
                }
                saveJob.await()
                if (copyResult) {
                    dataController.add(richData, Data(Data.TYPE_ATTACHMENT, null, picAttachment), null)
                }
                val pasteResult = PasteAttachResult(
                    result = copyResult,
                    type = pasteAttach.type,
                    src = picAttachment.relativePath(),
                    attachId = picAttachment.attachmentId,
                    picWidth = picAttachment.picture?.width ?: 0,
                    picHeight = picAttachment.picture?.height ?: 0
                )
                return@runBlocking Gson().toJson(pasteResult)
            }
        }
        return@runBlocking null
    }

    private fun parseTableCount(html: String?): Int {
        if (html == null) {
            return 0
        }
        return Regex("<table").findAll(html).count()
    }

    fun statisticShareByText(context: Context?, webViewContainer: IWebViewContainerCoverpaint?) {
        if (webViewContainer == null) {
            return
        }
        val ctx = context?.applicationContext
        viewModelScope.launch(Dispatchers.IO) {
            val data = getWebViewData(webViewContainer)
            val tableCount = parseTableCount(data?.htmlData?.content)
            StatisticsUtils.setEventShareNoteByText(ctx, tableCount)
        }
    }

    fun statisticShareByPicture(context: Context?, webViewContainer: IWebViewContainerCoverpaint?) {
        if (webViewContainer == null) {
            return
        }
        val ctx = context?.applicationContext
        viewModelScope.launch(Dispatchers.IO) {
            val data = getWebViewData(webViewContainer)
            val tableCount = parseTableCount(data?.htmlData?.content)
            StatisticsUtils.setEventShareNoteByPic(ctx, StatisticsUtils.TYPE_SHARE_NOTE_BY_PIC_DEFULT, tableCount)
        }
    }

    fun statisticShareByDoc(context: Context?, webViewContainer: IWebViewContainerCoverpaint?) {
        if (webViewContainer == null) {
            return
        }
        val ctx = context?.applicationContext
        viewModelScope.launch(Dispatchers.IO) {
            val data = getWebViewData(webViewContainer)
            val tableCount = parseTableCount(data?.htmlData?.content)
            StatisticsUtils.setEventNoteShareByDoc(ctx, tableCount)
        }
    }

    /**
     * @param editDuration 毫秒数
     */
    @Suppress("MagicNumber")
    fun statisticEditOrNewNote(context: Context?, editDuration: Long,isHigherVersion:Boolean) {
        val ctx = context?.applicationContext
        GlobalScope.launch(Dispatchers.IO) {
            val data = mRichData?.metadata?.htmlText
            val tableCount = parseTableCount(data)
            if (!mIsCreateNote) {
                StatisticsUtils.setEventOpenMemo(
                    ctx,
                    mNoteChanged,
                    tableCount,
                    editDuration / 1000,
                    isHigherVersion
                )
            } else {
                StatisticsUtils.setEventNewNote(ctx, StatisticsUtils.TYPE_ALL_NEW_NOTE, tableCount, editDuration / 1000)
            }
        }
    }

    fun statisticEveryTableInfo(context: Context?) {
        val ctx = context?.applicationContext
        GlobalScope.launch(Dispatchers.IO) {
            val data = mRichData?.metadata?.htmlText ?: return@launch
            val trRegex = Regex("<tr>")
            val tdRegex = Regex("<td>")
            val infos = data.split("<table").filter { it.contains("<tr>") }.map {
                val tr = trRegex.findAll(it).count()
                val td = tdRegex.findAll(it).count() / tr
                tr to td
            }
            RichStatisticsUtils.setEventTableInfoBatch(ctx, infos)
        }
    }

    fun statisticSaveFileInfo() {
        val count = mRichData?.countOfAudioVideoAndDoc() ?: 0
        ExternalFileStatisticUtils.saveNoteFileCount(count)
    }
}