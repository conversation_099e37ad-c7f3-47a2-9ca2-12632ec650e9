/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - NoteDetailMaskHelper.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/2/6
 ** Author: ********
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  AAA       2024/2/6     1.0     create file
 ****************************************************************/

package com.nearme.note.activity.richedit

import android.view.View
import android.view.View.OnClickListener
import android.view.ViewStub
import android.webkit.WebView
import com.nearme.note.paint.coverdoodle.CoverDoodlePresenter
import com.nearme.note.paint.coverdoodle.CoverDoodlePresenterCoverPaint
import com.oplus.note.logger.AppLogger

class NoteDetailMaskHelper(private val viewStub: ViewStub) {
    private val tag = "NoteDetailMaskHelper"
    private var webView: WebView? = null
    private var coverDoodlePresenter: CoverDoodlePresenter? = null
    private var maskScreen: NoteDetailMaskView? = null
    private var coverDoodlePresenterCoverPaint: CoverDoodlePresenter? = null
    private var maskScreenCoverPaint: NoteDetailMaskView? = null
    private var onClickListener: OnClickListener? = null
    var visibility: Int = View.GONE
        get() {
            return maskScreen?.visibility ?: View.GONE
        }
        set(value) {
            if (value == View.VISIBLE) {
                initMaskView()
            }
            maskScreen?.visibility = value
            field = value
        }

    private fun initMaskView(): NoteDetailMaskView? {
        if (maskScreen == null) {
            runCatching {
                maskScreen = (viewStub.inflate() as? NoteDetailMaskView)?.apply {
                    setOnClickListener {
                        onClickListener?.onClick(it)
                    }
                    setScrollChild(webView, coverDoodlePresenter)
                }
                AppLogger.BASIC.d(tag, "maskScreen = $maskScreen")
            }.onFailure {
                AppLogger.BASIC.d(tag, "initMaskView ${it.message}")
            }
        }
        return maskScreen
    }

    fun setOnClickListener(onClickListener: OnClickListener) {
        this.onClickListener = onClickListener
    }

    fun setScrollChild(webView: WebView?, coverDoodlePresenter: CoverDoodlePresenter?) {
        this.webView = webView
        this.coverDoodlePresenter = coverDoodlePresenter
        this.maskScreen?.setScrollChild(webView, coverDoodlePresenter)
    }
    fun setScrollChildCoverPaint(webView: WebView?, coverDoodlePresenter: CoverDoodlePresenterCoverPaint?) {
        this.webView = webView
        this.coverDoodlePresenter = coverDoodlePresenterCoverPaint
        this.maskScreenCoverPaint?.setScrollChild(webView, coverDoodlePresenterCoverPaint)
    }
}