/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : WVAdapter.kt
 * Description    : WVAdapter.kt
 * Version        : 1.0
 * Date           : 2023/11/14
 * Author         : Chandler
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * Chandler     2023/11/14         1.0           create
 */
package com.nearme.note.activity.richedit.webview

import android.app.Activity
import android.graphics.Bitmap
import android.net.Uri
import android.view.DragEvent
import android.view.ViewTreeObserver
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.heytap.tbl.webkit.WebView
import com.nearme.note.activity.edit.ClipDataParseCallback
import com.nearme.note.activity.richedit.DragCallback
import com.nearme.note.activity.richedit.UiMode
import com.nearme.note.activity.richedit.entity.RichData
import com.nearme.note.activity.richedit.entity.getPicCount
import com.nearme.note.skin.bean.Skin
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import com.oplus.note.utils.toast
import com.oplus.notes.webviewcoverpaint.container.api.IWebViewContainer
import com.oplus.notes.webviewcoverpaint.container.api.IWebViewContainerCoverpaint
import com.oplus.notes.webviewcoverpaint.container.api.InputContent
import com.oplus.notes.webviewcoverpaint.container.api.TiptapFocusInfo
import com.oplus.richtext.editor.view.focus.FocusInfo
import com.oplus.richtext.editor.view.onClipExitLister
import com.oplus.richtext.editor.view.toolbar.content.AbsToolbar

class WVAdapterCoverPaint(
    private val webView: WebView,
    private val absToolbar: AbsToolbar?,
    val focusInfo: FocusInfo,
    val currentMode: UiMode,
    private val fragment: WVNoteViewEditFragmentCoverPaint,
    private val webViewContainer: IWebViewContainerCoverpaint? = null
) : ClipDataParseCallback {
    companion object {
        const val TAG = "WVAdapter"
        private const val DURATION_HIDE_TITLE = 300
        private const val DEFAULT_HINT_START = 0
    }

    var mIsDrag: Boolean = false
    var mRichData: RichData? = null
    var titleHeight: Float = 0f
        set(value) {
            field = value
            hideTitleListener?.invoke(value)
            hideTitleListener = null
        }
    var clipExitLister: onClipExitLister? = null
    val maxClipCount
        get() = RichData.PICS_UPPER_BOUND - (mRichData?.getPicCount() ?: 0)
    var dragCallback: DragCallback? = null
    var mInOcrHint = false

    private var mSpeechType = -1
    private var mNeedAddUndo: Boolean = false
    private var showSoftInput: Boolean = false
    private var notifyProgressing: Boolean = false
    private var mScale = 1.0F
    private var hideTitleListener: ((titleHeight: Float) -> Unit)? = null
    var mIsInMultiWindowMode = false

    fun clearCursorVisible() {
        AppLogger.BASIC.d(TAG, "clearCursorVisible")
        if (fragment.needClearFocus()) {
            webViewContainer?.ignoreActionModeInvalidate(true)
            webViewContainer?.clearFocus()
        }
    }

    fun clearFocused() {
        AppLogger.BASIC.d(TAG, "clearFocused")
        if (fragment.needClearFocus()) {
            webViewContainer?.clearFocus()
        }
        absToolbar?.hideSoftInput()
    }

    fun requestFocused(pos: String = "") {
        AppLogger.BASIC.d(TAG, "requestFocused $pos")
        webViewContainer?.focus(pos) {
            // 弹窗软件盘放在View加载之后，防止弹出软键盘与View加载冲突，导致软键盘弹不出来
            val activity = (fragment.context as? Activity) ?: return@focus
            if (!activity.isFinishing && !activity.isDestroyed) {
                webView.requestFocus()
                absToolbar?.showSoftInput()
            }
        }
    }

    fun notifyDataSetChanged() {
    }

    fun setNeedAddUndo(needAddUndo: Boolean) {
        mNeedAddUndo = needAddUndo
    }

    fun setSpeechType(speechType: Int) {
        mSpeechType = speechType
    }

    fun setOnClipExitListener(onClipExitLister: onClipExitLister) {
        clipExitLister = onClipExitLister
    }

    fun removeText(ocrHintIndex: Int, ocrHintStart: Int = -1, ocrHintLength: Int) {
    }

    fun setNeedHideInputMethod(needHideInput: Boolean?) {
    }

    fun setSharePicture(isSharePicture: Boolean) {
    }

    override fun insertClipDataText(text: CharSequence) {
        insertText(
            InputContent(
                text = text.toString(),
                focusedEditor = if (fragment.isTitleFocus != false) TiptapFocusInfo.TITLE_FOCUSED else TiptapFocusInfo.CONTENT_FOCUSED,
                isDragDrop = true
            )
        )
    }

    override suspend fun insertClipDataPic(
        type: Int,
        uri: Uri,
        isMulti: Boolean,
        event: DragEvent,
        isTargetTitle: Boolean,
        isFromBitchImageFinal: Boolean
    ) {
        dragCallback?.doDragInsertPic(
            type,
            uri,
            isMulti,
            event,
            isTargetTitle,
            isFromBitchImageFinal = isFromBitchImageFinal
        )
    }

    override fun showToastNoteReachMaximumImageNumber() {
        fragment.toast(R.string.toast_excceed_limit_of_attrs)
        AppLogger.BASIC.d(TAG, "reachMaxImageCountLimit")
    }

    fun insertText(content: InputContent) {
        webViewContainer?.setText(content)
        if (currentMode.isEditMode()) {
            absToolbar?.setImeVisiblie(true)
        }
    }

    fun noteNotifyItemRangeChanged(itemIndex: Int, count: Int) {
    }

    fun lastIndex(): Int = 0

    fun clearSearchText() {
    }

    fun removeForegroundColorSpan() {
    }

    fun updateFocused(showSoftInput: Boolean) {
    }

    fun cache(attrGuid: String?, bitmap: Bitmap?) {
    }

    fun updateFocusInfo(index: Int, start: Int, end: Int) {
        focusInfo.updateFocusContent(index, start, end)
    }

    fun notifyDataSetChangedBeforeScrollToFocusView(
        picHeight: Int,
        isManu: Boolean = false,
        isInsertPic: Boolean = false,
        isDelPic: Boolean = false,
        isUndo: Boolean = false,
        isRedo: Boolean = false,
        listener: ((showSoftInputSuccess: Boolean) -> Unit)? = null,
    ) {
    }

    fun clearCache() {
    }

    fun resetSpeechEditText() {
    }

    fun updateCheckboxRes(skinId: String, checkbox: Skin.EditPage.Checkbox) {
    }

    fun updateTitleBgData(skinId: String, titleBg: Skin.EditPage.Background.TitleBg) {
    }

    fun adjustFocusToContent() {
    }

    fun notifyDataSetChangedBeforeHideTitleAndShowSoftInput(listener: (() -> Unit)) {
        AppLogger.BASIC.d(TAG, "notifyDataSetChangedBeforeHideTitleAndShowSoftInput: titleHeight=$titleHeight")
        showSoftInput = true
        notifyProgressing = true
        onGlobalLayoutListener {
            if (titleHeight.compareTo(0.0f) > 0) {
                if (fragment.firstCreate) {
                    webViewContainer?.smoothScrollTo(0, titleHeight.toInt(), DURATION_HIDE_TITLE)
                    fragment.firstCreate = false
                }
            } else {
                hideTitleListener = { height ->
                    if (fragment.firstCreate) {
                        webViewContainer?.smoothScrollTo(0, height.toInt(), DURATION_HIDE_TITLE)
                        fragment.firstCreate = false
                    }
                }
            }
            onGlobalLayoutListener {
                showSoftInput = false
                notifyProgressing = false
                listener.invoke()
            }
        }
    }

    private fun onGlobalLayoutListener(listener: () -> Unit) {
        val onGlobalLayoutListener = object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                webView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                listener.invoke()
            }
        }
        webView.viewTreeObserver.addOnGlobalLayoutListener(onGlobalLayoutListener)
    }

    fun getTodoEditText(isViewMode: Boolean) {
    }

    fun switchRequestFocus() {
    }

    fun insertHint(hintString: String) {
        webViewContainer?.insertHintText(hintString)
        mInOcrHint = true
    }

    fun changeHint(mIndex: Int, mStart: Int, hintString: String, mLastHintSize: Int): Int {
        return DEFAULT_HINT_START
    }

    fun clearHint(mHintIndex: Int) {
        AppLogger.BASIC.d(TAG, "clearHint: mInOcrHint=$mInOcrHint")
        if (mInOcrHint) {
            webViewContainer?.deleteHintText()
        }
    }

    fun removeHint(mIndex: Int) {
        AppLogger.BASIC.d(TAG, "removeHint: mInOcrHint=$mInOcrHint")
        if (mInOcrHint) {
            webViewContainer?.deleteHintText()
            mInOcrHint = false
        }
    }
}