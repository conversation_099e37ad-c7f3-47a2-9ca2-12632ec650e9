/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: AddonWrapper.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/10/8
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.util

import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.graphics.Point
import android.graphics.Rect
import android.media.AudioAttributes
import android.media.SoundPool
import android.os.Build
import android.view.SurfaceControl
import android.view.WindowManager
import android.widget.ImageView
import androidx.annotation.RequiresApi
import com.nearme.note.activity.richedit.PocketStudioWrapper
import com.nearme.note.main.UIConfigMonitor
import com.nearme.note.view.helper.UiHelper
import com.oplus.forcealertcomponent.ScreenTopAnimationWindow
import com.oplus.note.BuildConfig
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import com.oplus.note.osdk.proxy.OplusFeatureConfigManagerProxy
import com.oplus.note.osdk.proxy.OplusFlexibleWindowManagerProxy
import com.oplus.note.osdk.proxy.OplusScreenShotOptionsProxy
import com.oplus.note.osdk.proxy.OplusScreenShotResultProxy
import com.oplus.note.osdk.proxy.OplusWindowInfoProxy
import com.oplus.note.osdk.proxy.OplusWindowManagerProxy
import com.oplus.note.osdk.proxy.OplusZoomWindowManagerProxy

class AddonWrapper {

    object OplusScreenShotManager{
        private const val TAG = "OplusScreenShotManager"
        private const val WINDOWING_MODE_SPLIT = 6
        private const val WINDOWING_MODE_SMALL_WINDOW = 100
        private var soundPool: SoundPool? = null
        private var soundId:Int? = -1
        private const val WINDOW_TYPE_1 = 2014
        private const val WINDOW_TYPE_2 = 2019
        private const val WINDOW_TYPE_3 = 2024 //caihong 和 blue

        @RequiresApi(Build.VERSION_CODES.Q)
        fun halfScreenShot(imageView: ImageView?, palySoundContext: Context? = null): Pair<Bitmap?,Boolean> {
            var bitmap: Bitmap? = null
            var containsSecure = false
            try {
                val wm = OplusWindowManagerProxy()
                val allVisibleWindowInfo: List<OplusWindowInfoProxy> = wm.allVisibleWindowInfo().reversed()
                val screenOptions = OplusScreenShotOptionsProxy()
                var containsSmallWindow = false
                val arrayList = ArrayList<SurfaceControl>()
                var index = 0
                val list = ArrayList<Int>()
                var lastSize = 0
                for (windowInfo in allVisibleWindowInfo) {
                    val windowingMode = windowInfo.windowingMode
                    val name = windowInfo.componentName
                    containsSecure = (windowInfo.windowAttributes?.flags ?: 0) and WindowManager.LayoutParams.FLAG_SECURE != 0
                    if (containsSecure) {
                        return Pair(bitmap, containsSecure)
                    }
                    val windowTypeContain =
                        windowInfo.type == WINDOW_TYPE_1 || windowInfo.type == WINDOW_TYPE_2 || windowInfo.type == WINDOW_TYPE_3
                    if (windowInfo.type == WindowManager.LayoutParams.TYPE_STATUS_BAR || windowTypeContain
                        || (windowInfo.windowName?.contains("ScreenDecorOverlay") == true)
                    ) {
                        //排除navigation statusbar floatbar
                        windowInfo.mSurfaceControl?.let { arrayList.add(it) }
                    }
                    if (windowingMode == WINDOWING_MODE_SPLIT && name != null) {
                        if ((name.className != "com.nearme.note.activity.richedit.QuickNoteViewRichEditActivity")
                            && (name.className != "com.nearme.note.ocr.OcrConverterActivity")
                            && (windowInfo.mFrame != null)) {
                            screenOptions.run {
                                if (!containsSmallWindow) {
//                                    this.mLayer = windowInfo.mSurfaceControl
                                }
                                val size = windowInfo.mFrame!!.width() * windowInfo.mFrame!!.height()
                                if (size >= lastSize && (windowInfo.windowAttributes?.type != 1000)) {
                                    this.mSourceCrop = windowInfo.mFrame
                                }
                                if (size >= lastSize) {
                                    lastSize = size
                                }
//                                if (windowInfo.windowAttributes.type != 1000) {
//                                    this.mSourceCrop = windowInfo.mFrame
//                                }
                                list.add(windowInfo.taskId)
                            }
                        }
                    }
                    if (windowingMode == WINDOWING_MODE_SMALL_WINDOW && name != null) {
                        if (name.className != "com.nearme.note.activity.richedit.QuickNoteViewRichEditActivity" && name.className != "com.nearme.note.ocr.OcrConverterActivity") {
                            containsSmallWindow = true
                            if (screenOptions.mSourceCrop != null) {
                                screenOptions.mLayer = null
                            }
                        }
                    }
                }
                if (containsSmallWindow) {
                    screenOptions.mFullDisplay = true
                }
                val excludeLayers: Array<SurfaceControl?> =
                    arrayOfNulls(arrayList.size)
                screenOptions.mExcludeLayers = excludeLayers
                for (surface in arrayList) {
                    screenOptions.mExcludeLayers!![index] = surface
                    index++
                }
                screenOptions.mTasks = IntArray(list.size)
                for ((i, taskId) in list.withIndex()) {
                    screenOptions.mTasks!![i] = taskId
                }
                val buffer: OplusScreenShotResultProxy = wm.getScreenshot(screenOptions)
                palySoundContext?.apply {
                    playSound(this)
                    ScreenTopAnimationWindow.showTopWindow(this, screenOptions.mSourceCrop)
                }
                bitmap = buffer.mHardwareBuffer?.let {
                    Bitmap.wrapHardwareBuffer(
                        it,
                        ColorSpace.get(ColorSpace.Named.SRGB)
                    )
                }
                bitmap?.let {
                    imageView?.setImageBitmap(bitmap)
                }
            } catch (e: Throwable) {
                AppLogger.BASIC.e(TAG, "halfScreenShot failed not support ${e.message}")
            }
            return Pair(bitmap, containsSecure)
        }

        @RequiresApi(Build.VERSION_CODES.Q)
        fun fullScreenShot(imageView: ImageView?, rect: Rect, palySoundContext: Context? = null): Pair<Bitmap?, Boolean> {
            var bitmap: Bitmap? = null
            var containsSecure = false
            try {
                val wm = OplusWindowManagerProxy()
                val allVisibleWindowInfo: List<OplusWindowInfoProxy> = wm.allVisibleWindowInfo().reversed()
                val screenOptions = OplusScreenShotOptionsProxy()

                val arrayList = ArrayList<SurfaceControl>()
                var index = 0
                for (windowInfo in allVisibleWindowInfo) {
                    val name = windowInfo.componentName
                    containsSecure = (windowInfo.windowAttributes?.flags ?: 0) and WindowManager.LayoutParams.FLAG_SECURE != 0
                    if (containsSecure) {
                        return Pair(bitmap, containsSecure)
                    }
                    val windowTypeContain =
                        windowInfo.type == WINDOW_TYPE_1 || windowInfo.type == WINDOW_TYPE_2 || windowInfo.type == WINDOW_TYPE_3
                    if (windowInfo.windowName == "InputMethod" || windowInfo.type == WindowManager.LayoutParams.TYPE_STATUS_BAR || windowTypeContain
                        || (windowInfo.windowName?.contains("ScreenDecorOverlay") == true)
                    ) {
                        //排除navigation statusbar floatbar
                        windowInfo.mSurfaceControl?.let { arrayList.add(it) }
                    }
                    if (name != null) {
                        if (name.className == "com.nearme.note.activity.richedit.QuickNoteViewRichEditActivity") {
                            windowInfo.mSurfaceControl?.let { arrayList.add(it) }
                        } else
                            if (name.className == "com.nearme.note.ocr.OcrConverterActivity") {
                                windowInfo.mSurfaceControl?.let { arrayList.add(it) }
                            }
                    }
                }
                //launcher作为sourceCrop
                screenOptions.mSourceCrop = rect
                val excludeLayers: Array<SurfaceControl?> =
                    arrayOfNulls(arrayList.size)
                screenOptions.mExcludeLayers = excludeLayers
                for (surface in arrayList) {
                    screenOptions.mExcludeLayers!![index] = surface
                    index++
                }
                screenOptions.mFullDisplay = true

                val buffer: OplusScreenShotResultProxy = wm.getScreenshot(screenOptions)
                palySoundContext?.apply {
                    playSound(this)
                    ScreenTopAnimationWindow.showTopWindow(this, screenOptions.mSourceCrop)
                }
                bitmap = buffer.mHardwareBuffer?.let {
                    Bitmap.wrapHardwareBuffer(
                        it,
                        ColorSpace.get(ColorSpace.Named.SRGB)
                    )
                }
            } catch (e: Throwable) {
                AppLogger.BASIC.e(TAG, "fullScreenShot failed not support ${e.message}")
            }

            return Pair(bitmap, containsSecure)
        }

        fun forbiddenScreenShot(context: Activity?): Boolean {
            if (context == null) return false
            try {
                val wm = OplusWindowManagerProxy()
                val allVisibleWindowInfo: List<OplusWindowInfoProxy> = wm.allVisibleWindowInfo()
                var hasLauncher = false
                var hasSplitScreen = false
                val isSupportPocketStudio = OplusFlexibleWindowManagerProxy.isSupportPocketStudio(context)
                for (windowInfo in allVisibleWindowInfo) {
                    hasSplitScreen = if (isSupportPocketStudio) {
                        OplusFlexibleWindowManagerProxy.isMinimizedPocketStudio() || hasSplitScreen
                    } else {
                        AppLogger.BASIC.d(TAG, "packageName = ${windowInfo.packageName},windowingMode=${windowInfo.windowingMode}")
                        (windowInfo.windowingMode == 6 || hasSplitScreen)
                    }
                    hasLauncher = (windowInfo.packageName == "com.android.launcher" || hasLauncher)
                }
                return hasLauncher && hasSplitScreen && OplusFlexibleWindowManagerProxy.isInFreeFormMode(context)
            } catch (e: Throwable) {
                AppLogger.BASIC.e(TAG, "forbiddenScreenShot not support windowInfo")
            }
            return false
        }

        fun getScreen(context: Context): IntArray {
            var mRealSizeWidth = 0
            var mRealSizeHeight = 0

            val display = (context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager).defaultDisplay
            val outPoint = Point()
            if (Build.VERSION.SDK_INT >= 19) {
                // include navigation bar
                display.getRealSize(outPoint)
            } else {
                // exclude navigation bar
                display.getSize(outPoint)
            }
            if (outPoint.y > outPoint.x) {
                mRealSizeHeight = outPoint.y
                mRealSizeWidth = outPoint.x
            } else {
                mRealSizeHeight = outPoint.x
                mRealSizeWidth = outPoint.y
            }
            val orientation = context.resources.configuration.orientation
            return if (orientation == Configuration.ORIENTATION_LANDSCAPE) intArrayOf(
                mRealSizeHeight,
                mRealSizeWidth
            ) else intArrayOf(mRealSizeWidth, mRealSizeHeight)
        }

        private fun playSound(context: Context){
            if (soundPool == null) {
                soundPool = SoundPool
                    .Builder()
                    .setMaxStreams(1)
                    .setAudioAttributes(
                        AudioAttributes.Builder().setLegacyStreamType(1).build()
                    ).build()
                soundId = soundPool?.load(context,R.raw.capture,1)
                soundPool?.setOnLoadCompleteListener { soundPool, _, status ->
                    if (status == 0) {
                        if (soundId != -1) {
                            soundPool.play(soundId!!, 1.0f, 1.0f, 10, 0, 1.0f)
                        }
                    }
                }
            }else{
                soundPool?.play(soundId!!, 1.0f, 1.0f, 10, 0, 1.0f)
            }
        }

        fun needAlertSercureScreen(): Boolean {
            var containsSecure = false
            try {
                val wm = OplusWindowManagerProxy()
                val allVisibleWindowInfo: List<OplusWindowInfoProxy> = wm.allVisibleWindowInfo()

                for (windowInfo in allVisibleWindowInfo) {
                    containsSecure = (windowInfo.windowAttributes?.flags ?: 0) and WindowManager.LayoutParams.FLAG_SECURE != 0
                    if (containsSecure) {
                        return containsSecure
                    }
                }
            } catch (e: Throwable) {
                AppLogger.BASIC.e(TAG, "needAlertSercureScreen not support windowInfo")
            }

            return containsSecure
        }

        fun release() {
            soundPool?.release()
            soundPool = null
            soundId = -1
        }

        fun supportScreenShot(): Boolean {
            return Build.VERSION.SDK_INT >= 33
        }

        fun supportOcrText(context: Context?): Boolean {
            if (context == null) return false
            val isSupported = OcrDownloadUtils.isSupport(context)
            AppLogger.BASIC.d(
                TAG,
                "ocrSupport $isSupported isExport ${BuildConfig.isExport}; supportScreenShot:${supportScreenShot()}; supportWindowChange: ${
                    supportWindowChange(
                        context.packageName
                    )
                }"
            )
            return isSupported && !BuildConfig.isExport && supportScreenShot() && supportWindowChange(
                context.packageName
            )
        }

        fun notifyWindowChange(packageName: String, small: Boolean) {
            try {
                OplusZoomWindowManagerProxy.notifyZoomStateChange(packageName, if (small) 1 else 2)
            } catch (e: Throwable) {
                AppLogger.BASIC.e(TAG, "notifyWindowChange not support notifyChange")
            }
        }

        fun supportWindowChange(packageName: String): Boolean {
            try {
                OplusZoomWindowManagerProxy.notifyZoomStateChange(packageName, 0)
                return true
            } catch (e: Throwable) {
                AppLogger.BASIC.e(TAG, "supportWindowChange not support windowInfo")
                return false
            }
        }

        fun isPhoneLandScape(activity: Activity): Boolean {
            return if (Build.VERSION.SDK_INT >= 30) {
                val rect =
                    (activity.getSystemService(Context.WINDOW_SERVICE) as WindowManager).maximumWindowMetrics.bounds
                val foldModeClose = UiHelper.isDeviceFold() && !UIConfigMonitor.isFoldingModeOpen()
                val isPhoneLandscape =
                    rect.width() > rect.height() && !OplusFeatureConfigManagerProxy.isDeviceFold &&
                        !OplusFeatureConfigManagerProxy.isDevicePad
                val foldLandscape = rect.width() > rect.height() && foldModeClose
                val dragFlyLandscape = UiHelper.isDeviceDragonfly() && rect.width() > rect.height()
                isPhoneLandscape || foldLandscape || dragFlyLandscape
            } else {
                false
            }
        }

        @RequiresApi(Build.VERSION_CODES.Q)
        @JvmStatic
        fun screenShot(
            activity: Activity,
            pocketStudioWrapper: PocketStudioWrapper?,
            isZoomWindowState: Boolean
        ): Pair<Bitmap?, Boolean> {
            /*
            由于在pocketStudio方案下,activity.isInMultiWindowMode也是为true的，所以needPocketStudioModeScreenShot的判断
            需要放在前面，不然会执行halfScreenShot，会出现问题
             */
            return when {
                (pocketStudioWrapper?.needPocketStudioModeScreenShot(isZoomWindowState) == true) -> {
                    AppLogger.BASIC.d(TAG, "screenShot: screenShotOnPocketStudio")
                    pocketStudioWrapper.screenShotOnPocketStudio(activity)
                }

                pocketStudioWrapper?.isPocketStudioMultiWindow() == true -> {
                    AppLogger.BASIC.d(TAG, "screenShot: halfScreenShot")
                    halfScreenShot(null)
                }

                else -> {
                    AppLogger.BASIC.d(TAG, "screenShot: fullScreenShot")
                    val rect = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        (activity.getSystemService(Context.WINDOW_SERVICE) as WindowManager).maximumWindowMetrics.bounds
                    } else {
                        val realScreen = getScreen(activity)
                        Rect().apply {
                            this.left = 0
                            this.top = 0
                            this.right = realScreen[0]
                            this.bottom = realScreen[1]
                        }
                    }
                    fullScreenShot(null, rect)
                }
            }
        }
    }
}