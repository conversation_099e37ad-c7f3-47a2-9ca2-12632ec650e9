/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/10/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  baixu       2023/10/17      1.0     create file
 ****************************************************************/
package com.nearme.note.thirdlog.service

import com.oplus.note.logger.AppLogger
import com.oplus.summary.ISummaryBinderPoolCallback

class SummaryBinderPoolCallBackApiImpl : ISummaryBinderPoolCallback.Stub() {
    companion object {
        const val TAG = "ISummaryBinderPoolCallback"
        const val ERROR_CONTENT_SAFETY = -6
    }

    /**
     * 回传录音状态
     * state 摘要状态
     * type 回传类型
     * callId 录音/通话ID
     */
    override fun callback() {
        AppLogger.BASIC.d(TAG, "callback")
    }
}
