/***********************************************************
 * Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File:
 * Description:
 * Version:
 * Date :
 * Author:
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
</desc></version></data></author> */
package com.nearme.note

import android.app.Activity
import android.app.Application
import android.app.NotificationManager
import android.content.Context
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.os.Bundle
import android.os.Environment
import android.os.UserManager
import android.text.TextUtils
import android.text.format.DateUtils
import androidx.annotation.WorkerThread
import com.google.android.appfunctions.AppFunctionAndroidApp
import com.heytap.usercenter.accountsdk.AccountAgentClient
import com.heytap.usercenter.accountsdk.AccountSDKConfig
import com.nearme.note.activity.richedit.NoteViewRichEditActivity
import com.nearme.note.common.feedbacklog.FeedbackLog
import com.nearme.note.db.NotesProvider
import com.nearme.note.encrypt.EncryptedBackgroundMonitor
import com.nearme.note.external.GlideThumbManager
import com.nearme.note.main.MainActivity
import com.nearme.note.setting.Setting
import com.nearme.note.skin.api.SkinManager.downSkinList
import com.nearme.note.thirdlog.ThirdLogNoteManager
import com.nearme.note.upgrade.MigrateOldPackageManager
import com.nearme.note.util.AlarmUtils
import com.nearme.note.util.CloudControlUtil
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.DataStatisticsHelper
import com.nearme.note.util.DeleteSoundUtils
import com.nearme.note.util.FileUtil
import com.nearme.note.util.PrivacyPolicyHelper
import com.nearme.note.util.ReflectUtil
import com.nearme.note.util.TBLSdkUtils
import com.nearme.note.util.ThemeBundleUtils
import com.oplus.cloud.agent.note.NoteSyncAgent
import com.oplus.cloud.status.Device
import com.oplus.cloud.sync.SyncManager
import com.oplus.cloud.sync.richnote.RichNoteOperator.Companion.getInstance
import com.oplus.cloud.sync.todo.TodoSyncOperator
import com.oplus.cloudkit.util.CloudkitDeleteSyncManager
import com.oplus.migrate.backuprestore.plugin.NoteBackupPlugin
import com.oplus.migrate.backuprestore.plugin.NoteRestorePlugin
import com.oplus.migrate.backuprestore.plugin.third.ThirdNoteRestorePlugin
import com.oplus.migrate.utils.FilePermissionUtil
import com.oplus.note.R
import com.oplus.note.aigc.manager.AIGCSupportManager
import com.oplus.note.aigc.util.AigcConfigUtil
import com.oplus.note.compat.os.CloudSyncCompact
import com.oplus.note.export.doc.DocUtils.clearDocFileInNeed
import com.oplus.note.logger.AppLogger
import com.oplus.note.logsalvage.api.LogSalvageAgentFactory
import com.oplus.note.market.MarketManager
import com.oplus.note.permission.PermissionsBlockedUtils
import com.oplus.note.push.PushAgentFactory
import com.oplus.note.search.NoteSearchManager
import com.oplus.note.utils.SharedPreferencesUtil
import com.oplus.notes.webview.container.di.webviewModule
import com.oplus.notes.webviewcoverpaint.container.di.webviewCoverpaintModule
import com.oplus.richtext.core.utils.RichUiHelper.initFontScale
import com.oplus.richtext.core.utils.RichUiHelper.initStatics
import com.oplus.statistics.OplusTrack
import com.oplus.stdid.sdk.StdIDSDK
import com.oplus.todo.search.TodoSearchManager
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.context.startKoin
import org.koin.core.logger.Level
import java.io.File
import java.io.IOException
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.concurrent.thread

@AppFunctionAndroidApp
class MyApplication : Application() {

    private var mFinalCount = 0
    private val mActivityNames: MutableSet<String> = HashSet()
    private val mActivities: MutableList<Activity> = ArrayList()
    private var mIsBackground = false
    val mHasUpgradeDb = AtomicBoolean(false)

    val isAlive: Boolean
        get() {
            AppLogger.BASIC.d(TAG, "isAlive " + mActivities.size)
            return mActivities.isNotEmpty() || ThirdLogNoteManager.getInstance().generatingIds.isNotEmpty() || hasRunningBRPlugin()
        }

    val filesDirAbsolutePath: String by lazy {
        filesDir.absolutePath
    }

    override fun attachBaseContext(base: Context) {
        runCatching {
            startKoin {
                androidLogger(Level.ERROR)
                androidContext(this@MyApplication)
                modules(
                    webviewModule,
                    webviewCoverpaintModule
                )
            }
        }
        super.attachBaseContext(base)
        feedbackContext = this
        myApplication = this
    }

    val isDbUpgradeFinished: Boolean
        get() = mHasUpgradeDb.get()

    override fun onCreate() {
        super.onCreate()
        val isUserUnlocked = isUserUnlocked(this)
        AppLogger.BASIC.d(TAG, "MyApplication onCreate isUserUnlocked=$isUserUnlocked")

        if (resources == null || !isUserUnlocked) {
            val pid = android.os.Process.myPid();
            AppLogger.BASIC.e(TAG, "Process $pid is going to be killed");
            FeedbackLog.E.userLog("Process $pid is going to be killed");
            android.os.Process.killProcess(pid);
        }

        if (TBLSdkUtils.instance.isRenderProcess()) {
            TBLSdkUtils.instance.init(this)
            AppLogger.BASIC.d(TAG, "onCreate: isRenderProcess, return")
            return
        }

        OplusTrack.init(this)
        initStatics(this)
        FileUtil.initDir(this)
        StdIDSDK.init(this)
        PermissionsBlockedUtils.setIsExport(ConfigUtils.isExport)
        AIGCSupportManager.initEnvironment(this, ConfigUtils.isExport, ConfigUtils.isLightOS)

        MigrateOldPackageManager.INSTANCE.migrateOldPackageData(this) { migrated: Boolean ->
            AppLogger.BASIC.d(TAG, "[Room] MyApplication onCreate after migrate")
            initSyncManager()
            AlarmUtils.init()
            AlarmUtils.AlarmReceiver.killSelfCountDown()
            mHasUpgradeDb.set(true)
//            PresetNoteUtils.insetPresetNote(this)
        }

        GlobalScope.launch(IO) {
            val isAgreeUserNotice = PrivacyPolicyHelper.isAgreeUserNotice(myApplication)
            initFontScale(myApplication)
            Setting.getInstance()
            val dataDir = myApplication.applicationInfo.dataDir
            checkBackupDb(File(dataDir + NotesProvider.DB_BACKUP_FOLDER))

            //get GUID
            if (StdIDSDK.isSupported()) {
                Device.setGuid(StdIDSDK.getGUID(myApplication))
            }
            Device.clearDeviceIMEICache(myApplication)
            if (isAgreeUserNotice) {
                MigrateOldPackageManager.INSTANCE.addOnMigrateFinishedListenerWithDialog(null, {
                    downSkinList()
                }, null, null)
            }
            deleteLegacyLogs()
            DeleteSoundUtils.loadSound()

            //一加内销手机在子线程中做一次权限修复
            val isFixPermission = SharedPreferencesUtil.getInstance().getBoolean(
                appContext, SharedPreferencesUtil.FILE_PERMISSION_NAME,
                SharedPreferencesUtil.FILE_PERMISSION_KEY, false
            )
            AppLogger.BASIC.e(TAG, "isFixPermission:$isFixPermission")
            if (!com.oplus.note.BuildConfig.isExport && com.oplus.note.BuildConfig.isOnePlus && !isFixPermission) {
                FilePermissionUtil.fixAppData(application)
            }
            if (isAgreeUserNotice) {
                LogSalvageAgentFactory.getAgent()?.init(this@MyApplication)
                CloudControlUtil.handleCloudControl()
                MarketManager.initMarketInfo(appContext)
            }

            clearDocFileInNeed(this@MyApplication)
            CloudkitDeleteSyncManager.checkAndChangeStatus(this@MyApplication)

            val isAgreeDmp = PrivacyPolicyHelper.isAgreeDmpSearch()
            NoteSearchManager.init(isAgreeDmp)
            TodoSearchManager.init(isAgreeDmp)
        }.start()
        TBLSdkUtils.instance.init(this@MyApplication)

        // 启用 WebView 调试
        if (com.oplus.note.BuildConfig.DEBUG) {
            android.webkit.WebView.setWebContentsDebuggingEnabled(true)
            AppLogger.BASIC.d(TAG, "WebView debugging enabled")
        }

        ReflectUtil.initNoticeChannel(this, CHANNEL_ID_NOTE, resources.getString(R.string.app_name), NotificationManager.IMPORTANCE_HIGH)
        registerActivityLifecycleListener()
        //init global theme flag
        ThemeBundleUtils.setImmersiveTheme(resources.getBoolean(com.oplus.note.base.R.bool.note_is_immsersive_theme))
        ThemeBundleUtils.setStatusWhite(resources.getBoolean(com.oplus.note.base.R.bool.note_is_status_white))

        // env 0 正式环境， 1 预发布环境， else 测试环境
        val env = 0
        thread {
            initAccount(env)
            initPush(env)
            initSync(env)
        }
        initAigcConfig()
    }

    private fun initAigcConfig() {
        val extraApp = com.oplus.note.BuildConfig.AIGC_SUPPORT_APP_EXTRA
        if (!TextUtils.isEmpty(extraApp)) {
            AigcConfigUtil.init(extraApp)
        }
    }

    private fun initAccount(env: Int) {
        val builder = AccountSDKConfig.Builder().context(this)
        val isOnePlusAndExport =
            com.oplus.note.BuildConfig.isOnePlus && com.oplus.note.BuildConfig.isExport
        when (env) {
            0, 1 -> {
                if (isOnePlusAndExport) {
                    builder.env(AccountSDKConfig.ENV.ENV_OP_RELEASE)
                } else {
                    builder.env(AccountSDKConfig.ENV.ENV_RELEASE)
                }
            }
//            3 -> {
//                builder.env(AccountSDKConfig.ENV.ENV_TEST_3)
//            }
            else -> {
                if (isOnePlusAndExport) {
                    builder.env(AccountSDKConfig.ENV.ENV_OP_TEST_1)
                } else {
                    builder.env(AccountSDKConfig.ENV.ENV_TEST_1)
                }
            }
        }
        AccountAgentClient.get().init(builder.create())
    }

    private fun initPush(env: Int) {
        when (env) {
            0, 1 -> {
                PushAgentFactory.get()?.init(this, 0)
            }

            else -> {
                PushAgentFactory.get()?.init(this, 1)
            }
        }
    }

    private fun initSync(env: Int) {
        com.oplus.cloudkit.SyncManager.init(this, env)
    }

    @WorkerThread
    private fun deleteLegacyLogs() {
        try {
            val packageName = packageName
            val path = File(
                Environment.getExternalStorageDirectory(),
                File.separator + Environment.DIRECTORY_DOWNLOADS + "/AppMonitorSDKLogs/" + packageName
            )
            path.deleteRecursively()
        } catch (ignored: Throwable) {
        }
    }

    private fun initSyncManager() {
        if (!CloudSyncCompact.getInstance().isSupportCloudSync(this)) {
            return
        }
        Thread {
            val syncManager = SyncManager.getInstance()
            syncManager.init(NoteSyncAgent.instance)
            syncManager.registerOperator(TodoSyncOperator.getInstance())
            syncManager.registerOperator(getInstance())
        }.start()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        //update global theme flag
        ThemeBundleUtils.setImmersiveTheme(resources.getBoolean(com.oplus.note.base.R.bool.note_is_immsersive_theme))
        ThemeBundleUtils.setStatusWhite(resources.getBoolean(com.oplus.note.base.R.bool.note_is_status_white))
        initStatics(this)
        initFontScale(this)
    }

    private fun registerActivityLifecycleListener() {
        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, bundle: Bundle?) {
                mActivities.add(activity)
            }

            override fun onActivityStarted(activity: Activity) {
                mActivityNames.add(activity.componentName.className)
                mFinalCount++
                if (mFinalCount == 1) {
                    mIsBackground = false
                    startEncryptedAlarm()
                }
            }

            override fun onActivityResumed(activity: Activity) {}
            override fun onActivityPaused(activity: Activity) {}
            override fun onActivityStopped(activity: Activity) {
                if (mActivityNames.contains(activity.componentName.className)) {
                    mFinalCount--
                    if (mFinalCount == 0) {
                        mIsBackground = true
                        startEncryptedAlarm()
                        GlideThumbManager.checkRelease("Application onStop")
                    }
                }
            }

            override fun onActivitySaveInstanceState(activity: Activity, bundle: Bundle) {}
            override fun onActivityDestroyed(activity: Activity) {
                mActivities.remove(activity)
                if (mActivities.isEmpty()) {
                    EncryptedBackgroundMonitor.cancelMonitor()
                }
            }
        })
    }

    fun finishAll() {
        for (activity in mActivities) {
            activity.finish()
        }
    }

    private fun checkBackupDb(backupDb: File) {
        if (!backupDb.exists()) {
            return
        }
        val modifiedTime = backupDb.lastModified()
        AppLogger.BASIC.d(TAG, backupDb.name + " modified:" + modifiedTime)
        if (System.currentTimeMillis() - modifiedTime > BACKUP_DB_EFFECTIVE_TIME) {
            try {
                backupDb.deleteRecursively()
            } catch (e: IOException) {
                // ignore
            }
        }
    }

    val activities: List<Activity>
        get() = mActivities

    private fun startEncryptedAlarm() {
        var mainActivity: MainActivity? = null
        var noteViewRichEditActivity: NoteViewRichEditActivity? = null
        for (activity in mActivities) {
            if (activity is MainActivity) {
                mainActivity = activity
            }
            if (activity is NoteViewRichEditActivity) {
                noteViewRichEditActivity = activity
            }
        }
        if (mainActivity != null && mainActivity.isEncryptedNote()) {
            resetEncryptedAlarm()
        } else if (noteViewRichEditActivity != null
            && noteViewRichEditActivity.isEncryptedNote()
        ) {
            resetEncryptedAlarm()
        }
    }

    private fun resetEncryptedAlarm() {
        if (mIsBackground) {
            EncryptedBackgroundMonitor.startMonitor()
        } else {
            EncryptedBackgroundMonitor.cancelMonitor()
        }
    }

    private fun isUserUnlocked(context: Context): Boolean {
        kotlin.runCatching {
            val userManager = context.getSystemService(Context.USER_SERVICE) as? UserManager
            return userManager == null || userManager.isUserUnlocked
        }
        return true
    }

    private fun hasRunningBRPlugin() = NoteBackupPlugin.isRunning || NoteRestorePlugin.isRunning || ThirdNoteRestorePlugin.isRunning

    companion object {
        const val TAG = "MyApplication"
        const val CHANNEL_ID_NOTE = "channel_id_nearme_note"
        private const val BACKUP_DB_EFFECTIVE_TIME = 4 * DateUtils.WEEK_IN_MILLIS /* four week */

        @JvmStatic
        lateinit var myApplication: MyApplication
            private set

        @JvmStatic
        val appContext: Context
            get() = myApplication

        @JvmStatic
        val application: Application
            get() = myApplication

        @JvmStatic
        var feedbackContext: Context? = null

        @JvmStatic
        fun getVersion(context: Context, needDate: Boolean, needCommit: Boolean): String {
            val pm = context.applicationContext.packageManager
            try {
                val packageInfo = pm.getPackageInfo(
                    context.packageName,
                    PackageManager.GET_META_DATA
                )
                if (packageInfo?.versionName != null) {
                    val metadata = packageInfo.applicationInfo?.metaData
                    val version = StringBuilder(packageInfo.versionName ?: "")
                    if (metadata != null && needDate) {
                        if (needCommit) {
                            val versionCommitObject = metadata["versionCommit"]
                            if (versionCommitObject != null) {
                                var commit = versionCommitObject.toString() + ""
                                if (!TextUtils.isEmpty(commit) && !commit.contains("_")) {
                                    commit = "_$commit"
                                }
                                version.append(commit)
                            }
                        }
                        var versionDate = ""
                        val versionDateObject = metadata["versionDate"]
                        if (versionDateObject != null) {
                            versionDate = versionDateObject.toString()
                            version.append("_").append(versionDate)
                        }
                    }
                    return version.toString()
                }
            } catch (e: PackageManager.NameNotFoundException) {
            }
            return ""
        }
    }
}