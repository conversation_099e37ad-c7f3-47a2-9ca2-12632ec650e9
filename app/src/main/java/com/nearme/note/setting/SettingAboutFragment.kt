/***********************************************************
 * * Copyright (C), 2008-2018, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SettingFragment.kt
 * * Description: the fragment of setting about
 * * Version: 1.0
 * * Date : 2021/3/5
 * * Author:
 * * ---------------------Revision History: ---------------------
 * *        <author>         <data>       <version>    <desc>
 * * <EMAIL>    2021/3/5       1.0         the fragment of setting about
</desc></version></data></author> */
package com.nearme.note.setting

import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.preference.Preference
import androidx.preference.PreferenceScreen
import com.coui.appcompat.preference.COUIJumpPreference
import com.coui.appcompat.toolbar.COUIToolbar
import com.nearme.note.setting.bigmodel.BigModelInfoActivity
import com.nearme.note.setting.opensourcelicense.OpenSourceActivity.Companion.start
import com.nearme.note.util.FlexibleWindowUtils
import com.oplus.note.BuildConfig
import com.oplus.note.R

class SettingAboutFragment : AutoIndentPreferenceFragment(), Preference.OnPreferenceClickListener {
    private var screen: PreferenceScreen? = null
    private var settingOpenSourceLicence: COUIJumpPreference? = null
    private var settingBigModelInfo: COUIJumpPreference? = null
    private var appCompatActivity: AppCompatActivity? = null
    private var toolbar: COUIToolbar? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        appCompatActivity = activity as? AppCompatActivity
        initActionBar()
        initView()
        initOpenSourceLicence()
        FlexibleWindowUtils.setBackground(appCompatActivity, view)
    }

    override fun onPreferenceClick(preference: Preference): Boolean {
        when (preference.key) {
            NOTE_SETTING_OPEN_SOURCE_LICENCE -> start(activity)
            NOTE_SETTING_BIG_MODEL_INFO -> BigModelInfoActivity.start(activity)
        }
        return true
    }

    private fun initActionBar() {
        toolbar = view?.findViewById(R.id.toolbar)
        appCompatActivity?.setSupportActionBar(toolbar)
        val actionBar = appCompatActivity?.supportActionBar
        actionBar?.setDisplayHomeAsUpEnabled(true)
        toolbar?.setNavigationOnClickListener { appCompatActivity?.finish() }
    }

    private fun initView() {
        screen = preferenceScreen
        settingOpenSourceLicence = findPreference(NOTE_SETTING_OPEN_SOURCE_LICENCE)
        settingBigModelInfo = findPreference(NOTE_SETTING_BIG_MODEL_INFO)
        appCompatActivity?.setTitle(R.string.about_notes)
        settingBigModelInfo?.isVisible = !BuildConfig.isExport
    }

    private fun initOpenSourceLicence() {
        settingOpenSourceLicence?.onPreferenceClickListener = this
        settingBigModelInfo?.onPreferenceClickListener = this
    }

    override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
        addPreferencesFromResource(R.xml.setting_about)
    }

    companion object {
        const val TAG = "NoteAboutSetting"
        const val NOTE_SETTING_OPEN_SOURCE_LICENCE = "pref_open_source_licence"
        const val NOTE_SETTING_BIG_MODEL_INFO = "pref_big_model_info"
    }
}