/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: ToDoViewModel.java
 * * Description: ToDoViewModel
 * * Version: 1.0
 * * Date: 2019/9/27
 * * Author: lvwuyou
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * lvwuyou 2019/9/27 1.0 build this module
 ****************************************************************/

package com.nearme.note.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.Transformations;

import com.nearme.note.common.feedbacklog.FeedbackLog;
import com.oplus.note.repo.todo.entity.ToDo;
import com.nearme.note.model.ToDoRepository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public class ToDoViewModel extends AndroidViewModel {
    private final ToDoRepository mToDoRepository;

    public ToDoViewModel(@NonNull Application application) {
        this(application, ToDoRepository.getInstance());
    }

    public ToDoViewModel(@NonNull Application application, ToDoRepository toDoRepository) {
        super(application);
        mToDoRepository = toDoRepository;
    }

    public void insert(ToDo toDo) {
        mToDoRepository.insert(toDo);
    }

    public void insert(ToDo toDo, ToDoRepository.ResultCallback<Long> callback) {
        if (toDo != null) {
            FeedbackLog.INSTANCE.getD().userLog("insert todo", toDo.getLocalId().toString());
        }
        mToDoRepository.insert(toDo, callback);
    }

    public void delete(ToDo toDo) {
        if (toDo != null) {
            FeedbackLog.INSTANCE.getD().userLog("delete todo", toDo.getLocalId().toString());
        }
        mToDoRepository.markDelete(toDo);
    }

    public void delete(ToDo toDo, ToDoRepository.ResultCallback<Integer> callback) {
        List<ToDo> toDos = new ArrayList<>(1);
        toDos.add(toDo);
        FeedbackLog.INSTANCE.getD().userLog("mark delete all todo");
        mToDoRepository.markDeleteAll(toDos, callback);
    }

    public void deleteAll(List<ToDo> toDos, ToDoRepository.ResultCallback<Integer> callback) {
        FeedbackLog.INSTANCE.getD().userLog("delete todo");
        mToDoRepository.markDeleteAll(toDos, callback);
    }

    public void update(ToDo toDo, ToDoRepository.ResultCallback<Integer> callback) {
        if (toDo != null) {
            FeedbackLog.INSTANCE.getD().userLog("update todo", toDo.getLocalId().toString());
        }
        mToDoRepository.update(toDo, callback);

    }

    //重复待办功能，  未完成/已完成 切换时 相关业务逻辑
    public void updateFinishTime(ToDo toDo, boolean checked, ToDoRepository.ResultCallback<Integer> callback) {
        if (toDo == null) {
            return;
        }
        FeedbackLog.INSTANCE.getD().userLog("updateFinishTime todo");
        mToDoRepository.updateFinishTime(toDo, checked, callback);
    }

    public LiveData<List<ToDo>> getAll() {
        return mToDoRepository.getAll();
    }

    public LiveData<List<ToDo>> getAllByLocalIds(List<UUID> localIds) {
        return mToDoRepository.getAllByLocalIds(localIds);
    }

    public LiveData<ToDo> getByLocalIdExcludeDeleted(UUID localId) {
        return mToDoRepository.getByLocalIdExcludeDeleted(localId);
    }

    public LiveData<List<ToDo>> getBeforeTomorrow(@NonNull LiveData<Date> dateLiveData) {
        return Transformations.switchMap(dateLiveData, input -> mToDoRepository.getBeforeTomorrow());
    }

    public LiveData<List<ToDo>> getDataExpired(@NonNull LiveData<Date> dateLiveData) {
        return Transformations.switchMap(dateLiveData, input -> mToDoRepository.getDataExpired());
    }

   public LiveData<List<ToDo>> getToday(@NonNull LiveData<Date> dateLiveData) {
        return Transformations.switchMap(dateLiveData, input -> mToDoRepository.getTodayWithAlarmTime());
    }

    public LiveData<List<ToDo>> getNoAlarmTimeDate(@NonNull LiveData<Date> dateLiveData) {
        return Transformations.switchMap(dateLiveData, input -> mToDoRepository.getNoAlarmTimeDate());
    }

    public LiveData<List<ToDo>> getTomorrow(@NonNull LiveData<Date> dateLiveData) {
        return Transformations.switchMap(dateLiveData, input -> mToDoRepository.getTomorrow());
    }

    public LiveData<List<ToDo>> getAfterTomorrow(@NonNull LiveData<Date> dateLiveData) {
        return Transformations.switchMap(dateLiveData, input -> mToDoRepository.getAfterTomorrow());
    }

    public LiveData<List<ToDo>> getFinished(@NonNull LiveData<Date> dateLiveData) {
        return Transformations.switchMap(dateLiveData, input -> mToDoRepository.getFinished());
    }

    public void sumToDoDistribution(ToDoRepository.ResultCallback<List<Integer>> callback) {
        mToDoRepository.sumToDoDistribution(callback);
    }

    public void sumToDoContentLengthDistribution(ToDoRepository.ResultCallback<List<Integer>> callback) {
        mToDoRepository.sumToDoContentLengthDistribution(callback);
    }

    public int getLocalDirtyToDosCount() {
        return mToDoRepository.getLocalDirtyToDosCount();
    }

    public int getDirtyToDosCount() {
        return mToDoRepository.getDirtyDataCount();
    }

    public ToDo getByLocalId(String localId) {
        return mToDoRepository.getByLocalId(localId);
    }
}
