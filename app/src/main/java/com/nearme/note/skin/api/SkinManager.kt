/****************************************************************
 * * Copyright (C), 2020-2028, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SkinManager
 * * Description: SkinManager
 * * Version: 1.0
 * * Date: 2020/5/25
 * * Author: 80242942
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * 80242942 2020/5/25 1.0 build this module
 ****************************************************************/
package com.nearme.note.skin.api

import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.os.SystemClock
import android.text.TextUtils
import android.util.ArrayMap
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import androidx.annotation.VisibleForTesting
import androidx.annotation.WorkerThread
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import com.nearme.note.MyApplication
import com.nearme.note.skin.SkinData
import com.nearme.note.skin.bean.Skin
import com.nearme.note.skin.bean.SkinSummary
import com.nearme.note.skin.core.SkinDownloader
import com.nearme.note.skin.core.SkinRepository
import com.nearme.note.skin.protocol.IHttpListener
import com.nearme.note.skin.protocol.IHttpTransferListener
import com.nearme.note.util.AccessibilityUtils
import com.nearme.note.util.AppExecutors
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.DarkModeUtil
import com.nearme.note.util.PrivacyPolicyHelper
import com.nearme.note.util.postValueSafe
import com.nearme.note.view.helper.UiHelper
import com.oplus.cloud.utils.PrefUtils
import com.oplus.note.common.PkgConstants
import com.oplus.richtext.editor.factory.ManualSkinManager
import com.oplus.richtext.editor.factory.ManualSkinType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import java.io.File

object SkinManager : SkinConfiguration, SkinResources() {

    private val embedSkinSummaries = ArrayList<SkinSummary>()
    private val embedSkins = ArrayMap<String, Skin>()

    init {
        EmbedSkinInitializer.initiateSkinSummaries(embedSkinSummaries)
        EmbedSkinInitializer.initiateSkins(embedSkins)
    }

    const val JSON_FILE = "skin.json"
    private const val SKIN = "skin"
    private const val ZIP_SUFFIX = ".zip"
    private const val SKIN_BASE_URL: String = "skin_base_url"
    private const val CHECK_SKIN_LIST_INTERVAL_TIME: Long = 60 * 1000L

    private var mLastDownloadListHash = ""

    private var mBaseUrl: String? = null

    private val mSkinPath: String = MyApplication.appContext.filesDir.canonicalPath + File.separator + SKIN + File.separator

    private val mSkinDownloader: SkinDownloader by lazy {
        SkinDownloader(MyApplication.appContext)
    }

    private val mSkinRepository: SkinRepository by lazy {
        SkinRepository()
    }

    fun getSkinDownList() = mSkinDownloader.getmDownloadingItems()

    private var mRemoteSkinListHasSkin = false
    private var mCheckRemoteSkinListTime = 0L

    @VisibleForTesting
    val mSkinViewManagerMap = ArrayMap<Int, ManualSkinManager>()
    private val mutableLiveData = MutableLiveData<List<SkinSummary>>()

    fun getSkinList(): LiveData<List<SkinSummary>> {
        return mSkinRepository.getSkinSummaries(getSkinSize()).switchMap { dataFromDB ->
            val data = ArrayList<SkinSummary>().apply {
                addAll(embedSkinSummaries)
                if (ConfigUtils.isSupportSkinSettings && !UiHelper.isDevicePad()) {
                    addAll(dataFromDB)
                }
            }
            mutableLiveData.value = data
            mutableLiveData
        }
    }

    fun getSkinListData(): MutableLiveData<List<SkinSummary>> {
        return mutableLiveData
    }

    fun updateSkinList(data: List<SkinSummary>) {
        mutableLiveData.postValueSafe(data)
    }

    @WorkerThread
    fun getSkin(id: String): Skin? {
        return mSkinRepository.getSkin(id, getSkinSize())
    }

    fun getSkinBasePath(): String {
        return mSkinPath
    }

    fun getSkinPath(skinId: String): String {
        return mSkinPath + skinId + File.separator + getSkinSize() + File.separator
    }

    fun getSkinRelativePath(skinId: String): String {
        return File.separator + SKIN + File.separator + skinId + File.separator + getSkinSize() + File.separator
    }

    private fun hasRemoteSkinList(): Boolean {
        return mSkinRepository.hasSkin()
    }

    @JvmStatic
    fun asyncCheckRemoteSkin() {
        if (mRemoteSkinListHasSkin) {
            return
        }
        if ((SystemClock.uptimeMillis() - mCheckRemoteSkinListTime) <= CHECK_SKIN_LIST_INTERVAL_TIME) {
            return
        }
        mCheckRemoteSkinListTime = SystemClock.uptimeMillis()
        Thread {
            if (!hasRemoteSkinList() && PrivacyPolicyHelper.isAgreeUserNotice(MyApplication.myApplication)) {
                downSkinList()
            } else {
                mRemoteSkinListHasSkin = true
            }
        }.start()
    }

    @JvmStatic
    fun asyncDownRemoteSkin() {
        if (mRemoteSkinListHasSkin) {
            return
        }
        Thread {
            if (!hasRemoteSkinList() && PrivacyPolicyHelper.isAgreeUserNotice(MyApplication.myApplication)) {
                downSkinList()
            } else {
                mRemoteSkinListHasSkin = true
            }
        }.start()
    }

    fun downSkinList() {
        if (AccessibilityUtils.isTalkBackAccessibility(MyApplication.appContext) || !ConfigUtils.isSupportSkinSettings || UiHelper.isDevicePad()) {
            return
        }
        getSupportSkinSize().forEach {
            downSkinList(it)
        }
    }

    @JvmOverloads
    fun downSkinList(condition: String, listener: IHttpListener<List<SkinSummary>>? = null) {
        val url = PkgConstants.SKIN_DOWN_URL // product environment
//        val url = "https://note-skin-dev.wanyol.com/enum/v1/child" // test environment
        mSkinDownloader.downSkinList(url, condition, listener)
    }

    private fun buildSkinZipFilePath(skinSummary: SkinSummary) = mSkinPath + skinSummary.id + File.separator + skinSummary.condition + File.separator + skinSummary.id + ZIP_SUFFIX

    fun downSkin(skinSummary: SkinSummary, listener: IHttpTransferListener<Skin>?, manualDownload: Boolean) {
        mSkinDownloader.downSkin(skinSummary, buildSkinZipFilePath(skinSummary), listener, manualDownload)

        // trigger other condition summaries to download
        AppExecutors.getInstance().executeCommandInDiskIO {
            mSkinRepository.getOtherSkinSummariesSync(skinSummary.id, skinSummary.condition).forEach {
                mSkinDownloader.downSkin(it, buildSkinZipFilePath(it), null, false)
            }
        }
    }

    @WorkerThread
    @JvmOverloads
    fun downSkin(skinId: String?, listener: IHttpTransferListener<Skin>? = null) {
        skinId?.let {
            mSkinRepository.getSkinSummary(skinId, getSkinSize())?.apply {
                if (!isEmbedSkin(skinId) && !isDownloaded()) {
                    downSkin(this, listener, false)
                }
            }
        }
    }

    fun reattachDownloadingListener(skinSummary: SkinSummary, listener: IHttpTransferListener<Skin>?) {
        mSkinDownloader.reattachDownloading(skinSummary, listener)
    }

    fun getSkinDownloadingList() = mSkinDownloader.getSkinDownloadingList()

    fun hasNewManualDownloadSkin(): Boolean {
        if (mSkinDownloader.getManualDownloadSet().size == 0) return false
        val newHash = mSkinDownloader.getManualDownloadSet().toSet().joinToString()
        val hasNew = mLastDownloadListHash != newHash
        mLastDownloadListHash = newHash
        return hasNew
    }

    internal fun saveSkinBaseUrl(url: String) {
        mBaseUrl = url
        PrefUtils.putString(MyApplication.appContext, SKIN_BASE_URL, url)
    }

    internal fun getSkinBaseUrl(): String {
        return mBaseUrl ?: PrefUtils.getString(MyApplication.appContext, SKIN_BASE_URL, "")
    }

    internal fun addSkinList(list: List<SkinSummary>, condition: String) {
        mSkinRepository.addSkinList(list, condition)
    }

    internal fun saveSkin(id: String, condition: String, content: String) {
        mSkinRepository.saveSkin(id, condition, content)
    }

    @JvmStatic
    fun isEmbedSkin(skinId: String?) = SkinData.getImageList().contains(skinId) || SkinData.getColorList().contains(skinId)
            || skinId?.let { SkinData.isManualSkin(it) } == true || TextUtils.isEmpty(skinId)

    @JvmStatic
    fun isSkinValid(skinId: String?): Boolean =
        (!SkinData.COLOR_SKIN_WHITE.equals(skinId)) && (SkinData.getImageList()
            .contains(skinId) || SkinData.getColorList().contains(skinId))

    private fun getEmbedSkinBySkinId(skinId: String?): Skin? {
        val isWhiteSkin = TextUtils.isEmpty(skinId) || (SkinData.COLOR_SKIN_WHITE == skinId)
        if (isWhiteSkin && DarkModeUtil.isDarkMode(MyApplication.appContext)) {
            return embedSkins[SkinData.COLOR_SKIN_BLACK]
        }
        return embedSkins[skinId]
    }

    override fun getEditPageConfiguration(activity: Activity?, skinId: String, block: (config: Skin.EditPage) -> Unit) {
        if (activity == null) {
            return
        }
        AppExecutors.getInstance().executeCommandInDiskIO {
            fun getConfig(): Skin.EditPage {
                getEmbedSkinBySkinId(skinId)?.editPage?.apply {
                    return this
                }
                getSkin(skinId)?.editPage?.apply {
                    return this
                }

                return getEmbedSkinBySkinId(SkinData.COLOR_SKIN_WHITE)!!.editPage
            }

            getConfig().apply {
                activity.runOnUiThread { block.invoke(this) }
            }
        }
    }

    @WorkerThread
    override fun getCardConfiguration(skinId: String): Skin.Card? {
        getSkin(skinId)?.card?.apply {
            return this
        }

        return getEmbedSkinBySkinId(skinId)?.card
    }

    @WorkerThread
    fun getEmbedCardConfiguration(skinId: String?): Skin.Card? {
        getEmbedSkinBySkinId(skinId)?.card?.apply {
            return this
        }

        return null
    }

    @WorkerThread
    fun getSharePageContentBackground(skinId: String): Skin.SharePage.Background.ContentBg? {
        getEmbedSkinBySkinId(skinId)?.sharePage?.background?.contentBg?.apply {
            return this
        }
        getSkin(skinId)?.sharePage?.background?.contentBg?.apply {
            return this
        }
        return null
    }

    @WorkerThread
    fun getSharePageTopExtraBg(skinId: String): Skin.SharePage.Background.TopExtraBg? {
        getEmbedSkinBySkinId(skinId)?.sharePage?.background?.topExtraBg?.apply {
            return this
        }
        getSkin(skinId)?.sharePage?.background?.topExtraBg?.apply {
            return this
        }
        return null
    }

    @JvmStatic
    fun updateSkinViewState(
        type: Int,
        backCloth: View?,
        bottomCloth: View?,
        backGround: View?,
        backgroundColor: Int = Color.TRANSPARENT,
        backClothColor: Int = Color.TRANSPARENT
    ) {
        when (type) {
            SkinData.TYPE_IS_MANUAL_SKIN -> {
                backCloth?.visibility = View.VISIBLE
                bottomCloth?.visibility = View.GONE
                backGround?.visibility = View.VISIBLE
                backCloth?.setBackgroundColor(backClothColor)
                bottomCloth?.setBackgroundColor(Color.TRANSPARENT)
                backGround?.setBackgroundColor(Color.TRANSPARENT)
            }
            SkinData.TYPE_IS_COLOR_BACK -> {
                backGround?.visibility = View.VISIBLE
                backCloth?.visibility = View.VISIBLE
                bottomCloth?.visibility = View.VISIBLE
                backGround?.setBackgroundColor(backgroundColor)
                backCloth?.setBackgroundColor(backClothColor)
                bottomCloth?.setBackgroundColor(backClothColor)
            }
            SkinData.TYPE_OTHER -> {
                bottomCloth?.visibility = View.GONE
                backGround?.visibility = View.GONE
                backCloth?.visibility = View.GONE
                backGround?.setBackgroundColor(Color.TRANSPARENT)
                backCloth?.setBackgroundColor(Color.TRANSPARENT)
                bottomCloth?.setBackgroundColor(Color.TRANSPARENT)
            }
        }
    }

    @JvmStatic
    fun changeManualSkinType(
        owner: LifecycleOwner?,
        inflater: LayoutInflater,
        type: ManualSkinType,
        view: LinearLayout?,
        offsetY: Float,
        forceUpdateHeight: Boolean,
        isShareImg: Boolean = false,
        titleDiff: Int = 0,
        isNeedAddNeeSkin: Boolean = false
    ) {
        if (owner == null || view == null) {
            return
        }
        val factory = getCurrentManualSkinManager(owner)
        factory.changeSkinType(
            owner,
            inflater,
            type,
            view,
            offsetY,
            forceUpdateHeight,
            isShareImg,
            titleDiff,
            isNeedAddNeeSkin = isNeedAddNeeSkin
        )
    }

    @JvmStatic
    fun updateManualSkinPath(owner: LifecycleOwner?, offsetY: Float) {
        owner?.let {
            val factory = getCurrentManualSkinManager(it)
            factory.updatePath(owner, offsetY)
        }
    }

    fun destroyManualSkinView(owner: LifecycleOwner?) {
        owner?.let {
            val factory = getCurrentManualSkinManager(it)
            factory.clear(it)
            mSkinViewManagerMap.remove(it.hashCode())
        }
    }

    fun setManualSkinViewGone(owner: LifecycleOwner?) {
        owner?.let {
            val factory = getCurrentManualSkinManager(it)
            factory.setSkinViewGone(it)
        }
    }

    @VisibleForTesting
    fun getCurrentManualSkinManager(owner: LifecycleOwner): ManualSkinManager {
        val hashCode = owner.hashCode()
        val factory: ManualSkinManager
        if (mSkinViewManagerMap.contains(hashCode)) {
            factory = mSkinViewManagerMap[hashCode]!!
        } else {
            factory = ManualSkinManager()
            mSkinViewManagerMap[hashCode] = factory
        }
        return factory
    }

    fun resetSkin(skinId: String) {
        val skinListData = getSkinListData()
        skinListData.value?.run {
            forEach { data ->
                if (skinId == data.id) {
                    data.detail = ""
                }
            }
            updateSkinList(this)
        }
    }

    suspend fun getEditPage(scope: CoroutineScope, skinId: String): Skin.EditPage? {
        var editPage = getEmbedSkinBySkinId(skinId)?.editPage
        if (editPage != null) {
            return editPage
        }
        val result = scope.async(Dispatchers.IO) {
            getSkin(skinId)?.editPage
        }
        editPage = result.await()
        if (editPage != null) {
            return editPage
        }
        return getEmbedSkinBySkinId(SkinData.COLOR_SKIN_WHITE)?.editPage
    }
}