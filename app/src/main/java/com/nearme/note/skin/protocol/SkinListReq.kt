/****************************************************************
 * * Copyright (C), 2020-2028, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SkinListReq
 * * Description: SkinListReq
 * * Version: 1.0
 * * Date: 2020/5/25
 * * Author: 80242942
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * 80242942 2020/5/25 1.0 build this module
 ****************************************************************/

package com.nearme.note.skin.protocol

data class SkinListReq(val aids: List<Aid> = listOf(Aid()),
                       var condition: Condition = Condition(),
                       var devId: String = "",
                       var mode: Int = 1) {
    data class Aid(val aid: String = "note-skin")
    data class Condition(val resolution: String = "1080P")
}