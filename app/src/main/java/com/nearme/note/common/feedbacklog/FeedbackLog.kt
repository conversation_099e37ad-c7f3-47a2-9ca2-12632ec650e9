package com.nearme.note.common.feedbacklog

import com.oplus.note.logger.AppLogger
import com.nearme.note.MyApplication
import com.oplus.note.questionnaire.api.feedback.FeedbackUserFactory

/**
 * Created by 80326921 on 2021/4/26.
 * Email
 */
object FeedbackLog {
    const val DB_OPERATION: String = "DB:"
    const val USER_OPERATION: String = "USER:"
    const val DB_TABLE_NAME: String = " | table name:"
    const val DB_DATA_ID: String = " | local id:"
    const val DB_LENGTH: String = " | length:"
    const val COMMA: String = ","

    enum class Operation {
        Insert,
        Update,
        Delete
    }

    abstract class AbLog {

        /**
         * @param dataCategory the category of data.
         * @param dataId the unique id of data.
         */
        fun dbLog(operation: Operation, dataCategory: String, dataId: String, txt: String? = null) {
            val length = txt?.length ?: 0
            val log =
                DB_OPERATION + operation + DB_TABLE_NAME + dataCategory + DB_DATA_ID + dataId + DB_LENGTH + length
            printLog(log)
        }

        fun anyDbLog(msg: String) {
            val log = DB_OPERATION + msg
            printLog(log)
        }

        fun userLog(msg: String?) {
            msg?.let {
                printLog(USER_OPERATION + it)
            }
        }

        fun userLog(msg: String?, localId: String?) {
            if (msg != null && localId != null) {
                val log = USER_OPERATION + msg + DB_DATA_ID + localId
                printLog(log)
            }
        }

        fun userLog(msg: String?, localIdList: Set<String>?) {
            if (msg != null && localIdList != null) {
                val ids = StringBuffer()

                for (id in localIdList) {
                    ids.append(id)
                    ids.append(COMMA)
                }

                val log = USER_OPERATION + msg + DB_DATA_ID + ids
                printLog(log)
            }
        }

        protected abstract fun printLog(log: String)

    }

    class VLog : AbLog() {
        override fun printLog(log: String) {
            logV(log)
        }
    }

    class DLog : AbLog() {
        override fun printLog(log: String) {
            logD(log)
        }
    }

    class WLog : AbLog() {
        override fun printLog(log: String) {
            logW(log)
        }
    }

    class ILog : AbLog() {
        override fun printLog(log: String) {
            logI(log)
        }
    }

    class ELog : AbLog() {
        override fun printLog(log: String) {
            logE(log)
        }
    }

    @JvmStatic
    val V: VLog = VLog()
    @JvmStatic
    val D: DLog = DLog()
    @JvmStatic
    val W: WLog = WLog()
    @JvmStatic
    val I: ILog = ILog()
    @JvmStatic
    val E: ELog = ELog()

    private fun logV(msg: String) {
        FeedbackUserFactory.getHelper()?.fbLogV(MyApplication.feedbackContext, msg)
        AppLogger.FEEDBACK.v("Feedback", msg)
    }

    private fun logD(msg: String) {
        FeedbackUserFactory.getHelper()?.fbLogD(MyApplication.feedbackContext, msg)
        AppLogger.FEEDBACK.d("Feedback", msg)
    }

    private fun logW(msg: String) {
        FeedbackUserFactory.getHelper()?.fbLogW(MyApplication.feedbackContext, msg)
        AppLogger.FEEDBACK.w("Feedback", msg)
    }

    private fun logI(msg: String) {
        FeedbackUserFactory.getHelper()?.fbLogI(MyApplication.feedbackContext, msg)
        AppLogger.FEEDBACK.i("Feedback", msg)
    }

    private fun logE(msg: String) {
        FeedbackUserFactory.getHelper()?.fbLogE(MyApplication.feedbackContext, msg)
        AppLogger.FEEDBACK.e("Feedback", msg)
    }

}