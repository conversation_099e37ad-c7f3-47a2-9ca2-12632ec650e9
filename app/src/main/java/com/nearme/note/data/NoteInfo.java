/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.data;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.oplus.note.logger.AppLogger;
import com.oplus.note.BuildConfig;
import com.oplus.note.R;
import com.oplus.note.repo.note.entity.FolderInfo;
import com.oplus.richtext.editor.utils.Linkify;
import com.nearme.note.MyApplication;
import com.nearme.note.data.NoteAttribute.PictureAttribute;
import com.nearme.note.data.NoteAttribute.TextAttribute;
import com.nearme.note.db.extra.NoteExtra;
import com.nearme.note.editor.parser.ContentConverter;
import com.nearme.note.skin.SkinData;
import com.nearme.note.skin.api.SkinManager;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map.Entry;
import java.util.Set;

public class NoteInfo implements Parcelable {

    private static final String TAG = "NoteInfo";

    public static final String DIVISION = "__END_OF_PART__";
    public static final String NULL_STRING = "";

    public static final int TEXT_NOTE_THUMB_LEN = 300; // 文本笔记缩略图文字个数

    public static final int STATE_NEW = 0;
    public static final int STATE_MODIFIED = 1;
    public static final int STATE_UNCHANGE = 3;
    public static final int SHOW_TITLE_LEN = 30;


    public static final String STATE_UNMARK_DELETED = "0";
    public static final String STATE_MARK_DELETED = "1";

    private static final int NOTE_OWNER_SHARE = 0;// 用户没登录，公用
    private static final int NOTE_OWNER_USER = 1; // 用户登录
    private static final int MAX_LINES = 6;
    private final HashMap<String, NoteAttribute> mAttributes = new HashMap<String, NoteAttribute>();
    private int mVersion = 1;
    private int mCreateConsole = 0;// 默认0 手机
    private int mThumbType; // 表明是什么类型的
    private int mState = STATE_NEW;
    private int mOwner = NOTE_OWNER_SHARE;
    private int mBackgroundRes = 0;
    private String mDelete = STATE_UNMARK_DELETED;
    private long mUpdated;
    private long mCreated;
    private long mTopped = -1;
    private String mGuid;
    private String mContent; // 对应类型的值：缩略图名称，不带G.THUMB; 或者文本内容头部
    private String mGlobalId;
    private String mAttachmentId;
    private TextAttribute mWholeContentAttr;
    private ArrayList<NoteAttribute> mPagedElements = new ArrayList<>();

    private String mFolderGuid;
    private String mFolderName;
    private long mRecycled;
    private long mAlarmTime;
    private String mNoteSkin;
    private long mRecycledPre;
    private long mAlarmTimePre;
    private String mNoteSkinPre;
    private long mTimestamp;
    private NoteExtra mExtra;
    //sysVersion用于记录 cloudkit 同步数据的版本号，由云服务生成，本地只做记录
    private long mSysVersion;

    public NoteInfo() {
        super();
        mFolderGuid = FolderInfo.FOLDER_GUID_NO_GUID;
        mFolderName = MyApplication.getAppContext().getResources().getString(R.string.memo_all_notes);
        mExtra = NoteExtra.Companion.create(null);
    }

    public void setWholeContentAttribute(@NonNull TextAttribute originContentAttr) {
        this.mWholeContentAttr = originContentAttr;
        updatePagedElements();
    }

    public TextAttribute getWholeContentAttribute() {
        return mWholeContentAttr;
    }

    public synchronized void updatePagedElements() {
        if ((null == mPagedElements) || (null == mWholeContentAttr)) {
            return;
        }
        mPagedElements.clear();
        TextAttribute titleTextAttribute = TextAttribute.newTitleAttribute();
        if ((mExtra != null) && (mExtra.getTitle() != null)) {
            titleTextAttribute.setText(mExtra.getTitle());
        } else {
            titleTextAttribute.setText("");
        }
        titleTextAttribute.setText(getExtra().getTitle());
        mPagedElements.add(titleTextAttribute);
        final TextAttribute wholeContentAttr = mWholeContentAttr;

        final String text = wholeContentAttr.getText();
        if (null != text) {
            String[] contentText = null;
            if (!text.contains(DIVISION)) {
                addAttribute(wholeContentAttr);
                mPagedElements.add(wholeContentAttr);
            } else {
                contentText = text.split(DIVISION);
                final int size = contentText.length;
                for (int i = 0; i < size; i++) {
                    if (i % 2 == 0) {
                        final TextAttribute textAttribute = NoteAttribute.newTextAttribute();
                        textAttribute.setText(contentText[i]);
                        mPagedElements.add(textAttribute);
                    } else {
                        final NoteAttribute noteAttribute = mAttributes.get(contentText[i]);
                        if (noteAttribute != null) {
                            mPagedElements.add(noteAttribute);
                        } else {
                            final PictureAttribute picture = NoteAttribute.newPictureAttribute();
                            picture.setNoteGuid(mGuid);
                            picture.setAttrGuid(contentText[i]);
                            mPagedElements.add(picture);
                        }
                    }
                }
            }
        }
    }

    public String getWholeContent() {
        return (mWholeContentAttr == null) ? null : mWholeContentAttr.getText();
    }

    public NoteAttribute addAttribute(int type, String object) {
        return addAttribute(type, object, null);
    }

    public NoteAttribute addAttribute(int type, String object, String param) {
        final NoteAttribute attr = NoteAttribute.newNoteAttribute(type, object,
                NoteAttribute.OP_ADD);
        attr.setCreated(System.currentTimeMillis());
        attr.setParam(param);

        return addAttribute(attr);
    }

    public NoteAttribute addAttribute(NoteAttribute attr) {
        attr.setNoteGuid(mGuid);
        final String object = attr.getContent();
        if (object == null) {
            return attr;
        }
        mAttributes.put(object, attr);
        for (int i = 0, size = mPagedElements.size(); i < size; i++) {
            final NoteAttribute item = mPagedElements.get(i);
            if ((attr.getType() == item.getType()) && object.equals(item.getContent())) {
                mPagedElements.set(i, attr);
                break;
            }
        }
        return attr;
    }

    public void clearAttributes() {
        mAttributes.clear();
    }

    public PictureAttribute getFirstPictureAttribute() {
        for (NoteAttribute attr : mPagedElements) {
            if (attr instanceof PictureAttribute) {
                return (PictureAttribute) attr;
            }
        }
        return null;
    }

    public int getAttributesSize() {
        if (mAttributes != null) {
            return mAttributes.size();
        }
        return 0;
    }

    public int getElementCount() {
        if (mPagedElements != null) {
            return mPagedElements.size();
        }

        return 0;
    }

    public NoteAttribute getElement(int index) {
        if ((mPagedElements == null) || (mPagedElements.size() <= 0)
                || (index < 0) || (index > mPagedElements.size())) {
            return null;
        }

        return mPagedElements.get(index);
    }

    public String getWholeTextContent() {
        return getWholeTextContent(false);
    }

    private String getWholeTextContent(boolean exceptTitle) {
        StringBuilder textContent = new StringBuilder();
        int index = 0;
        String content = null;
        String tmpContent = null;

        for (NoteAttribute attr : mPagedElements) {
            if (attr instanceof TextAttribute) {
                if (exceptTitle && (attr.getType() == NoteAttribute.TYPE_TITLE)) {
                    continue;
                }

                content = ((TextAttribute) attr).getText();
                tmpContent = ContentConverter.filterCheckboxContent(content);

                if (!TextUtils.isEmpty(tmpContent)) {
                    tmpContent = tmpContent.replace("\u200c", "").trim();
                }

                if (!TextUtils.isEmpty(tmpContent)) {
                    if (index++ > 0) {
                        textContent.append("\n");
                    }

                    textContent.append(tmpContent.trim());
                }
            }
        }

        return textContent.toString();
    }

    public List<NoteAttribute> getAttributesIncWholeContent() {
        final Collection<NoteAttribute> values = mAttributes.values();
        final ArrayList<NoteAttribute> attributes = new ArrayList<>(values);
        if ((mWholeContentAttr != null) && !attributes.contains(mWholeContentAttr)) {
            attributes.add(0, mWholeContentAttr);
        }
        return attributes;
    }

    public Collection<NoteAttribute> getAttributes() {
        return mAttributes.values();
    }

    /**
     * it may uses in main thread to refresh view
     * or use in sync thread to save data
     */
    public synchronized int getPictureAttributeSize() {
        int picCount = 0;
        final Set<Entry<String, NoteAttribute>> entrySet = mAttributes.entrySet();
        for (final Entry<String, NoteAttribute> entry : entrySet) {
            final NoteAttribute attr = entry.getValue();
            if (!attr.isDeleted() && (attr instanceof PictureAttribute)) {
                picCount++;
            }
        }
        return picCount;
    }

    public void removeAllAttribute() {
        mAttributes.clear();
    }

    public void setStateBeforeSave() {

        // 只有在修改状态或者已同步状态
        if ((mState == STATE_UNCHANGE) || (mState == STATE_MODIFIED)) {
            // 先将状态设置好
            for (final NoteAttribute attr : mAttributes.values()) {
                if (attr != null) {
                    attr.setStateBeforeSave();
                }
            }
            for (final NoteAttribute attr : mAttributes.values()) {
                if (attr != null) {
                    if (attr.getState() != STATE_UNCHANGE) {
                        // 表示已经同步过，检查是否需要置为修改状态；
                        // 否则就是本地新建或者修改状态，都需要上传
                        if ((mState == STATE_UNCHANGE)) {
                            mState = STATE_MODIFIED;
                            mVersion++;
                            AppLogger.NOTE.d(TAG, "[NoteInfo]setStateBeforeSave version++++++:" + mVersion);
                        }
                        break;
                    }
                }
            }
        }
    }

    /*
     * 设置缩略图相关信息，在所笔记界面需要
     */
    public void setThumbInfoForAllNoteList() {
        final PictureAttribute attr = getFirstPictureAttribute();
        if (attr != null) { //has picture
            mContent = attr.getAttrGuid();
            mThumbType = (byte) attr.getType();
        } else { //no picture
            mThumbType = NoteAttribute.TYPE_TEXT_CONTENT;
            final String content = getWholeTextContent(true);

            if (!TextUtils.isEmpty(content)) {
                if (content.length() > TEXT_NOTE_THUMB_LEN) {
                    mContent = content.substring(0, TEXT_NOTE_THUMB_LEN) + "...";
                } else {
                    mContent = content;
                }
            } else {
                mContent = "";
                AppLogger.NOTE.d(TAG,"[NoteInfo] setThumbInfoForAllnoteList error!!!! no attr");
            }
        }
    }

    public void setOwnerBeforeSave() {
        if (mOwner == NOTE_OWNER_USER) {
            for (final NoteAttribute attr : mAttributes.values()) {
                if (attr != null) {
                    attr.setOwner(NOTE_OWNER_USER);
                }
            }
        }
    }

    public TextAttribute getFirstTextAttribute() {
        for (NoteAttribute attr : mPagedElements) {
            if ((attr instanceof TextAttribute)
                    && !NULL_STRING.equals(((TextAttribute) attr).getText())) {
                return (TextAttribute) attr;
            }
        }
        return null;
    }

    public synchronized String getDescription() {
        boolean hasPictureAttribute = false;
        TextAttribute firstTextAttr = null;
        TextAttribute secondTextAttr = null;

        for (NoteAttribute attr : mPagedElements) {
            if (attr instanceof TextAttribute) {
                final String text = ((TextAttribute) attr).getText();
                if ((text != null) && !NULL_STRING.equals(text.trim())) {
                    if ((firstTextAttr == null) && (attr.getType() != NoteAttribute.TYPE_TITLE)) {
                        firstTextAttr = (TextAttribute) attr;
                    } else if (firstTextAttr != null) {
                        if (secondTextAttr == null) {
                            secondTextAttr = (TextAttribute) attr;
                        }

                        if (hasPictureAttribute) {
                            break;
                        }
                    }
                }
            } else if ((attr instanceof PictureAttribute) && !attr.isDeleted()) {
                hasPictureAttribute = true;
                if ((firstTextAttr != null) && (secondTextAttr != null)) {
                    break;
                }
            }
        }

        /*
         * If has picture and text, return first text content + second text content.
         *
         * If pure text, return second paragraph content, if no second paragraph, return null.
         */
        if (hasPictureAttribute) {
            String fisrt = null;
            String second = null;
            StringBuilder description = new StringBuilder();

            if (firstTextAttr != null) { //get from fisrt text attr
                fisrt = getFisrtParagraphText(firstTextAttr.getText());

                if ((fisrt != null) && (fisrt.length() < firstTextAttr.getText().length())) {
                    second = getSecondAndMoreParagraphText(firstTextAttr.getText());
                }
            }

            if (TextUtils.isEmpty(second) && (secondTextAttr != null)) { //get from second text attr
                second = secondTextAttr.getText();
            }

            if (!TextUtils.isEmpty(fisrt)) {
                description.append(subDescription(fisrt));
            }

            if (!TextUtils.isEmpty(second)) {
                description.append("\n").append(subDescription(second));
            }

            if (description.length() > 0) {
                return description.toString();
            }
        } else if (firstTextAttr != null) { //pure text, no picture
            String second = getSecondAndMoreParagraphText(firstTextAttr.getText());

            if (!TextUtils.isEmpty(second) && (second.length() > TEXT_NOTE_THUMB_LEN)) {
                second = second.substring(0, TEXT_NOTE_THUMB_LEN);
            }

            return second;
        }

        if (BuildConfig.isOnePlus) {
            try {
                if (mAttributes.values().size() > 0) {
                    for (NoteAttribute attribute : mAttributes.values()) {
                        if (attribute.getType() == NoteAttribute.TYPE_TEXT_CONTENT) {
                            String content = attribute.getContent();
                            String[] smallContent = content.split(NoteInfo.DIVISION);
                            for (String item : smallContent) {
                                if (!isPicChars(item)) {
                                    return item;
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                AppLogger.BASIC.d("NoteInfo", e.getMessage());
            }
        }
        return null;
    }

    private boolean isPicChars(String name) {
        for (int i = 0; i < name.length(); i++) {
            char c = name.charAt(i);
            if (!((((c >= 'a') && (c <= 'z')) || ((c >= '0') && (c <= '9')) || ((c >= 'A') && (c <= 'Z'))) || (c == '-') || (c == '_'))) {
                return false;
            }
        }
        return true;
    }

    private String subDescription(String description) {
        if (!TextUtils.isEmpty(description)) {
            description = description.trim();
            int index = description.indexOf("\n");
            if (index != -1) {
                index = Math.min(index, TEXT_NOTE_THUMB_LEN);
            } else {
                index = TEXT_NOTE_THUMB_LEN;
            }
            if (description.length() > index) {
                description = description.substring(0, index);
            }
        }
        return description;
    }

    private String getFisrtParagraphText(String text) {
        final String[] contentText = text.split("\n");

        for (String s : contentText) {
            String string = ContentConverter.filterCheckboxContent(s);

            if (!TextUtils.isEmpty(string)) {
                string = string.replace("\u200c", "").trim();
            }

            if (!TextUtils.isEmpty(string)) {
                return string;
            }
        }
        return null;
    }

    private String getSecondAndMoreParagraphText(String text) {
        final String[] contentText = text.split("\n");
        StringBuilder result = new StringBuilder();
        int index = 0;
        for (String s : contentText) {
            String string = ContentConverter.filterCheckboxContent(s);

            if (!TextUtils.isEmpty(string)) {
                string = string.replace("\u200c", "").trim();
            }

            if (!TextUtils.isEmpty(string)) {
                if (index >= 1) {
                    if (index > 1) {
                        result.append("\n");
                    }

                    result.append(string);

                    if (index >= MAX_LINES) {
                        break;
                    }
                }
                index++;
            }
        }

        return result.toString();
    }

    public List<NoteAttribute> getPagedElements() {
        return mPagedElements;// TODO leak
    }

    public long getCreated() {
        return mCreated;
    }

    public void setCreated(long created) {
        this.mCreated = created;
    }

    public long getTopped() {
        return mTopped;
    }

    public void setTopped(long mTopped) {
        this.mTopped = mTopped;
    }

    public int getBackgroudRes() {
        return mBackgroundRes;
    }

    public void setBackgroundRes(int bg) {
        this.mBackgroundRes = bg;
    }

    public long getUpdated() {
        return mUpdated;
    }

    public void setUpdated(long updated) {
        this.mUpdated = updated;
    }

    public int getCreateConsole() {
        return mCreateConsole;
    }

    public void setCreateConsole(int createConsole) {
        this.mCreateConsole = createConsole;
    }

    public int getThumbType() {
        return mThumbType;
    }

    public void setThumbType(int thumbType) {
        this.mThumbType = thumbType;
    }

    public String getContent() {
        return mContent;
    }

    public void setContent(String content) {
        this.mContent = content;
    }

    public int getOwner() {
        return mOwner;
    }

    public void setOwner(int owner) {
        this.mOwner = owner;
    }

    public boolean isAvailable() {
        return mVersion != -1;
    }

    public String getGuid() {
        return mGuid;
    }

    public void setGuid(String guid) {
        this.mGuid = guid;
    }

    public int getVersion() {
        return mVersion;
    }

    public void setVersion(int version) {
        this.mVersion = version;
    }

    public int getState() {
        return mState;
    }

    public void setState(int state) {
        this.mState = state;
    }

    public String getGlobalId() {
        return mGlobalId;
    }

    public void setGlobalId(String globalId) {
        this.mGlobalId = globalId;
    }

    public String getAttachmentId() {
        return mAttachmentId;
    }

    public void setAttachmentId(String attachmentId) {
        this.mAttachmentId = attachmentId;
    }

    private String getTitleTextFromContent() {
        return getTitleTextFromContent(this.mContent, false);
    }


    public String getAlarmTitleText() {
        if (!TextUtils.isEmpty(getExtra().getTitle())) {
            return getExtra().getTitle();
        } else if (mThumbType == NoteAttribute.TYPE_TEXT_CONTENT) {
            return getTitleTextFromContent();
        } else {
            return MyApplication.getAppContext().getString(R.string.picture);
        }
    }

    public static String getTitleTextFromContent(String titleAndContent, boolean isFilterLen) {
        if (TextUtils.isEmpty(titleAndContent)) {
            return "";
        }
        titleAndContent = ContentConverter.filterCheckboxContent(titleAndContent);
        String textSub = (isFilterLen && (titleAndContent.length() > SHOW_TITLE_LEN)) ? Linkify.subEmojiCHString(titleAndContent, 0, SHOW_TITLE_LEN) : titleAndContent;
        if ((!TextUtils.isEmpty(textSub)) && textSub.contains("\n")) {
            final String[] contentText = textSub.split("\n");
            for (String s : contentText) {
                final String string = s.trim();
                if (!TextUtils.isEmpty(string)) {
                    return string;
                }
            }
        }
        return textSub;
    }

    /**
     * 检测是否有emoji表情
     *
     * @param source
     * @return
     */
    public static boolean containsEmoji(String source) {
        int len = source.length();
        for (int i = 0; i < len; i++) {
            char codePoint = source.charAt(i);
            if (!isEmojiCharacter(codePoint)) { // 如果不能匹配,则该字符是Emoji表情
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否是Emoji
     *
     * @param codePoint 比较的单个字符
     * @return
     */
    public static boolean isEmojiCharacter(char codePoint) {
        int code0 = 0x0;
        int code9 = 0x9;
        int codeA = 0xA;
        int codeD = 0xD;
        int code20 = 0x20;
        int codeD7FF = 0xD7FF;
        int codeE000 = 0xE000;
        int codeFFFD = 0xFFFD;
        return (codePoint == code0) || (codePoint == code9) || (codePoint == codeA) || (codePoint == codeD)
                || ((codePoint >= code20) && (codePoint <= codeD7FF)) || ((codePoint >= codeE000) && (codePoint <= codeFFFD));

    }


    public String getFolderGuid() {
        return mFolderGuid;
    }

    public void setFolderGuid(String folderGuid) {
        this.mFolderGuid = folderGuid;
    }

    public String getFolderName() {
        return mFolderName;
    }

    public void setFolderName(String folderName) {
        this.mFolderName = folderName;
    }

    public long getRecycled() {
        return mRecycled;
    }

    public void setRecycled(long recycled) {
        this.mRecycled = recycled;
    }

    public String getDelete() {
        return mDelete;
    }

    public void setDelete(String mDelete) {
        this.mDelete = mDelete;
    }

    public long getAlarmTime() {
        return mAlarmTime;
    }

    public void setAlarmTime(long alarmTime) {
        mAlarmTime = alarmTime;
    }

    public String getNoteSkin() {
        return mNoteSkin;
    }

    public void setNoteSkin(String mNoteSkin) {
        this.mNoteSkin = mNoteSkin;
    }

    public void setSkinId(String skinId) {
        if (SkinData.COLOR_SKIN_GREY.equals(skinId) || !SkinManager.INSTANCE.isEmbedSkin(skinId)) {
            mNoteSkin = SkinData.COLOR_SKIN_WHITE;
            getExtra().setSkinId(skinId);
        } else {
            mNoteSkin = skinId;
            getExtra().setSkinId("");
        }
    }

    public String getSkinId() {
        if (!TextUtils.isEmpty(getExtra().getSkinId())) {
            return getExtra().getSkinId();
        }

        return mNoteSkin;
    }

    public long getRecycledPre() {
        return mRecycledPre;
    }

    public void setRecycledPre(long mRecycledPre) {
        this.mRecycledPre = mRecycledPre;
    }

    public long getAlarmTimePre() {
        return mAlarmTimePre;
    }

    public void setAlarmTimePre(long mAlarmTimePre) {
        this.mAlarmTimePre = mAlarmTimePre;
    }

    public String getNoteSkinPre() {
        return mNoteSkinPre;
    }

    public void setNoteSkinPre(String mNoteSkinPre) {
        this.mNoteSkinPre = mNoteSkinPre;
    }

    public long getTimestamp() {
        return mTimestamp;
    }

    public void setTimestamp(long timestamp) {
        mTimestamp = timestamp;
    }

    public void setTitle(String title) {
        this.mExtra.setTitle(title);
    }

    public String getTitle() {
        return this.mExtra.getTitle();
    }

    public void setSysVersion(long sysVersion) {
        this.mSysVersion = sysVersion;
    }

    public long getSysVersion() {
        return mSysVersion;
    }

    @Override
    public int hashCode() {
        // FIXME cloud data may has no guid.
        return mGuid.hashCode();
    }

    @Override
    public boolean equals(Object object) {
        if (object == null) {
            return false;
        }
        if (object == this) {
            return true;
        }
        if (!(object instanceof NoteInfo)) {
            return false;
        }
        final NoteInfo other = (NoteInfo) object;
        if ((other.mState == this.mState)
                && (other.mGuid != null) && other.mGuid.equalsIgnoreCase(this.mGuid)) {
            return true;
        }
        return false;
    }

    @Override
    public String toString() {
        return "NoteInfo{"
                + "mAttributes=" + mAttributes
                + ", mVersion=" + mVersion
                + ", mCreateConsole=" + mCreateConsole
                + ", mThumbType=" + mThumbType
                + ", mState=" + mState
                + ", mOwner=" + mOwner
                + ", mBackgroundRes=" + mBackgroundRes
                + ", mUpdated=" + mUpdated
                + ", mCreated=" + mCreated
                + ", mTopped=" + mTopped
                + ", mGuid='" + mGuid + '\''
                + ", mContent='" + mContent + '\''
                + ", mGlobalId='" + mGlobalId + '\''
                + ", mAttachmentId='" + mAttachmentId + '\''
                + ", mWholeContentAttr=" + mWholeContentAttr
                + ", mPagedElements=" + mPagedElements
                + ", mFolderGuid='" + mFolderGuid + '\''
                + ", mFolderName='" + mFolderName + '\''
                + ", mRecycled=" + mRecycled
                + ", mDelete=" + mDelete
                + ", mAlarmTime=" + mAlarmTime
                + ", mNoteSkin=" + mNoteSkin
                + ", mExtra=" + mExtra
                + '}';
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(mGuid);
        dest.writeInt(mVersion);
        dest.writeInt(mState);
        dest.writeLong(mUpdated);
        dest.writeInt(mCreateConsole);
        dest.writeInt(mThumbType);
        dest.writeString(mContent);
        dest.writeParcelable(mWholeContentAttr, flags);
        dest.writeMap(mAttributes);
        dest.writeList(mPagedElements);
        dest.writeString(mFolderGuid);
        dest.writeString(mFolderName);
        dest.writeString(mDelete);
        dest.writeLong(mAlarmTime);
        dest.writeString(mNoteSkin);
        dest.writeParcelable(this.mExtra, flags);
    }

    @SuppressWarnings("unchecked")
    private NoteInfo(Parcel in) {
        mGuid = in.readString();
        mVersion = in.readInt();
        mState = in.readInt();
        mUpdated = in.readLong();
        mCreateConsole = in.readInt();
        mThumbType = in.readInt();
        mContent = in.readString();
        final ClassLoader classLoader = NoteAttribute.class.getClassLoader();
        in.readParcelable(classLoader);
        in.readMap(mAttributes, classLoader);
        mPagedElements = in.readArrayList(classLoader);
        mFolderGuid = in.readString();
        mFolderName = in.readString();
        mDelete = in.readString();
        mAlarmTime = in.readLong();
        mNoteSkin = in.readString();
        mExtra = in.readParcelable(NoteExtra.class.getClassLoader());
    }

    @SuppressWarnings("ucd")
    public static final Parcelable.Creator<NoteInfo> CREATOR = new Parcelable.Creator<NoteInfo>() {
        @Override
        public NoteInfo createFromParcel(Parcel in) {
            return new NoteInfo(in);
        }

        @Override
        public NoteInfo[] newArray(int size) {
            return new NoteInfo[size];
        }
    };

    public NoteExtra getExtra() {
        if (mExtra == null) {
            mExtra = NoteExtra.Companion.create(null);
        }
        return mExtra;
    }

    public void setExtra(NoteExtra extra) {
        this.mExtra = extra;
    }
}
