package com.oplus.cloud.sync.notetorichnote

import android.text.TextUtils
import com.nearme.note.util.NoteSearchManagerWrapper
import com.oplus.note.logger.AppLogger
import com.oplus.cloud.sync.MergeStrategy.MERGE_COUNT_LIMIT
import com.oplus.cloud.sync.richnote.RichNoteConstants
import com.oplus.cloud.sync.richnote.RichNoteStrategy
import com.oplus.note.repo.note.entity.RichNote
import com.oplus.note.repo.note.entity.RichNoteWithAttachments

class ToRichNoteSameContentStrategy : RichNoteStrategy() {

    private val mRichNoteList = ArrayList<RichNoteWithAttachments>()

    override fun merge(remoteData: RichNoteWithAttachments?, relatedData: RichNoteWithAttachments?): Boolean {
        AppLogger.BASIC.d(TAG, "ToRichNoteSameContentStrategy remoteData localId = " + remoteData!!.richNote.localId + " , globalID = " + remoteData.richNote.globalId)
        AppLogger.BASIC.d(TAG, "ToRichNoteSameContentStrategy relatedData localId = " + relatedData!!.richNote.localId + " , globalID = " + relatedData.richNote.globalId)
        relatedData.let {
            val sameRawTitle = (remoteData.richNote.rawTitle.hashCode() == it.richNote.rawTitle.hashCode())
                    && TextUtils.equals(remoteData.richNote.rawTitle, it.richNote.rawTitle)
            val sameRawContent = (remoteData.richNote.rawText.hashCode() == it.richNote.rawText.hashCode())
                    && TextUtils.equals(remoteData.richNote.rawText, it.richNote.rawText)
            if (!sameRawTitle || !sameRawContent) {
                return@let
            }
            it.richNote.state = RichNote.STATE_MODIFIED
            it.richNote.deleted = false
            it.richNote.version = RichNoteConstants.VERSION_UNCHANGE
            it.richNote.updateTime = remoteData.richNote.updateTime
            it.richNote.topTime = remoteData.richNote.topTime
            it.richNote.folderGuid = remoteData.richNote.folderGuid
            it.richNote.timestamp = System.currentTimeMillis()

            //pre字段解冲突
            handleConflict(it, remoteData)
            mRichNoteList.add(relatedData)
            if (mRichNoteList.size >= MERGE_COUNT_LIMIT) {
                repository.updateList(mRichNoteList)
                mRichNoteList.clear()
                NoteSearchManagerWrapper.notifyDataChange()
            }
            AppLogger.BASIC.d(TAG, "RichNoteSameContentStrategy merge over")
            return true
        }
        return false
    }

    private fun handleConflict(it: RichNoteWithAttachments, remoteData: RichNoteWithAttachments) {
        if (it.richNote.alarmTime == it.richNote.alarmTimePre) {
            it.richNote.alarmTime = remoteData.richNote.alarmTime
            it.richNote.alarmTimePre = remoteData.richNote.alarmTime
        } else {
            it.richNote.version += 1
        }
        if (it.richNote.recycleTime == it.richNote.recycleTimePre) {
            it.richNote.recycleTime = remoteData.richNote.recycleTime
            it.richNote.recycleTimePre = remoteData.richNote.recycleTime
        } else {
            it.richNote.version += 1
        }
        if (TextUtils.equals(it.richNote.skinId, it.richNote.skinIdPre)) {
            it.richNote.skinId = remoteData.richNote.skinId
            it.richNote.skinIdPre = remoteData.richNote.skinId
        } else {
            it.richNote.version += 1
        }
    }

    override fun mergeDataListBuffer() {
        if (mRichNoteList.size > 0) {
            repository.updateList(mRichNoteList)
            mRichNoteList.clear()
            NoteSearchManagerWrapper.notifyDataChange()
        }
    }

}