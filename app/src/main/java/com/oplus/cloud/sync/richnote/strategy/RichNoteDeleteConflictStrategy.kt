package com.oplus.cloud.sync.richnote.strategy

import com.oplus.note.logger.AppLogger
import com.oplus.cloud.sync.richnote.RichNoteStrategy
import com.oplus.note.repo.note.entity.RichNote
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.nearme.note.util.DataStatisticsHelper
import com.nearme.note.util.NoteSearchManagerWrapper
import com.oplus.cloudkit.util.Constants

class RichNoteDeleteConflictStrategy : RichNoteStrategy() {

    override fun merge(remoteData: RichNoteWithAttachments?, relatedData: RichNoteWithAttachments?): Boolean {
        if (relatedData!!.richNote.state != RichNote.STATE_UNCHANGE) {
            DataStatisticsHelper.noteCloudOps(TAG, "02010106", remoteData?.richNote)

            AppLogger.CLOUDKIT.d(TAG, "RichNoteDeleteConflictStrategy merge over, restore it id: ${relatedData.richNote.globalId}")

            //云端是删除状态，本地是修改或新建状态，将笔记恢复
            relatedData.let {
                it.richNote.state = RichNote.STATE_RESTORE
                if (sysRecordType == Constants.RECORD_TYPE_RICH_NOTE_ITEM) {
                    it.richNote.sysVersion = remoteData?.richNote?.sysVersion
                } else if (sysRecordType == Constants.RECORD_TYPE_ENCRYPT_RICH_NOTE_ITEM) {
                    it.richNote.encryptSysVersion = remoteData?.richNote?.encryptSysVersion
                } else {
                    it.richNote.encryptSysVersion = remoteData?.richNote?.encryptSysVersion
                    it.richNote.sysVersion = remoteData?.richNote?.sysVersion
                }
            }
            repository.update(relatedData.richNote)
            NoteSearchManagerWrapper.notifyDataChange()
            return true
        }
        AppLogger.CLOUDKIT.e(TAG, "delete remoteData merge failed!")
        return false
    }
}