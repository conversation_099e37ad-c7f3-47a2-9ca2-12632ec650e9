package com.oplus.cloud.sync.richnote.strategy

import com.oplus.note.logger.AppLogger
import com.oplus.cloud.sync.richnote.RichNoteStrategy
import com.oplus.note.repo.note.entity.RichNoteWithAttachments

class RichNoteConditionJudgeStrategy : RichNoteStrategy() {

    override fun merge(remoteData: RichNoteWithAttachments?, relatedData: RichNoteWithAttachments?): Boolean {
        if (remoteData == null) {
            AppLogger.CLOUDKIT.d(TAG, "RichNoteConditionJudgeStrategy merge over, remoteData is null")
            return true
        }
        return false
    }

}