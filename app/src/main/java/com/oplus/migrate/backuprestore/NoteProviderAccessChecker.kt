package com.oplus.migrate.backuprestore

import android.content.Context
import android.content.pm.PackageManager
import com.nearme.note.util.SignUtil
import com.oplus.note.logger.AppLogger
import com.oplus.note.common.PkgConstants
import java.util.*

object NoteProviderAccessChecker {

    const val TAG = "NoteProviderAccessChecker"
    const val NEW_CLOUD_MD5_SIGN = "6caf67a5e819edb9c9cb54477f247e64"
    const val RETAIL_MODE_MD5_SIGN = "0d2bb493d4c258eb105fa6e0d59ac47b"
    const val PKG_UNIVERSAL_SEARCH = "com.oneplus.universalsearch"
    const val PKG_ANDROID_LAUNCHER = "com.android.launcher"
    const val UNIVERSAL_SEARCH_MD5_SIGN = "23527ef30c2eb107dc50d2800794b5d58e6067fc"
    const val ANDROID_LAUNCHER_MD5_SIGN = "d6a6fca6cc19e84bc4b32a4e9ea6125c"
    const val SCOUT_SIGN = "aa3d5228afbbfd9222fd0d4c3d5aa8ff"
    const val PKG_RETAIL_MODE = "com.oneplus.retailmode"
    const val PKG_LAUNCHER = "net.oneplus.launcher"
    const val SCOUT_PKG = "com.oneplus.opscout"
    const val PKG_SYSTEM_UID = "android.uid.system:1000"

    val WHITE_LIST_SIGNATURES: MutableList<String> = ArrayList()
    val WHITE_LIST_PACKAGES: MutableList<String> = ArrayList()

    init {
        WHITE_LIST_SIGNATURES.add(UNIVERSAL_SEARCH_MD5_SIGN)
        WHITE_LIST_SIGNATURES.add(ANDROID_LAUNCHER_MD5_SIGN)

        WHITE_LIST_PACKAGES.add(PKG_UNIVERSAL_SEARCH)
        WHITE_LIST_PACKAGES.add(PKG_ANDROID_LAUNCHER)
    }

    fun checkIfCanAccessProvider(context: Context?, callingAppName: String?): Boolean {
        AppLogger.RED_MIGRATION.i(NoteBackupRestoreProvider.TAG, "callingAppName == $callingAppName")
        if (context == null || callingAppName.isNullOrEmpty()) {
            return false
        }
        //val callingAppName = context.packageManager.getNameForUid(Binder.getCallingUid())
        if (context.packageName == callingAppName) {
            return true
        }
        if (PKG_LAUNCHER == callingAppName) {
            return true
        }
        if (PKG_SYSTEM_UID == callingAppName) {
            return true
        }
        if (isSwitchApp(context, callingAppName)) {
            return true
        }
        if (isZenModeApp(context, callingAppName)) {
            return true
        }
        if (EncodeDecodeUtil.decode(EncodeDecodeUtil.NEW_CLOUD_PKG) == callingAppName && NEW_CLOUD_MD5_SIGN == getSign(context, callingAppName)) {
            return true
        }
        if (isScoutApp(context, callingAppName)) {
            return true
        }
        if (WHITE_LIST_PACKAGES.contains(callingAppName)
                && WHITE_LIST_SIGNATURES.contains(getSign(context, callingAppName))) {
            return true
        }
        return if (PKG_RETAIL_MODE == callingAppName && RETAIL_MODE_MD5_SIGN == getSign(context, callingAppName)) {
            true
        } else context.packageManager.checkSignatures(callingAppName!!, "android") == PackageManager.SIGNATURE_MATCH
    }

    fun getSign(context: Context, packageName: String?): String? {
        return SignUtil.getSign(context, packageName)
    }

    private fun isScoutApp(context: Context, callingAppName: String?): Boolean {
        return SCOUT_PKG == callingAppName && SCOUT_SIGN == getSign(context, callingAppName)
    }

    private fun isSwitchApp(context: Context, callingAppName: String?): Boolean {
        return EncodeDecodeUtil.decode(EncodeDecodeUtil.SWITCH_PKG) == callingAppName
                && EncodeDecodeUtil.SWITCH_SIGN == getSign(context, callingAppName)
    }

    private fun isZenModeApp(context: Context, callingAppName: String?): Boolean {
        return EncodeDecodeUtil.decode(EncodeDecodeUtil.ZENMODE_PKG) == callingAppName
                && EncodeDecodeUtil.ZENMODE_SIGN == getSign(context, callingAppName)
    }
}