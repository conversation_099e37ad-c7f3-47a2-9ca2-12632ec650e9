package com.oplus.migrate.utils

import android.content.Context
import com.oplus.note.logger.AppLogger
import com.oplus.note.utils.SharedPreferencesUtil
import java.io.File

/**
 * https://hio.oppo.com/app/ozone/appmanagement/gotoOzoneCircleKbItemDetail?page=ozone&source=index&enc_kbi_id=158009105_157683174&folder_id=202979#readersBox
 * 三、应用私有数据文件权限修复接口
 * 接口类：OplusPackageManager.java  //包 package android.content.pm;
 *
 * 接口1：public boolean fixupAppData(String pkgName, int flags);  //修复flags代表的私有数据区域的所有文件权限
 *
 * 接口2：public boolean fixupAppData(String pkgName, @Nullable String relativePath, int flags);  //修复flags代表的私有数据区域的指定相对路径下的文件权限
 *
 * pkgName：文件归属的应用包名
 *
 * flag定义：
 *
 * FLAG_APP_DATA_DE = 1
 *
 * FLAG_APP_DATA_CE = 2
 *
 * FLAG_APP_DATA_EXTERNAL_DATA = 16
 *
 * FLAG_APP_DATA_EXTERNAL_MEDIA = 32
 *
 * FLAG_APP_DATA_EXTERNAL_OBB = 64
 *
 * 以上接口和常量在 调试版本 上可以反射调用（后续在Android S的正式版本上也都可以通过反射调用拿到）。add on sdk在S上还没有出版本，后面S上有出版本，可以自行选择换成sdk调用进行编译。
 *
 * 修复大量文件的权限是耗时工作，所以提供了带相对路径的接口，希望能最小化权限修复的操作耗时。
 *
 * 返回值：返回是否成功。
 *
 * 接口权限控制：调用方需要有oplus.permission.OPLUS_COMPONENT_SAFE权限。
 */
object FilePermissionUtil {

    private const val TAG = "FilePermissionUtil"
    private const val FLAG_APP_DATA_CE = 2

    fun fixPermission(context: Context, path: String) {
        AppLogger.RED_MIGRATION.v(TAG, "fixPermission $path begin")
        val oplusPm = ReflectUtils.invokeConstructorWithParams("android.content.pm.OplusPackageManager", arrayOf(Context::class.java), arrayOf(context))

        ReflectUtils.invokeMethodWithParams(oplusPm, "android.content.pm.OplusPackageManager", "fixupAppData",
            arrayOf(String::class.java, String::class.java, Int::class.javaPrimitiveType), arrayOf(context.packageName, path, FLAG_APP_DATA_CE))
        SharedPreferencesUtil.getInstance().putBoolean(context, SharedPreferencesUtil.FILE_PERMISSION_NAME,
                SharedPreferencesUtil.FILE_PERMISSION_KEY, true)
        AppLogger.RED_MIGRATION.v(TAG, "fixPermission $path end")
    }

    @JvmStatic
    fun fixAppData(context: Context) {
        // should fix file permission manually
        try {
            fixPermission(context, File.separator + "files")
        } catch (ignored: Throwable) {
            AppLogger.BASIC.d(TAG, "fixupAppData:" + ignored.message)
        }
    }
}