package com.oplus.cloudkit.metadata

import com.nearme.note.skin.SkinData
import com.oplus.cloud.sync.richnote.RichNoteConstants

/**
 * the field name should be same as [RichNoteConstants]
 */
// 以下参数名下是星链平台表一致，不要随意修改
data class RichNoteMetaData(
    var localId: String? = null,

    var rawText: String? = null,
    var rawTitle: String? = null,

    var folderGuid: String? = null,

    var createTime: Long = 0,
    var updateTime: Long = 0,
    var topTime: Long = 0,
    var recycleTime: Long = 0,
    var alarmTime: Long = 0,

    var skinId: String = SkinData.COLOR_SKIN_WHITE,

    var extra: String? = null,

    var version: Int = 0,
    var category: Int = 0,
    var dataVersion: Int = 0,

    var itemId: String? = null, // the value is same as localId

    var attachments: List<AttachmentMetaData>? = null, // only image infos.

    /**
     * add [speechLog] on call summary
     */
    var speechLog: String? = null,

    /**
     * add [encrypted] on call summary
     */
    var encrypted: Boolean = false,

    var attachmentExtra: String? = null
)

// 以下参数名下是星链平台表一致，不要随意修改
data class AttachmentMetaData(
    var type: Int,
    var id: String,
    var url: String
)