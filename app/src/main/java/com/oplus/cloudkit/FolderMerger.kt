/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - FolderMeger
 ** Description:
 **         v1.0:   Create FolderMeger file
 **
 ** Version: 1.0
 ** Date: 2023/11/06
 ** Author: Ji<PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/11/6   1.0      Create this module
 ********************************************************************************/
package com.oplus.cloudkit

import android.content.Context
import android.text.TextUtils
import com.nearme.note.MyApplication
import com.oplus.note.repo.note.entity.FolderInfo
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.FolderUtil
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderExtra
import com.nearme.note.db.isLatestChangeThan
import com.nearme.note.db.localEncryptChanged
import com.nearme.note.thirdlog.ThirdLogNoteBuildHelper
import com.nearme.note.util.DataStatisticsHelper
import com.oplus.cloud.agent.BaseSyncAgent.FolderBean
import com.oplus.cloud.status.Device
import com.oplus.cloudkit.util.Constants.Companion.RECORD_TYPE_ENCRYPT_FOLDER
import com.oplus.cloudkit.util.Constants.Companion.RECORD_TYPE_FOLDER
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import com.oplus.note.utils.base64
import java.util.Date
import java.util.UUID
import kotlin.math.max

class FolderMerger(private val mContext: Context) {

    private var edfHelper: EncryptDecryptFolderHelper? = null
    private val foldersEncryptStateChange = mutableListOf<Triple<Int, String, Long>>()

    private fun deleteMerger(
        localFolders: MutableList<Folder>,
        cloudFolder: FolderBean
    ) {
        localFolders.find { it.guid == cloudFolder.mFolderGuid }?.apply {
            if (state == FolderInfo.FOLDER_STATE_UNCHANGE || state == FolderInfo.FOLDER_STATE_DELETED) {
                DataStatisticsHelper.folderCloudOps(
                        TAG,
                        "02020101",
                        cloudFolder.mFolderGuid,
                        cloudFolder.mFolderName
                )

                handleFolderCase6(listOf(guid))
            } else if (state == FolderInfo.FOLDER_STATE_MODIFIED || state == FolderInfo.FOLDER_STATE_NEW) {
                //云端是删除状态，本地是修改或新建状态，把笔记本从回收站恢复
                AppLogger.CLOUDKIT.d(TAG, "restore  ID:$guid folder.")
                state = FolderInfo.FOLDER_RESTORE
                if (cloudFolder.mSysRecordType == RECORD_TYPE_FOLDER) {
                    sysVersion = cloudFolder.mSysVersion
                } else {
                    encryptSysVersion = cloudFolder.mSysVersion
                }
                AppDatabase.getInstance().foldersDao().updateFolder(this)
            }
        }
    }

    @Suppress("LongMethod")
    fun onRecoveryFolders(
        cloudFolders: List<FolderBean>?,
        skipCache: Boolean = false
    ) {
        AppLogger.CLOUD.d(TAG, "onRecoveryFolders(), cloudFolders: $cloudFolders")
        if (cloudFolders.isNullOrEmpty()) {
            return
        }

        val localFolders = AppDatabase.getInstance().foldersDao().allFoldersOrderByCreateTime
        for (cloudFolder in cloudFolders) {
            if ((cloudFolder.mState == FolderInfo.FOLDER_STATE_UNCHANGE ||
                            cloudFolder.mState == FolderInfo.FOLDER_STATE_DELETED) && skipCache.not()
            ) {
                edfHelper?.cache(cloudFolder)
                continue
            }

            if (cloudFolder.mState == FolderInfo.FOLDER_STATE_DELETED) {
                deleteMerger(localFolders, cloudFolder)
                continue
            }

            val folderList = AppDatabase.getInstance().foldersDao()
                    .getFoldersWithGuidOrName(cloudFolder.mFolderGuid, cloudFolder.mFolderName)
            val folderWithSameGuid =
                    findLocalFolderWithSameGuid(folderList, cloudFolder.mFolderGuid)
            if (null != folderWithSameGuid) {
                val iterator = localFolders.iterator()
                while (iterator.hasNext()) {
                    val folder = iterator.next()
                    if (folder.id == folderWithSameGuid.id) {
                        iterator.remove()
                        break
                    }
                }
                if (TextUtils.equals(folderWithSameGuid.name, cloudFolder.mFolderName)) {
                    handleFolderCase1(cloudFolder, folderWithSameGuid)
                } else {
                    handleFolderCase2(cloudFolder, folderWithSameGuid)
                }
            } else {
                val folderWithSameName =
                        findLocalFolderWithSameName(folderList, cloudFolder.mFolderName)
                if (null != folderWithSameName) {
                    val iterator = localFolders.iterator()
                    while (iterator.hasNext()) {
                        val folder = iterator.next()
                        if (folder.id == folderWithSameName.id) {
                            iterator.remove()
                            break
                        }
                    }
                    handleFolderCase3(cloudFolder, folderWithSameName)
                } else {
                    DataStatisticsHelper.folderCloudOps(
                            TAG,
                            "02020102",
                            cloudFolder.mFolderGuid,
                            cloudFolder.mFolderName
                    )
                    cloudFolder.mState = FolderInfo.FOLDER_STATE_UNCHANGE
                    handleFolderCase5(cloudFolder)
                }
            }
            DataStatisticsHelper.folderCloudOps(
                    TAG,
                    "02020103",
                    cloudFolder.mFolderGuid,
                    cloudFolder.mFolderName
            )
        }
    }

    fun onCombineRecoverFolders(
        folder: FolderBean,
        matchEncryptFolder: FolderBean
    ) {
        val sameGuidFolder = AppDatabase.getInstance().foldersDao().findByGuid(folder.mFolderGuid)
        AppLogger.BASIC.d(TAG, "onCombineRecoverFolders, sameGuidFold is null = ${sameGuidFolder == null}")
        if (sameGuidFolder == null) {
            if ((folder.mState == FolderInfo.FOLDER_STATE_DELETED) &&
                    matchEncryptFolder.mState == FolderInfo.FOLDER_STATE_UNCHANGE
            ) {
                /**
                 * 主表删除，加密表新增，本地没有folder，即从云端同步新的加密folder到本地
                 */
                combineHandleFolderCase5(matchEncryptFolder, folder)
            } else if ((folder.mState == FolderInfo.FOLDER_STATE_UNCHANGE) &&
                    matchEncryptFolder.mState == FolderInfo.FOLDER_STATE_DELETED
            ) {
                /**
                 * 主表新增，加密表删除，本地没有folder，是否存在此场景？
                 */
                combineHandleFolderCase5(folder, matchEncryptFolder)
            } else {
                AppLogger.CLOUDKIT.e(TAG, "onCombineRecoverFolders Error1")
            }
        } else {
            /**
             * 主表删除，加密表新增，本地有folder，即从云端同步B端加密同一folder到本地
             * 与本地folder解冲突，策略为加密判断encrypt_pre字段，其余内容比较ModifyTime
             */
            if ((folder.mState == FolderInfo.FOLDER_STATE_DELETED) &&
                    (matchEncryptFolder.mState == FolderInfo.FOLDER_STATE_UNCHANGE)
            ) {
                /**
                 * 主表删除，加密表新增，本地有folder，即从云端同步相同folder的加密操作与本地folder做解冲突
                 */
                if (folder.mFolderName == matchEncryptFolder.mFolderName) {
                    combineHandleFolderCase1(matchEncryptFolder, folder, sameGuidFolder)
                } else {
                    combineHandleFolderCase2(matchEncryptFolder, folder, sameGuidFolder)
                }
            } else if ((folder.mState == FolderInfo.FOLDER_STATE_UNCHANGE) &&
                    (matchEncryptFolder.mState == FolderInfo.FOLDER_STATE_DELETED)
            ) {
                /**
                 * 主表新增，加密表删除，本地有folder，即从云端同步相同folder的解密操作与本地folder做解冲突
                 */
                if (folder.mFolderName == matchEncryptFolder.mFolderName) {
                    combineHandleFolderCase1(folder, matchEncryptFolder, sameGuidFolder)
                } else {
                    combineHandleFolderCase2(folder, matchEncryptFolder, sameGuidFolder)
                }
            } else {
                AppLogger.CLOUDKIT.e(TAG, "onCombineRecoverFolders Error2")
            }
        }
    }


    /**
     *  case 1: 如果guid和name都相同
     *  ① encryptedPre != encrypted 表示过本地进行了加解密操作，encrypted以本地为准，encryptedPre置为本地encrypted
     *  ② encryptedPre == encrypted 表示本地进行偶数次加解密，最后状态未发生变化，以云端encrypted字段为准，encryptedPre置为云端encrypted
     *  ③ 笔记本除加密信息外 其余信息比较modifyTime，保留时间新的一端；
     */
    private fun handleFolderCase1(
        cloudFolder: FolderBean,
        localFolder: Folder
    ) {
        AppLogger.CLOUD.d(
                TAG,
                "handleFolderCase1(), cloudFolder: $cloudFolder, localFolder: $localFolder"
        )
        if (localFolder.isLatestChangeThan(cloudFolder).not()) {
            localFolder.name = cloudFolder.mFolderName
            localFolder.modifyDevice = cloudFolder.mModifyDevice
            localFolder.extra = FolderExtra.create(cloudFolder.mExtra)
            localFolder.modifyTime = Date(cloudFolder.mModifyTime)
            localFolder.createTime = Date(cloudFolder.mCreateTime)
            localFolder.state = FolderInfo.FOLDER_STATE_UNCHANGE
        } else {
            localFolder.state = FolderInfo.FOLDER_STATE_MODIFIED
        }
        if (localFolder.localEncryptChanged()) {
            //本地加解密状态已改变，已本地状态为准
            localFolder.state = FolderInfo.FOLDER_STATE_MODIFIED
        } else {
            //本地加解密状态未改变，以云端为准
            localFolder.encrypted = cloudFolder.mEncrypted
        }
        if (cloudFolder.mSysRecordType == RECORD_TYPE_FOLDER) {
            localFolder.sysVersion = cloudFolder.mSysVersion
        } else {
            localFolder.encryptSysVersion = cloudFolder.mSysVersion
        }
        AppDatabase.getInstance().foldersDao().updateFolder(localFolder)
    }


    /**
     * @param cloudFolder 表新增
     * @param remove 表删除
     * @param localFolder 本地同id数据
     */
    private fun combineHandleFolderCase1(
        cloudFolder: FolderBean,
        remove: FolderBean,
        localFolder: Folder
    ) {
        AppLogger.CLOUD.d(
                TAG,
                "combineHandleFolderCase1(), cloudFolder: $cloudFolder, localFolder: $localFolder"
        )
        if (localFolder.isLatestChangeThan(cloudFolder)) {
            if (localFolder.state == FolderInfo.FOLDER_STATE_DELETED) {
                localFolder.state = FolderInfo.FOLDER_STATE_DELETED
            } else {
                localFolder.state = FolderInfo.FOLDER_STATE_MODIFIED
            }
        } else {
            localFolder.name = cloudFolder.mFolderName
            localFolder.modifyDevice = cloudFolder.mModifyDevice
            localFolder.extra = FolderExtra.create(cloudFolder.mExtra)
            localFolder.modifyTime = Date(cloudFolder.mModifyTime)
            localFolder.createTime = Date(cloudFolder.mCreateTime)
            localFolder.state = FolderInfo.FOLDER_STATE_UNCHANGE
        }
        if (localFolder.localEncryptChanged()) {
            //本地加解密状态已改变，已本地状态为准
            if (localFolder.state == FolderInfo.FOLDER_STATE_DELETED) {
                localFolder.state = FolderInfo.FOLDER_STATE_DELETED
            } else {
                localFolder.state = FolderInfo.FOLDER_STATE_MODIFIED
            }
        } else {
            //本地加解密状态未改变，以云端为准
            localFolder.encrypted = cloudFolder.mEncrypted
        }
        localFolder.encryptedPre = localFolder.encrypted
        localFolder.sysVersion = listOf(cloudFolder, remove).find { it.mSysRecordType == RECORD_TYPE_FOLDER }?.mSysVersion
                ?: 0L
        localFolder.encryptSysVersion = listOf(cloudFolder, remove).find { it.mSysRecordType == RECORD_TYPE_ENCRYPT_FOLDER }?.mSysVersion
                ?: 0L
        AppDatabase.getInstance().foldersDao().updateFolder(localFolder)
    }

    /**
     * case 2: the guid is the same, but the name is not the same. compare the modifyTime
     */
    private fun handleFolderCase2(
        cloudFolder: FolderBean,
        localFolder: Folder
    ) {
        AppLogger.CLOUD.d(
                TAG,
                "handleFolderCase2(), cloudFolder: $cloudFolder, localFolder: $localFolder"
        )
        if (localFolder.isLatestChangeThan(cloudFolder)) {
            AppLogger.CLOUD.d(TAG, "Local changes newer than the cloud")
            localFolder.state = FolderInfo.FOLDER_STATE_MODIFIED
        } else {
            localFolder.name = cloudFolder.mFolderName
            localFolder.modifyDevice = cloudFolder.mModifyDevice
            localFolder.extra = FolderExtra.create(cloudFolder.mExtra)
            localFolder.modifyTime = Date(cloudFolder.mModifyTime)
            localFolder.createTime = Date(cloudFolder.mCreateTime)
            localFolder.state = FolderInfo.FOLDER_STATE_UNCHANGE
        }

        if (localFolder.localEncryptChanged()) {
            //本地加解密状态已改变，已本地状态为准
            localFolder.state = FolderInfo.FOLDER_STATE_MODIFIED
        } else {
            //本地加解密状态未改变，以云端为准
            localFolder.encrypted = cloudFolder.mEncrypted
        }

        if (cloudFolder.mSysRecordType == RECORD_TYPE_FOLDER) {
            localFolder.sysVersion = cloudFolder.mSysVersion
        } else {
            localFolder.encryptSysVersion = cloudFolder.mSysVersion
        }
        AppDatabase.getInstance().foldersDao().updateFolder(localFolder)
    }


    private fun combineHandleFolderCase2(
        cloudFolder: FolderBean,
        remove: FolderBean,
        localFolder: Folder
    ) {
        AppLogger.CLOUD.d(
                TAG,
                "combineHandleFolderCase2(), cloudFolder: $cloudFolder, localFolder: $localFolder"
        )
        if (localFolder.isLatestChangeThan(cloudFolder)) {
            AppLogger.CLOUD.d(TAG, "Local changes newer than the cloud")
            if (localFolder.state == FolderInfo.FOLDER_STATE_DELETED) {
                localFolder.state = FolderInfo.FOLDER_STATE_DELETED
            } else {
                localFolder.state = FolderInfo.FOLDER_STATE_MODIFIED
            }
        } else {
            localFolder.name = cloudFolder.mFolderName
            localFolder.modifyDevice = cloudFolder.mModifyDevice
            localFolder.extra = FolderExtra.create(cloudFolder.mExtra)
            localFolder.modifyTime = Date(cloudFolder.mModifyTime)
            localFolder.createTime = Date(cloudFolder.mCreateTime)
            localFolder.state = FolderInfo.FOLDER_STATE_UNCHANGE
        }

        if (localFolder.localEncryptChanged()) {
            //本地加解密状态已改变，已本地状态为准
            if (localFolder.state == FolderInfo.FOLDER_STATE_DELETED) {
                localFolder.state = FolderInfo.FOLDER_STATE_DELETED
            } else {
                localFolder.state = FolderInfo.FOLDER_STATE_MODIFIED
            }
        } else {
            //本地加解密状态未改变，以云端为准
            localFolder.encrypted = cloudFolder.mEncrypted
        }
        localFolder.encryptedPre = localFolder.encrypted
        localFolder.sysVersion = listOf(cloudFolder, remove).find { it.mSysRecordType == RECORD_TYPE_FOLDER }?.mSysVersion
                ?: 0L
        localFolder.encryptSysVersion = listOf(cloudFolder, remove).find { it.mSysRecordType == RECORD_TYPE_ENCRYPT_FOLDER }?.mSysVersion
                ?: 0L

        AppDatabase.getInstance().foldersDao().updateFolder(localFolder)
    }

    /**
     * case 3: the guid is not the same, but the name is the same. merge the two folders with the same name.
     */
    private fun handleFolderCase3(
        cloudFolder: FolderBean,
        localFolder: Folder
    ) {
        if (handleFolderCaseEncrypted(cloudFolder, localFolder)) {
            return
        }
        if (handleFolderCaseQuick(cloudFolder, localFolder)) {
            return
        }
        if (handleFolderCaseCallSummary(cloudFolder, localFolder)) {
            return
        }
        if (handleFolderCaseArticleSummary(cloudFolder, localFolder)) {
            return
        }
        if (handleFolderCaseAudioSummary(cloudFolder, localFolder)) {
            return
        }
        if (handlerFolderPreset(cloudFolder, localFolder)) {
            return
        }


        AppLogger.CLOUD.d(
                TAG,
                "handleFolderCase3(), cloudFolder: $cloudFolder, localFolder: $localFolder"
        )
        val info = FolderInfo(localFolder)
        info.guid = cloudFolder.mFolderGuid
        if (localFolder.isLatestChangeThan(cloudFolder).not()) {
            info.modifyDevice = cloudFolder.mModifyDevice
            info.createTime = cloudFolder.mCreateTime
            info.modifyTime = cloudFolder.mModifyTime
            info.extra = FolderExtra.create(cloudFolder.mExtra)
        }
        info.state = FolderInfo.FOLDER_STATE_UNCHANGE
        if (cloudFolder.mSysRecordType == RECORD_TYPE_FOLDER) {
            info.sysVersion = cloudFolder.mSysVersion
        } else {
            info.encryptSysVersion = cloudFolder.mSysVersion
        }
        if (info.encryptedPre != info.encrypted && info.guid != FolderInfo.FOLDER_GUID_ENCRYPTED) {
            //本地加解密状态已改变，已本地状态为准
        } else {
            //本地加解密状态未改变，已云端状态为准
            info.encrypted = cloudFolder.mEncrypted
        }

        FolderUtil.getInstance()
                .updateFolderWithFolderInfoSyncForRichNote(localFolder.guid, info)
    }

    /**
     * 处理不支持加密功能版本上的创建的普通“加密笔记”，同步到支持加密版本的冲突
     * 低版本同步上来的普通加密笔记，在高版本上会新建为一个带后缀的普通“加密笔记”文件夹，如“加密笔记1”，“加密笔记2”
     * 以此类推
     *
     * @param cloudFolder 云端文件夹信息
     * @param localFolder 本地文件夹信息
     * @return
     */
    private fun handleFolderCaseEncrypted(
        cloudFolder: FolderBean,
        localFolder: Folder
    ): Boolean {
        AppLogger.CLOUD.d(
                TAG,
                "handleFolderCaseEncrypted(), cloudFolder: $cloudFolder, localFolder: $localFolder"
        )
        if (FolderInfo.FOLDER_GUID_ENCRYPTED == localFolder.guid) {
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.CLOUD.d(TAG, "handleFolderCaseEncrypted(), localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()
            for (folder in localFolders) {
                if (folder.name.contains(localFolder.name) && folder.name.length > localFolder.name.length) {
                    folderNames.add(folder.name)
                }
            }
            AppLogger.CLOUD.d(TAG, "handleFolderCaseEncrypted(), folderNames: $folderNames")
            var index = 0
            for (name in folderNames) {
                val suffix = name.substring(localFolder.name.length)
                index = Math.max(index, suffix.toInt())
            }
            val newFolderName = cloudFolder.mFolderName + (index + 1)
            AppLogger.CLOUD.d(TAG, "handleFolderCaseEncrypted(), newFolderName: $newFolderName")
            FolderUtil.insertFolderNameSync(
                    mContext, newFolderName, cloudFolder.mFolderGuid,
                    Device.getDeviceIMEI(mContext), cloudFolder.mCreateTime, cloudFolder.mState,
                    cloudFolder.mEncrypted, null, cloudFolder.mSysVersion
            )
            return true
        }
        return false
    }

    /**
     * 处理不支持加速记功能版本上的创建的普通“速记”，同步到支持速记版本的冲突
     * 低版本同步上来的普通速记笔记，在高版本上会新建为一个带后缀的普通“速记”文件夹，如“速记1”，“速记2”
     * 以此类推
     *
     * @param cloudFolder 云端文件夹信息
     * @param localFolder 本地文件夹信息
     * @return
     */
    private fun handleFolderCaseQuick(
        cloudFolder: FolderBean,
        localFolder: Folder
    ): Boolean {
        AppLogger.CLOUD.d(
                TAG, "handleQuickFolder(), cloudFolder: $cloudFolder, localFolder: $localFolder"
        )
        if (cloudFolder.mFolderName == FolderUtil.DEFAULT_QUICK_NOTE && FolderInfo.FOLDER_GUID_QUICK != cloudFolder.mFolderGuid) {
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.CLOUD.d(TAG, "handleQuickFolder(), localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()
            for (folder in localFolders) {
                if (folder.name.contains(cloudFolder.mFolderName) && folder.name.length > cloudFolder.mFolderName.length) {
                    folderNames.add(folder.name)
                }
            }
            AppLogger.CLOUD.d(TAG, "handleQuickFolder(), folderNames: $folderNames")
            var index = 0
            for (name in folderNames) {
                val suffix = name.substring(cloudFolder.mFolderName.length)
                kotlin.runCatching {
                    index = Math.max(index, suffix.toInt())
                }
            }
            val newFolderName = cloudFolder.mFolderName + (index + 1)
            AppLogger.CLOUD.d(TAG, "handleQuickFolder(), newFolderName: $newFolderName")
            FolderUtil.insertFolderNameSync(
                    mContext,
                    newFolderName,
                    cloudFolder.mFolderGuid,
                    Device.getDeviceIMEI(mContext),
                    cloudFolder.mCreateTime,
                    FolderInfo.FOLDER_STATE_MODIFIED,
                    cloudFolder.mEncrypted,
                    null,
                    cloudFolder.mSysVersion
            )
            return true
        }
        return false
    }


    /**
     * 处理不支持通话摘要功能版本上的创建的普通“通话摘要”笔记本，同步到支持通话摘要版本的冲突
     * 低版本同步上来的通话摘要记笔记本，在高版本上会新建为一个带后缀的普通“通话摘要”文件夹，如“通话摘要1”，“通话摘要2”
     * 以此类推
     *
     * @param cloudFolder 云端文件夹信息
     * @param localFolder 本地文件夹信息
     * @return
     */
    @Suppress("LongMethod")
    private fun handleFolderCaseCallSummary(
        cloudFolder: FolderBean,
        localFolder: Folder
    ): Boolean {
        AppLogger.CLOUD.d(
                TAG, "handleFolderCaseCallSummary(), cloudFolder: $cloudFolder, localFolder: $localFolder"
        )
        if (cloudFolder.mFolderName == FolderUtil.DEFAULT_CALL_SUMMARY && FolderInfo.FOLDER_GUID_CALL_SUMMARY != cloudFolder.mFolderGuid) {
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.CLOUD.d(TAG, "handleFolderCaseCallSummary(), localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()
            for (folder in localFolders) {
                if (folder.name.contains(cloudFolder.mFolderName) && folder.name.length > cloudFolder.mFolderName.length) {
                    folderNames.add(folder.name)
                }
            }
            AppLogger.CLOUD.d(TAG, "handleFolderCaseCallSummary(), folderNames: $folderNames")
            var index = 0
            for (name in folderNames) {
                val suffix = name.substring(cloudFolder.mFolderName.length)
                kotlin.runCatching {
                    index = max(index, suffix.toInt())
                }
            }
            val newFolderName = cloudFolder.mFolderName + (index + 1)
            AppLogger.CLOUD.d(
                    TAG,
                    "handleFolderCaseCallSummary(), newFolderName: $newFolderName"
            )
            FolderUtil.insertFolderNameSync(
                    mContext,
                    newFolderName,
                    cloudFolder.mFolderGuid,
                    Device.getDeviceIMEI(mContext),
                    cloudFolder.mCreateTime,
                    FolderInfo.FOLDER_STATE_MODIFIED,
                    cloudFolder.mEncrypted,
                    null,
                    cloudFolder.mSysVersion
            )
            return true
        } else if (FolderInfo.FOLDER_GUID_CALL_SUMMARY == cloudFolder.mFolderGuid) {

            /**
             * 针对通话摘要笔记本，
             * 如果云端为真笔记本，本地为假笔记本，则需要将真笔记本保留同时将假笔记本改为”通话摘要X"
             * 将云端真笔记本保留即可
             */
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.CLOUD.d(TAG, "handleFolderCaseCallSummary2, localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()
            for (folder in localFolders) {
                if (folder.name.contains(cloudFolder.mFolderName) && folder.name.length > cloudFolder.mFolderName.length) {
                    folderNames.add(folder.name)
                }
                if (folder.name.equals(cloudFolder.mFolderName)) {
                    folderNames.add(folder.name)
                }
            }
            var index = 0
            for (name in folderNames) {
                val suffix = name.substring(cloudFolder.mFolderName.length)
                kotlin.runCatching {
                    index = max(index, suffix.toInt())
                }
            }
            val newFolderName = cloudFolder.mFolderName + (index + 1)
            AppLogger.CLOUD.d(
                TAG,
                "handleFolderCaseCallSummary2, newFolderName: ${newFolderName.base64()}"
            )

            localFolder.name = newFolderName
            AppDatabase.getInstance().foldersDao().updateFolder(localFolder)
            handleFolderCase5(cloudFolder)
            return true
        }
        return false
    }

    /**
     * 处理web端创建的假”文章摘要“，同步到本地时，数据冲突处理
     * 需要将假的”全部笔记“重命名为其他名称
     * 以此类推
     *
     * @param cloudFolder 云端文件夹信息
     * @param localFolder 本地文件夹信息
     * @return
     */
    @Suppress("LongMethod")
    private fun handleFolderCaseArticleSummary(
        cloudFolder: FolderBean,
        localFolder: Folder
    ): Boolean {
        AppLogger.CLOUD.d(
            TAG, "handleFolderCaseArticleSummary(), cloudFolder: $cloudFolder, localFolder: $localFolder"
        )
        if (cloudFolder.mFolderName == ThirdLogNoteBuildHelper.FOLDER_CALL_SUMMARY &&
            FolderInfo.FOLDER_GUID_ARTICLE_SUMMARY != cloudFolder.mFolderGuid) {
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.CLOUD.d(TAG, "handleFolderCaseArticleSummary(), localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()
            for (folder in localFolders) {
                if (folder.name.contains(cloudFolder.mFolderName) && folder.name.length > cloudFolder.mFolderName.length) {
                    folderNames.add(folder.name)
                }
            }
            var index = 0
            for (name in folderNames) {
                val suffix = name.substring(cloudFolder.mFolderName.length)
                kotlin.runCatching {
                    index = max(index, suffix.toInt())
                }
            }
            val newFolderName = cloudFolder.mFolderName + (index + 1)
            AppLogger.CLOUD.d(
                TAG,
                "handleFolderCaseArticleSummary(), newFolderName: ${newFolderName.base64()}"
            )
            FolderUtil.insertFolderNameSync(
                mContext,
                newFolderName,
                cloudFolder.mFolderGuid,
                Device.getDeviceIMEI(mContext),
                cloudFolder.mCreateTime,
                FolderInfo.FOLDER_STATE_MODIFIED,
                cloudFolder.mEncrypted,
                null,
                cloudFolder.mSysVersion
            )
            return true
        } else if (FolderInfo.FOLDER_GUID_ARTICLE_SUMMARY == cloudFolder.mFolderGuid) {

            /**
             * 针对文章摘要笔记本，
             * 如果云端为真笔记本，本地为假笔记本，则需要将真笔记本保留同时将假笔记本改为”文章摘要X"
             * 将云端真笔记本保留即可
             */
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.CLOUD.d(TAG, "handleFolderCaseArticleSummary2, localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()
            for (folder in localFolders) {
                if (folder.name.contains(cloudFolder.mFolderName) && folder.name.length > cloudFolder.mFolderName.length) {
                    folderNames.add(folder.name)
                }
            }
            var index = 0
            for (name in folderNames) {
                val suffix = name.substring(cloudFolder.mFolderName.length)
                kotlin.runCatching {
                    index = max(index, suffix.toInt())
                }
            }
            val newFolderName = cloudFolder.mFolderName + (index + 1)
            AppLogger.CLOUD.d(
                TAG,
                "handleFolderCaseArticleSummary2, newFolderName: ${newFolderName.base64()}"
            )

            localFolder.name = newFolderName
            AppDatabase.getInstance().foldersDao().updateFolder(localFolder)
            handleFolderCase5(cloudFolder)
            return true
        }
        return false
    }


    /**
     * 处理web端创建的假”AI 录音摘要“，同步到本地时，数据冲突处理
     * 需要将假的”录音摘要“重命名为其他名称
     * 以此类推
     *
     * @param cloudFolder 云端文件夹信息
     * @param localFolder 本地文件夹信息
     * @return
     */
    @Suppress("LongMethod")
    private fun handleFolderCaseAudioSummary(
        cloudFolder: FolderBean,
        localFolder: Folder
    ): Boolean {
        AppLogger.CLOUD.d(
            TAG, "handleFolderCaseAudioSummary(), cloudFolder: $cloudFolder, localFolder: $localFolder"
        )
        if (cloudFolder.mFolderName == MyApplication.appContext.resources.getString(com.oplus.note.baseres.R.string.ai_voice_summary) &&
            FolderInfo.FOLDER_GUID_AUDIO_SUMMARY != cloudFolder.mFolderGuid) {
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.CLOUD.d(TAG, "handleFolderCaseAudioSummary(), localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()
            for (folder in localFolders) {
                if (folder.name.contains(cloudFolder.mFolderName) && folder.name.length > cloudFolder.mFolderName.length) {
                    folderNames.add(folder.name)
                }
            }
            var index = 0
            for (name in folderNames) {
                val suffix = name.substring(cloudFolder.mFolderName.length)
                kotlin.runCatching {
                    index = max(index, suffix.toInt())
                }
            }
            val newFolderName = cloudFolder.mFolderName + (index + 1)
            AppLogger.CLOUD.d(
                TAG,
                "handleFolderCaseAudioSummary(), newFolderName: ${newFolderName.base64()}"
            )
            FolderUtil.insertFolderNameSync(
                mContext,
                newFolderName,
                cloudFolder.mFolderGuid,
                Device.getDeviceIMEI(mContext),
                cloudFolder.mCreateTime,
                FolderInfo.FOLDER_STATE_MODIFIED,
                cloudFolder.mEncrypted,
                null,
                cloudFolder.mSysVersion
            )
            return true
        } else if (FolderInfo.FOLDER_GUID_AUDIO_SUMMARY == cloudFolder.mFolderGuid) {

            /**
             * 针对文章摘要笔记本，
             * 如果云端为真笔记本，本地为假笔记本，则需要将真笔记本保留同时将假笔记本改为”文章摘要X"
             * 将云端真笔记本保留即可
             */
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.CLOUD.d(TAG, "handleFolderCaseAudioSummary2, localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()
            for (folder in localFolders) {
                if (folder.name.contains(cloudFolder.mFolderName) && folder.name.length > cloudFolder.mFolderName.length) {
                    folderNames.add(folder.name)
                }
            }
            var index = 0
            for (name in folderNames) {
                val suffix = name.substring(cloudFolder.mFolderName.length)
                kotlin.runCatching {
                    index = max(index, suffix.toInt())
                }
            }
            val newFolderName = cloudFolder.mFolderName + (index + 1)
            AppLogger.CLOUD.d(
                TAG,
                "handleFolderCaseAudioSummary2, newFolderName: ${newFolderName.base64()}"
            )

            localFolder.name = newFolderName
            AppDatabase.getInstance().foldersDao().updateFolder(localFolder)
            handleFolderCase5(cloudFolder)
            return true
        }
        return false
    }


    /**
     * 极端场景：云端存在非0000的全部笔记
     *
     * @param cloudFolder 云端文件夹信息
     * @param localFolder 本地文件夹信息
     * @return
     */
    @Suppress("LongMethod")
    private fun handlerFolderPreset(
        cloudFolder: FolderBean,
        localFolder: Folder
    ): Boolean {
        AppLogger.CLOUD.d(
            TAG, "handlerFolderPreset(), cloudFolder: $cloudFolder, localFolder: $localFolder"
        )
        if (cloudFolder.mFolderName == mContext.resources.getString(R.string.memo_all_notes) &&
            FolderInfo.FOLDER_GUID_NO_GUID != cloudFolder.mFolderGuid) {
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.CLOUD.d(TAG, "handlerFolderPreset(), localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()
            for (folder in localFolders) {
                if (folder.name.contains(cloudFolder.mFolderName) && folder.name.length > cloudFolder.mFolderName.length) {
                    folderNames.add(folder.name)
                }
            }
            var index = 0
            for (name in folderNames) {
                val suffix = name.substring(cloudFolder.mFolderName.length)
                kotlin.runCatching {
                    index = max(index, suffix.toInt())
                }
            }
            val newFolderName = cloudFolder.mFolderName + (index + 1)
            AppLogger.CLOUD.d(
                TAG,
                "handlerFolderPreset(), newFolderName: ${newFolderName.base64()}"
            )
            FolderUtil.insertFolderNameSync(
                mContext,
                newFolderName,
                cloudFolder.mFolderGuid,
                Device.getDeviceIMEI(mContext),
                cloudFolder.mCreateTime,
                FolderInfo.FOLDER_STATE_MODIFIED,
                cloudFolder.mEncrypted,
                null,
                cloudFolder.mSysVersion
            )
            return true
        } else if (FolderInfo.FOLDER_GUID_NO_GUID == cloudFolder.mFolderGuid) {

            /**
             * 针对预制全部笔记本
             * 如果云端为真笔记本，本地为假笔记本，则需要将真笔记本保留同时将假笔记本改为”全部笔记X"
             * 将云端真笔记本保留即可
             */
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.CLOUD.d(TAG, "handlerFolderPreset2, localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()
            for (folder in localFolders) {
                if (folder.name.contains(cloudFolder.mFolderName) && folder.name.length > cloudFolder.mFolderName.length) {
                    folderNames.add(folder.name)
                }
            }
            var index = 0
            for (name in folderNames) {
                val suffix = name.substring(cloudFolder.mFolderName.length)
                kotlin.runCatching {
                    index = max(index, suffix.toInt())
                }
            }
            val newFolderName = cloudFolder.mFolderName + (index + 1)
            AppLogger.CLOUD.d(
                TAG,
                "handlerFolderPreset2, newFolderName: ${newFolderName.base64()}"
            )

            localFolder.name = newFolderName
            AppDatabase.getInstance().foldersDao().updateFolder(localFolder)
            handleFolderCase5(cloudFolder)
            return true
        }
        return false
    }


    /**
     * case 5: local folders not in cloud folder list(has cloud new folders)
     */
    private fun handleFolderCase5(cloudFolder: FolderBean) {
        AppLogger.CLOUD.d(TAG, "handleFolderCase5(), cloudNewFolder: $cloudFolder")
        if (TextUtils.isEmpty(cloudFolder.mFolderName)) {
            AppLogger.CLOUD.d(TAG, "handleFolderCase5(), name empty.")
            return
        }
        val folder = Folder()
        folder.name = cloudFolder.mFolderName
        folder.guid = cloudFolder.mFolderGuid
        folder.modifyDevice = cloudFolder.mModifyDevice
        folder.createTime = Date(cloudFolder.mCreateTime)
        folder.modifyTime = Date(cloudFolder.mModifyTime)
        folder.state = FolderInfo.FOLDER_STATE_UNCHANGE
        folder.encrypted = cloudFolder.mEncrypted
        folder.encryptedPre = cloudFolder.mEncrypted
        folder.extra = FolderExtra.create(cloudFolder.mExtra)
        if (cloudFolder.mSysRecordType == RECORD_TYPE_FOLDER) {
            folder.sysVersion = cloudFolder.mSysVersion
        } else {
            folder.encryptSysVersion = cloudFolder.mSysVersion
        }
        FolderUtil.insertFolderRecovery(folder)
    }

    /**
     * case 5: 加密表新增。旧表删除
     * @param cloudFolder 表新增
     * @param remove 表删除
     */
    private fun combineHandleFolderCase5(
        cloudFolder: FolderBean,
        remove: FolderBean
    ) {
        AppLogger.CLOUD.d(TAG, "combineHandleFolderCase5(), cloudNewFolder: $cloudFolder")
        if (TextUtils.isEmpty(cloudFolder.mFolderName)) {
            AppLogger.CLOUD.d(TAG, "handleFolderCase5(), name empty.")
            return
        }
        val folder = Folder()
        folder.name = cloudFolder.mFolderName
        folder.guid = cloudFolder.mFolderGuid
        folder.modifyDevice = cloudFolder.mModifyDevice
        folder.createTime = Date(cloudFolder.mCreateTime)
        folder.modifyTime = Date(cloudFolder.mModifyTime)
        folder.state = FolderInfo.FOLDER_STATE_UNCHANGE
        folder.encrypted = cloudFolder.mEncrypted
        folder.encryptedPre = cloudFolder.mEncrypted
        folder.extra = FolderExtra.create(cloudFolder.mExtra)
        folder.sysVersion = listOf(cloudFolder, remove).find { it.mSysRecordType == RECORD_TYPE_FOLDER }?.mSysVersion
                ?: 0L
        folder.encryptSysVersion = listOf(cloudFolder, remove).find { it.mSysRecordType == RECORD_TYPE_ENCRYPT_FOLDER }?.mSysVersion
                ?: 0L
        FolderUtil.insertFolderRecovery(folder)
    }


    /**
     * case 6: cloud folders not in local folders list(has local new folders; cloud deleted some folders)
     */
    private fun handleFolderCase6(deletedFolders: List<String>) {
        AppLogger.CLOUD.d(TAG, "handleFolderCase6(), deletedFolders: $deletedFolders")
        FolderUtil.deleteFoldersSyncForRichNote(mContext, deletedFolders, false, false, true)
    }


    private fun findLocalFolderWithSameGuid(
        folderList: List<Folder>,
        targetGuid: String
    ): Folder? {
        for (folder in folderList) {
            if (TextUtils.equals(folder.guid, targetGuid)) {
                return folder
            }
        }
        return null
    }

    private fun findLocalFolderWithSameName(
        folderList: List<Folder>,
        targetName: String
    ): Folder? {
        for (folder in folderList) {
            if (TextUtils.equals(folder.name, targetName)) {
                return folder
            }
        }
        return null
    }

    fun onRecoveryEnd(backUp: () -> Unit) {
        edfHelper?.apply {
            onRecoveryEnd(backUp)
        } ?: run {
            AppLogger.BASIC.e(TAG, "error:edfManager this:${this@FolderMerger} -- edf null")
        }
    }

    fun forceMerge() {
        edfHelper?.resetCache()
        onRecoveryEnd {  }
    }

    fun initEncryptDecryptFolderManager() {
        edfHelper = EncryptDecryptFolderHelper()
        edfHelper?.init()
        foldersEncryptStateChange.clear()
    }

    /**
     * 缓存要改Encrpted_pre字段的folder修改
     */
    fun setFolderEncryptedChanged(
        isPending: Boolean = true,
        changedData: List<Triple<Int, String, Long>>?
    ) {
        AppLogger.CLOUD.d(TAG, "setFolderEncryptedChanged pending:$isPending, changeData:${changedData?.size}")
        if (isPending) {
            if (changedData.isNullOrEmpty()) {
                AppLogger.CLOUD.d(TAG, "no changedData")
            } else {
                foldersEncryptStateChange.addAll(changedData)
            }
        } else {
            AppLogger.BASIC.d(TAG, "setFolderEncryptedChanged $foldersEncryptStateChange")
            foldersEncryptStateChange.filter {
                it.first == FolderInfo.FOLDER_ENCRYPTED
            }.map {
                Pair(it.second, it.third)
            }.let {
                AppDatabase.getInstance().foldersDao().updateFoldersEncryptPreSysVersion(it)
            }

            foldersEncryptStateChange.filter {
                it.first == FolderInfo.FOLDER_UNENCRYPTED
            }.map {
                Pair(it.second, it.third)
            }.let {
                AppDatabase.getInstance().foldersDao().updateFoldersEncryptPreEncryptSysVersion(it)
            }

            foldersEncryptStateChange.clear()
        }
    }

    companion object {
        private const val TAG = "FolderMerger"
    }
}
