/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - CallContentTips.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/1/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  nieyong       2024/1/17     1.0     create file
 ****************************************************************/
package com.oplus.note.view.bubbletips

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.graphics.Rect
import android.os.Bundle
import android.view.View
import com.coui.appcompat.tooltips.COUIToolTips
import com.nearme.note.util.fixCOUIToolTipsLeaks
import com.oplus.note.logger.AppLogger
import com.oplus.note.view.CallContentTip

class CallContentTipsManager {
    private var guideView: View? = null
    private var toolTips: CallContentTip? = null

    companion object {
        private val managerTipHashMap = HashMap<Int, CallContentTipsManager>()
        private const val TAG = "CallContentTipsManager"

        fun instance(activity: Activity?): CallContentTipsManager {
            if (activity == null) {
                return CallContentTipsManager()
            }
            val code = activity.hashCode()
            return managerTipHashMap[code].let {
                if (it == null) {
                    val manager = CallContentTipsManager()
                    manager.registerLifecycle(activity)
                    managerTipHashMap[code] = manager
                    manager
                } else {
                    it
                }
            }
        }
    }

    fun isShowing(): Boolean {
        return guideView != null && guideView?.visibility == View.VISIBLE && toolTips?.isShowing == true
    }

    @SuppressLint("NewApi")
    private fun registerLifecycle(activity: Activity) {
        activity.registerActivityLifecycleCallbacks(object : Application.ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            }

            override fun onActivityStarted(activity: Activity) {
            }

            override fun onActivityResumed(activity: Activity) {
            }

            override fun onActivityPaused(activity: Activity) {
            }

            override fun onActivityStopped(activity: Activity) {
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
            }

            override fun onActivityDestroyed(activity: Activity) {
                managerTipHashMap.remove(activity.hashCode())
                guideView = null
                toolTips?.dismiss()
                toolTips = null
                activity.unregisterActivityLifecycleCallbacks(this)
            }
        })
    }

    @Suppress("MagicNumber")
    fun showCallContentGuideTips(view: View, rect: Rect, content: String, closeCallback: (() -> Unit)?) {
        toolTips?.dismissImmediately()
        val offsetX = (rect.left + rect.width() / 2) - view.width / 2
        AppLogger.BASIC.d(TAG, "showCallContentGuideTips offsetX=$offsetX,rect=$rect")
        showBubbleTipWithFork(view, offsetX, rect.top, text = content, haIndicator = true, closeCallback = closeCallback)
    }

    /**
     * 有叉叉的气泡展示方法
     */
    @Suppress("LongParameterList")
    private fun showBubbleTipWithFork(
        view: View,
        offsetX: Int = 0,
        offsetY: Int = 0,
        direction: Int = COUIToolTips.DEFAULT_ALIGN_DIRECTION,
        text: String = "",
        haIndicator: Boolean = false,
        closeCallback: (() -> Unit)? = null
    ) {
        val tips = createCOUIToolTips(view).apply {
            setDismissOnTouchOutside(false)
            setContent(text)
            setOnCloseIconClickListener {
                //必须点击x或者用户点击了引导内容才能取消引导消失提示
                closeCallback?.invoke()
                dismissTips()
            }
        }
        tips.showWithDirection(
            view,
            direction,
            haIndicator,
            offsetX,
            offsetY,
            true
        )
        toolTips = tips
        guideView = view
    }

    fun dismissTips() {
        toolTips?.apply {
            if (isShowing) {
                AppLogger.BASIC.d(TAG, "dismissTips")
                toolTips?.dismissImmediately()
            }
        }
    }

    private fun createCOUIToolTips(targetView: View): CallContentTip {
        val couiToolTips = CallContentTip(targetView)
        fixCOUIToolTipsLeaks(targetView, couiToolTips)
        return couiToolTips
    }
}