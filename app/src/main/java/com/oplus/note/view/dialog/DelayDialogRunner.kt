/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: DelayDialogRunner.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/03/20
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.view.dialog

import android.app.Dialog
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.nearme.note.util.DialogUtils
import com.nearme.note.util.startRotatingAnimation
import com.oplus.note.logger.AppLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 显示隐藏具有最少显示时长要求的对话框，该对话框具体有以下特性：
 * 1、延时指定时间显示
 * 2、若在延时时间内调用[dismiss]方法，则不再触发对话框显示逻辑，直接执行[dismiss]后续逻辑
 * 3、若对话框显示，则至少显示[atLeast]时长后，才会消失。
 *
 * @param delay the delay time to show dialog.
 * @param atLeast the least amount of time to show dialog.
 */
class DelayDialogRunner(
    private val builder: COUIAlertDialogBuilder,
    private val delay: Long = DEFAULT_DELAY,
    private val atLeast: Long = DEFAULT_AT_LEAST,
    private val owner: LifecycleOwner
) {

    companion object {
        private const val TAG = "DelayDialogRunner"

        const val DEFAULT_DELAY = 1000L
        const val DEFAULT_AT_LEAST = 1000L
    }

    private var startTimeOfRealShow = 0L
    private var jobOfDelayShow: Job? = null
    private var alreadyDismiss = false
    private var dialog: Dialog? = null

    fun show(loadingText: String?) {
        if (loadingText.isNullOrEmpty()) {
            return
        }
        alreadyDismiss = false
        jobOfDelayShow = owner.lifecycleScope.launch {
            AppLogger.BASIC.d(TAG, "show: delay = $delay, atLeast = $atLeast")
            delay(delay)
            withContext(Dispatchers.Main) {
                AppLogger.BASIC.d(TAG, "show")
                startTimeOfRealShow = System.currentTimeMillis()
                dialog = builder.show()
                dialog?.startRotatingAnimation(loadingText) // try to start a rotation animation.
            }
        }
    }

    /**
     * @param block will invoke  on main thread after dismiss.
     */
    fun dismiss(immediately: Boolean = false, block: (() -> Unit)? = null) {
        owner.lifecycleScope.launch {
            if (immediately) {
                AppLogger.BASIC.d(TAG, "dismiss immediately.")
                jobOfDelayShow?.cancel()
                if (!alreadyDismiss) {
                    dismissInternal(block)
                }
                return@launch
            }

            val shouldDelay = atLeast - (System.currentTimeMillis() - startTimeOfRealShow)
            if (shouldDelay <= 0) {
                jobOfDelayShow?.cancel()
                if (!alreadyDismiss) {
                    dismissInternal(block)
                }
            } else {
                delay(shouldDelay)
                if (!alreadyDismiss) {
                    dismissInternal(block)
                }
            }
        }
    }

    private suspend fun dismissInternal(block: (() -> Unit)? = null) {
        alreadyDismiss = true
        withContext(Dispatchers.Main) {
            AppLogger.BASIC.d(TAG, "dismiss")
            DialogUtils.safeDismissDialog(dialog)
            block?.invoke()
        }
        dialog = null
    }

    fun isShowing(): Boolean {
        return dialog?.isShowing == true
    }
}