/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File:          SamNoteAnalyzerTest.kt
 * * Description:   SamNoteAnalyzerTest
 * * Version:       1.0
 * * Date :         2024/6/19
 * * Author:        LiDongHang
 * * ---------------------Revision History: ---------------------
 * *  <author>     <date>       <version >   <desc>
 * *  LiDongHang   2024/6/19     1.0          build this module
 ****************************************************************/
package com.oplus.migrate.backuprestore.plugin.third.analyze.sam

import com.oplus.migrate.backuprestore.plugin.third.analyze.hr.HtmlConverter
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.spyk
import io.mockk.verify
import org.junit.Ignore
import org.junit.Test

class SamNoteAnalyzerTest {

    @Test
    @Ignore
    fun should_convert_to_data_when_analyze() {
        val analyzer = spyk(SamNoteAnalyzer(), recordPrivateCalls = true)
        val converter = mockk<HtmlConverter> {
            every { convertToData(any(), any(), any()) } just runs
        }
        every { analyzer.getProperty("converter") } returns converter

        analyzer.analyze(mockk(), mockk(), "test.html")

        verify {
            converter.convertToData(any(), any(), any())
        }
    }
}

