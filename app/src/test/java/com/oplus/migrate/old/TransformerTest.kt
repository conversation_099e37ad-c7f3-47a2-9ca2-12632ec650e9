/**
 *  Copyright (C), 2020-2030 Oplus. All rights reserved..
 *  VENDOR_EDIT
 *  File: TransformerTest
 *  Description:
 *  Version: v1.0
 *  Date: 2023/6/21 15:44
 *  Author: W9013024
 *  History:NA
 *  ---------------------- Revision History:----------------------
 *  <author> <date> <version> <desc>
 * */
package com.oplus.migrate.old

import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [28])
class TransformerTest {

    @Before
    fun setUp() {
        mockkStatic(Transformer::class)
    }

    @Test
    fun should_execute_convertRichNoteContent() {
        //given
        val text = Transformer.convertRichNoteContent("note")

        //then
        Assert.assertNotNull(text)
    }

    @Test
    fun should_execute_calculationAndReplaceItemRange() {
        //given
        val list: MutableList<ItemRange> = ArrayList()
        list.add(ItemRange("note", 0, 2))

        //when
        val result = Whitebox.invokeMethod<Any>(
            Transformer,
            "calculationAndReplaceItemRange",
            StringBuilder(),
            list
        )

        //then
        Assert.assertTrue(list.size > 0)
    }

    @Test
    fun should_execute_calculationItemRange() {
        //given
        val list: MutableList<ItemRange> = ArrayList()
        list.add(ItemRange("note", 0, 2))

        //when
        val result =
            Whitebox.invokeMethod<Any>(Transformer, "calculationItemRange", StringBuilder(), list)

        //then
        Assert.assertTrue(list.size > 0)
    }

    @Test
    fun should_return_not_null_when_removeCheckBoxChar() {
        //given
        val s = "&#3"
        val content = StringBuilder("note")
        content.append(s)

        //when
        val result = Whitebox.invokeMethod<Any>(Transformer, "removeCheckBoxChar", content)

        //then
        Assert.assertNotNull(result)
    }


    @Test
    fun should_return_not_null_when_createImageSource() {
        //given
        val content = "note"
        val s = "img"

        //when
        val result = Whitebox.invokeMethod<Any>(Transformer, "createImageSource", content)

        //then
        Assert.assertTrue(result.toString().contains(s))
    }

    @Test
    fun should_return_not_null_when_parseAttachmentFromRichContent() {
        //given
        val content = "note"

        //when
        val result =
            Whitebox.invokeMethod<Any>(Transformer, "parseAttachmentFromRichContent", content)

        //then
        Assert.assertNotNull(result)
    }

    @Test
    fun should_execute_addDivTag() {
        //given
        val list: MutableList<ImageRange> = ArrayList()
        list.add(ImageRange("note", 0, 2))
        val content = StringBuilder()
        val s = "div"

        //when
        val result = Whitebox.invokeMethod<Any>(Transformer, "addDivTag", content, list)

        //then
        Assert.assertTrue(content.toString().contains(s))
    }

    @Test
    fun should_return_not_null_when_calculationImageRange() {
        //given
        val content = StringBuilder()
        val s = "<note>"
        content.append(s)
        //when
        val result = Whitebox.invokeMethod<Any>(Transformer, "calculationImageRange", content)

        //then
        Assert.assertNotNull(result)
    }

    @Test
    fun should_return_not_null_when_calculationCheckRange() {
        //given
        val list: MutableList<ItemRange> = ArrayList()
        list.add(ItemRange("note", 0, 2))
        val content = StringBuilder()
        val s = "<note>-&#2"
        val s1 = "li"
        content.append(s)
        //when
        val result =
            Whitebox.invokeMethod<Any>(Transformer, "calculationCheckRange", content, false, list)

        //then
        Assert.assertNotNull(content.toString().contains(s1))
    }

    @Test
    fun should_execute_calculationCheckRange() {
        //given
        val list: MutableList<CheckRange> = ArrayList()
        list.add(CheckRange("note", false, 0, 2))
        val content = StringBuilder()
        val s = "<note>"
        content.append(s)
        //when
        val result = Whitebox.invokeMethod<Any>(
            Transformer,
            "calculationAndReplaceCheckRange",
            content,
            list
        )
        //then
        Assert.assertTrue(list.size > 0)
    }

    @Test
    fun should_execute_replaceCheckRangeList() {
        //given
        val list: MutableList<CheckRange> = ArrayList()
        list.add(CheckRange("note", false, 0, 2))
        val content = StringBuilder()
        val s = "<note>"
        content.append(s)
        //when
        val result = Whitebox.invokeMethod<Any>(Transformer, "replaceCheckRangeList", content, list)

        //then
        Assert.assertTrue(list.isEmpty())
    }

    @Test
    fun should_execute_replaceItemRangeList() {
        //given
        val list: MutableList<ItemRange> = ArrayList()
        list.add(ItemRange("note", 0, 2))
        val content = StringBuilder()
        val s = "<note>"
        content.append(s)
        //when
        val result = Whitebox.invokeMethod<Any>(Transformer, "replaceItemRangeList", content, list)

        //then
        Assert.assertTrue(list.isEmpty())
    }

    @After
    fun tearDown() {
        unmockkStatic(Transformer::class)
    }
}