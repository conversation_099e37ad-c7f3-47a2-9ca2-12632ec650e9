/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: IOplusSplitScreenObserver.kt
 * Description: The fake class for IOplusSplitScreenObserver to do unit test
 *
 *
 * Version: 1.0
 * Date: 2023-06-28
 * Author: ********
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * ********                       2023-06-28    1.0    Create this module
 **********************************************************************************/

package com.oplus.app

import android.os.Binder
import android.os.Bundle
import android.os.IBinder
import android.os.IInterface
import android.os.Parcel

interface IOplusSplitScreenObserver : IInterface {
    fun onStateChanged(var1: String?, var2: Bundle?)

    abstract class Stub : Binder(), IOplusSplitScreenObserver {
        override fun asBinder(): IBinder? {
            return null
        }

        public override fun onTransact(var1: Int, var2: Parcel, var3: Parcel?, var4: Int): Boolean {
            return false
        }

        companion object {
            private val NULL_VALUE = null
            private const val BOOLEAN_VALUE = false
            fun asInterface(var0: IBinder?): IOplusSplitScreenObserver? {
                return NULL_VALUE
            }

            fun setDefaultImpl(var0: IOplusSplitScreenObserver?): Boolean {
                return BOOLEAN_VALUE
            }

            val defaultImpl: IOplusSplitScreenObserver?
                get() = NULL_VALUE
        }
    }

    class Default : IOplusSplitScreenObserver {
        override fun onStateChanged(var1: String?, var2: Bundle?) {
            //do nothing
        }

        override fun asBinder(): IBinder? {
            return null
        }
    }
}