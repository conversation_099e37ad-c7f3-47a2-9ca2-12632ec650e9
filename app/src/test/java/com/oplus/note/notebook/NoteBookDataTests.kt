/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: NoteBookDataTests.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/08/08
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook

import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oplus.note.TestEnvironments
import com.oplus.note.notebook.internal.NoteBookData
import com.oplus.note.os.OsConfigurations
import io.mockk.every
import io.mockk.mockkObject
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [TestEnvironments.ROBOLECTRIC_CONFIG_SDK_LEVEL])
class NoteBookDataTests {

    @Test
    fun should_success_when_init() {
        // given
        mockkObject(OsConfigurations)
        every { OsConfigurations.isOneplusExport() } returns true

        // when
        val list = NoteBookData.imageList
        val oldList = NoteBookData.oldList

        // then
        Assert.assertEquals(25, list.size)
    }

    @Ignore("need fix")
    @Test
    fun should_success_when_init_with_oneplus_export() {
        // given
        mockkObject(OsConfigurations)
        every { OsConfigurations.isOneplusExport() } returns false

        // when
        val list = NoteBookData.imageList
        val oldList = NoteBookData.oldList

        // then
        Assert.assertEquals(25, list.size)
        Assert.assertEquals(11, oldList.size)
    }

    @Test
    fun should_return_false_when_cover_null() {
        // given
        mockkObject(NoteBookData)

        // when
        val cover = null

        // then
        Assert.assertFalse(NoteBookData.isOldCover(cover))
    }

    @Test
    fun should_return_ture_when_cover() {
        // given
        mockkObject(NoteBookData)

        // when
        val cover = "img_cover_default"

        // then
        Assert.assertTrue(NoteBookData.isOldCover(cover))
    }
}