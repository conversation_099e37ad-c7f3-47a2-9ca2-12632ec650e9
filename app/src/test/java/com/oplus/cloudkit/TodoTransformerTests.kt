/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: TodoTransformerTests.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/6/1
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudkit

import com.oplus.note.repo.todo.entity.ToDo
import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import com.oplus.cloudkit.transformer.TodoTransformer
import org.junit.Assert
import org.junit.Test
import java.util.Date
import java.util.UUID

class TodoTransformerTests {

    @Test
    fun should_none_with_invalid_todo_record() {
        // given
        val transformer = TodoTransformer()
        val record = CloudMetaDataRecordProxy()

        // when
        val todo = transformer.convertToToDoFrom(record)

        // then
        Assert.assertNull(todo)
    }

    @Test
    fun should_success_when_convert_to_record() {
        // given
        val transformer = TodoTransformer()
        val toDo = ToDo().apply {
            localId = UUID.randomUUID()
            globalId = UUID.randomUUID()
            createTime = Date()
            updateTime = Date()
            setIsDelete(false)
        }

        // when
        val record = transformer.convertToRecordFrom(toDo)

        // then
        Assert.assertNotNull(record)
    }

    @Test
    fun should_success_when_convert_to_todo() {
        // given
        val metadata = "{\"alarmTime\":0,\"createTime\":1654082391728,\"finishTime\":0,\"itemId\":\"c415813b-e2c1-44f6-aa27-f976628caf57\",\"status\":0,\"updateTime\":1654082391728}"
        val transformer = TodoTransformer()
        val record = CloudMetaDataRecordProxy().apply {
            fields = metadata
        }

        // when
        val todo = transformer.convertToToDoFrom(record)

        // then
        Assert.assertNull(todo)
    }

}