/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SettingSyncTests.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/4/26
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudkit

import com.nearme.note.db.FolderSyncSwitchManager
import com.oplus.cloudkit.lib.CloudBackupResponseErrorProxy
import com.oplus.cloudkit.lib.CloudBackupResponseRecordProxy
import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import com.oplus.cloudkit.metadata.SettingMetaData
import com.oplus.cloudkit.transformer.SettingTransformer
import com.oplus.note.test.callOriginal
import com.oplus.note.test.relaxMockk
import com.oplus.note.test.verifyNot
import com.oplus.note.utils.SharedPreferencesUtil
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Test

class SettingSyncTests {

    @Test
    fun `should invoke backup when onRecoveryEnd`() {
        // Given
        val sync = relaxMockk<SettingSyncManager>()
        callOriginal { sync.onRecoveryEnd(any()) }
        // When
        var flag = 1
        sync.onRecoveryEnd {
            flag = 0
        }
        // Then
        assert(flag == 0)
    }

    @Test
    fun `should return 1 when getMetaDataCount `() {
        // Given
        val sync = relaxMockk<SettingSyncManager>()
        callOriginal { sync.getMetaDataCount() }
        // When
        val count = sync.getMetaDataCount()
        // Then
        assert(count == 1)
    }

    @Test
    fun `should write shared preference when onPagingRecoveryEnd`() {
        // Given
        val sp = relaxMockk<SharedPreferencesUtil>()
        mockkStatic(SharedPreferencesUtil::getInstance)
        every { SharedPreferencesUtil.getInstance() } returns sp
        val settingData = relaxMockk<SettingMetaData> {
            every { modeFlag } returns 1
            every { folderSyncSwitch } returns SharedPreferencesUtil.DEFAULT_VALUE_STRING
        }
        val mockTrans = relaxMockk<SettingTransformer> {
            every { convertToSettingsFrom(any()) } returns settingData
        }
        val sync = relaxMockk<SettingSyncManager> {
            every { transformer } returns mockTrans
            callOriginal { onPagingRecoveryEnd(any()) }
            every { context } returns relaxMockk()
        }
        val proxy = relaxMockk<CloudMetaDataRecordProxy> {
            every { sysRecordId } returns "1234"
        }
        mockkObject(FolderSyncSwitchManager)
        every { FolderSyncSwitchManager.getLocalRememberSyncFolderState() } returns ""
        val data = listOf(proxy, proxy)
        // When
        sync.onPagingRecoveryEnd(data)
        // Then
        verify {
            SharedPreferencesUtil.getInstance()
            sp.putString(any(), any(), any(), any())
            proxy.sysRecordId
            sp.putLong(any(), any(), any(), any())
            mockTrans.convertToSettingsFrom(any())
            FolderSyncSwitchManager.getLocalRememberSyncFolderState()
        }
        unmockkObject(FolderSyncSwitchManager)
        unmockkStatic(SharedPreferencesUtil::getInstance)
    }

    @Test
    fun `should read shared preference when onQueryDirtyData`() {
        // Given
        mockkStatic(SharedPreferencesUtil::getInstance)
        val mockTrans = relaxMockk<SettingTransformer> {
            every { convertToRecordFrom(any(), any(), any(), any()) } returns relaxMockk()
        }
        val sync = relaxMockk<SettingSyncManager> {
            every { transformer } returns mockTrans
        }
        callOriginal { sync.onQueryDirtyData() }
        val sp = relaxMockk<SharedPreferencesUtil> {
            every { getInt(any(), any(), any()) } returns 1
        }
        every { SharedPreferencesUtil.getInstance() } returns sp
        mockkObject(FolderSyncSwitchManager)
        every { FolderSyncSwitchManager.getRememberSyncFolderStateForBackup() } returns "test"
        mockkStatic(CloudKitSdkManager::getAccountUserId)
        every { CloudKitSdkManager.getAccountUserId() } returns "*********"
        // When
        sync.onQueryDirtyData()
        // Then
        verify {
            SharedPreferencesUtil.getInstance()
            sp.getInt(any(), any(), any())
            FolderSyncSwitchManager.getRememberSyncFolderStateForBackup()
            CloudKitSdkManager.getAccountUserId()
            mockTrans.convertToRecordFrom(any(), any(), any(), any())
        }
        unmockkStatic(CloudKitSdkManager::getAccountUserId)
        unmockkObject(FolderSyncSwitchManager)
        unmockkStatic(SharedPreferencesUtil::getInstance)
    }

    @Test
    fun `should read shared preference  when onPagingBackupEnd`() {
        // Given

        val success = listOf<CloudBackupResponseRecordProxy>(relaxMockk())
        val error = listOf<CloudBackupResponseErrorProxy>(relaxMockk())
        val sync = relaxMockk<SettingSyncManager> {
            callOriginal { onPagingBackupEnd(any(), any()) }
        }
        val sp = relaxMockk<SharedPreferencesUtil> {
            every { getInt(any(), any(), any()) } returns 1
        }
        mockkStatic(SharedPreferencesUtil::getInstance)
        every { SharedPreferencesUtil.getInstance() } returns sp
        // When
        sync.onPagingBackupEnd(success, error)
        // Then
        verify {
            SharedPreferencesUtil.getInstance()
            sp.getInt(any(), any(), any())
        }
        unmockkStatic(SharedPreferencesUtil::getInstance)
    }

    @Test
    fun `should return when onRecoverFolderSyncSwitch`(): Unit =
        mockkObject(FolderSyncSwitchManager) {
            // Given
            val merger = relaxMockk<SettingSyncManager.FoldSyncSwitchMerger> {
                callOriginal { onRecoverFolderSyncSwitch(any(), any()) }
            }
            justRun { FolderSyncSwitchManager.setRememberSyncFolderSyncState(any()) }
            // When
            merger.onRecoverFolderSyncSwitch("test", "test")
            // Then
            verifyNot {
                FolderSyncSwitchManager.setRememberSyncFolderSyncState(any())
            }
        }
}