package com.nearme.note.util

import io.mockk.mockk
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.util.*

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [28])
class DateUtilTest {

    @Test
    fun `should return Date when getSpecialTimeInMills with type`() {
        var date = DateUtil.getSpecialTimeInMills(DateUtil.TYPE_TODAY_0)
        Assert.assertNotNull(date)
        date = DateUtil.getSpecialTimeInMills(DateUtil.TYPE_TOMORROW_0)
        Assert.assertNotNull(date)
        date = DateUtil.getSpecialTimeInMills(DateUtil.TYPE_TOMORROW_24)
        Assert.assertNotNull(date)
    }

    @Test
    fun `should return boolean when areDateEquals`() {
        var result = DateUtil.areDateEquals(null, null)
        Assert.assertTrue(result)
        val oldDte = mockk<Date>()
        result = DateUtil.areDateEquals(oldDte, null)
        Assert.assertFalse(result)
        val newDate = oldDte
        result = DateUtil.areDateEquals(oldDte, newDate)
        Assert.assertTrue(result)
    }
}