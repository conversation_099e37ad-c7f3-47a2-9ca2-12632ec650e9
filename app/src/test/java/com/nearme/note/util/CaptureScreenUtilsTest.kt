package com.nearme.note.util

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.view.View
import android.widget.ListView
import com.oplus.note.R
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import junit.framework.Assert.assertEquals
import org.junit.Assert.*
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.locks.Condition
import java.util.concurrent.locks.ReentrantLock


@RunWith(RobolectricTestRunner::class)
@Config(sdk = [28])

class CaptureScreenUtilsTest {

    val mContext = RuntimeEnvironment.getApplication().applicationContext

    @Test
    fun `should return sSmoothRunnable getSmoothRunnable`() {
        CaptureScreenUtils.getSmoothRunnable()
        assertEquals(CaptureScreenUtils.getSmoothRunnable(), CaptureScreenUtils.sSmoothRunnable)
    }

    @Test
    fun `should return null resetSmoothRunnable`() {
        CaptureScreenUtils.resetSmoothRunnable()
        assertNull(CaptureScreenUtils.sSmoothRunnable)
    }

    @Test
    fun `should resetCaptureFiled`() {
        CaptureScreenUtils.wholeListHeight = AtomicInteger()
        CaptureScreenUtils.sSaveDoneCount = AtomicInteger()
        CaptureScreenUtils.resetCaptureFiled()
        assertEquals(CaptureScreenUtils.sCurrentIndex, 0)
        assertEquals(CaptureScreenUtils.wholeListHeight.get(), 0)
        assertEquals(CaptureScreenUtils.sAppendItemCount, 0)
        assertEquals(CaptureScreenUtils.sSaveDoneCount.get(), 0)
        assertEquals(CaptureScreenUtils.sBitmapPool, null)
    }

    @Test
    fun `should recoverListPosition`() {
        val listView = mockk<ListView>(relaxed = true, relaxUnitFun = true)
        CaptureScreenUtils.recoverListPosition(listView, 7, 7)
        val handler = android.os.Handler()
        handler.postDelayed(Runnable {
            verify() {
                listView.setSelection(7)
                listView.postDelayed(any(), any())
                listView.scrollListBy(any())
            }
        }, 300)
    }

    @Test
    fun `should return itemBitmap when cropLastTextIfNeed with cropHeight`() {
        val bitmap = BitmapFactory.decodeResource(mContext.resources, R.drawable.note_color_menu_ic_add_disabled)
        val result = CaptureScreenUtils.cropLastTextIfNeed(bitmap, 0)
        assertEquals(result, bitmap)

        val bitmapResult = CaptureScreenUtils.cropLastTextIfNeed(bitmap, 1)
        assertNotNull(bitmapResult)

    }

    @Test
    fun `should getCacheBitmap`() {
        val bitmapPool = ConcurrentHashMap<Int, WeakReference<Bitmap>>()
        CaptureScreenUtils.sBitmapPool = bitmapPool
        val result = CaptureScreenUtils.getCacheBitmap(10, 10)
        assertNotNull(result)
    }

    @Test
    fun `should signalCacheBitmap`() {
        val condition = mockk<Condition>(relaxUnitFun = true) {
            justRun { signal() }
        }
        val reentrantLock = mockk<ReentrantLock>(relaxUnitFun = true) {
            justRun {
                lock()
                unlock()
            }
        }
        CaptureScreenUtils.sReentrantLock = reentrantLock
        CaptureScreenUtils.sCondition = condition
        CaptureScreenUtils.signalCacheBitmap()
        verify {
            reentrantLock.lock()
            condition.signal()
            reentrantLock.unlock()
        }
    }

    @Test
    fun `should return boolean when enableBitmapPool with int`() {
        val heightRatio = 11
        val result = CaptureScreenUtils.enableBitmapPool(heightRatio)
        assertTrue(result)
    }

    @Test
    fun `should awaitCacheBitmap`() {
        val condition = mockk<Condition>(relaxUnitFun = true) {
            justRun { await() }
        }
        val reentrantLock = mockk<ReentrantLock>(relaxUnitFun = true) {
            justRun {
                lock()
                unlock()
            }
        }
        CaptureScreenUtils.sReentrantLock = reentrantLock
        CaptureScreenUtils.sCondition = condition
        CaptureScreenUtils.awaitCacheBitmap()
        verify {
            reentrantLock.lock()
            condition.await()
            reentrantLock.unlock()
        }
    }

    @Test
    fun `should getViewDrawingCache`() {
        val childView = mockk<View>(relaxed = true, relaxUnitFun = true) {
            justRun {
                measure(any(), any())
                layout(any(), any(), any(), any())
                setDrawingCacheEnabled(any())
                buildDrawingCache()
            }
        }
        CaptureScreenUtils.getViewDrawingCache(childView, 1)
        verify {
            childView.measure(any(), any())
            childView.layout(any(), any(), any(), any())
            childView.setDrawingCacheEnabled(any())
            childView.buildDrawingCache()
        }
    }

    @Test
    fun `should resetListView`() {
        val listView = mockk<ListView> {
            justRun { setSelection(any()) }
        }
        CaptureScreenUtils.resetListView(listView)
        verify {
            // 1. scroll to top
            listView.setSelection(any())
        }
    }

    @Test
    fun `should captureCurrentScreen`() {
        val result = CaptureScreenUtils.captureCurrentScreen(null)
        assertNull(result)
        val view = mockk<View>(relaxed = true, relaxUnitFun = true) {
            justRun {
                setDrawingCacheEnabled(any())
                buildDrawingCache()
                getDrawingCache()
                setDrawingCacheEnabled(any())
            }
        }
        CaptureScreenUtils.captureCurrentScreen(view)
        verify {
            view.setDrawingCacheEnabled(any())
            view.buildDrawingCache()
            view.getDrawingCache()
            view.setDrawingCacheEnabled(any())
        }
    }

    @Test
    fun `should relayoutChildView`() {
        val view = mockk<View>(relaxed = true, relaxUnitFun = true) {
            justRun {
                measure(any(), any())
                layout(any(), any(), any(), any())
            }
        }
        CaptureScreenUtils.relayoutChildView(view, 1)
        verify {
            view.measure(any(), any())
            view.layout(any(), any(), any(), any())
        }
    }

    @Test
    fun `should return itemBitmap when getBitmap`() {
        val itemBitmap = CaptureScreenUtils.getBitmap(7, 7, 7)
        assertNotNull(itemBitmap)
    }

    @Test
    fun `shoule result when getCaptureFilePath with Path`() {
        val result = CaptureScreenUtils.getCaptureFilePath("asdasda", 1)
        assertEquals("asdasdacapture1.png", result)
    }

    @Test
    fun `should return bitmap when getBitmap`() {
        val itemBitmap = CaptureScreenUtils.getBitmap(7, 7, 10)
        assertNotNull(itemBitmap)
    }

    @Test
    fun `should return path when getCapturePath`() {
        val path = CaptureScreenUtils.getCapturePath()
        assertNotNull(path)
    }

    @Test
    fun `should return bitmap when createListBitmap with List`() {
        val bitmapOne = BitmapFactory.decodeResource(mContext.resources, R.drawable.note_color_menu_ic_add_disabled)
        val bitmapTwo = BitmapFactory.decodeResource(mContext.resources, R.drawable.note_color_menu_ic_add_disabled)
        val bitmaps = ArrayList<Bitmap>()
        bitmaps.add(bitmapOne)
        bitmaps.add(bitmapTwo)

        val result = CaptureScreenUtils.createListBitmap(bitmaps, 10, 10)

        assertNotNull(result)
    }

    @Test
    fun `should clip largeItem when clipLargeItem with childView`() {
        val childView = mockk<View>(relaxed = true, relaxUnitFun = true){
            every { measuredHeight } returns 2
            every { measuredWidth } returns 2
        }
        CaptureScreenUtils.sCurrentIndex = 1

        CaptureScreenUtils.clipLargeItem(childView, 1)
    }
}