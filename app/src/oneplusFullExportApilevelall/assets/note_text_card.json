{"type": "constraint", "layout_width": "match_parent", "layout_height": "match_parent", "id": "parent_con", "forceDarkAllowed": "false", "background": "@drawable/note_card_background", "package": "com.oneplus.note", "child": [{"type": "constraint", "layout_width": "match_parent", "layout_height": "0", "layout_constraintTop_toBottomOf": "con_title_bar", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintVertical_bias": 1, "layout_marginTop": 8, "layout_marginStart": 16, "layout_marginEnd": 16, "layout_marginBottom": 16, "visibility": "gone", "id": "con_normal", "child": [{"type": "text", "layout_width": "match_parent", "layout_height": "0", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintVertical_chainStyle": "spread_inside", "layout_marginStart": 0, "layout_marginEnd": 0, "layout_marginTop": 0, "layout_marginBottom": 0, "id": "tv_normal_title", "textFontWeight": "500", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_12", "textColor": "@color/widget_card_title", "textAlignment": "viewStart"}, {"type": "text", "layout_width": "match_parent", "layout_height": 0, "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toBottomOf": "tv_normal_title", "layout_constraintBottom_toTopOf": "tv_normal_date", "layout_constraintVertical_chainStyle": "spread_inside", "layout_marginStart": 0, "layout_marginEnd": 0, "layout_marginTop": 2, "layout_marginBottom": 2, "textFontWeight": "400", "lineSpacingMultiplier": "1.1", "id": "tv_normal_content", "ellipsize": "end", "textSize": "@dimen/dp_12", "textColor": "@color/widget_card_content", "layout_constraintVertical_bias": 1, "textAlignment": "viewStart", "maxLines": 3}, {"type": "text", "layout_width": "match_parent", "layout_height": "0", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintVertical_chainStyle": "spread_inside", "layout_marginStart": 0, "layout_marginEnd": 0, "layout_marginTop": 0, "layout_marginBottom": 0, "textFontWeight": "500", "id": "tv_normal_date", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_12", "textColor": "@color/widget_card_date", "textAlignment": "viewStart"}]}, {"type": "constraint", "layout_width": "match_parent", "layout_height": "0", "layout_constraintTop_toBottomOf": "con_title_bar", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginTop": 8, "layout_marginStart": 16, "layout_marginEnd": 16, "layout_marginBottom": 16, "visibility": "gone", "id": "con_title_only", "child": [{"type": "text", "layout_width": "match_parent", "layout_height": "0", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toTopOf": "tv_title_only_date", "layout_constraintVertical_chainStyle": "spread_inside", "layout_marginStart": 0, "layout_marginEnd": 0, "layout_marginTop": 0, "layout_marginBottom": 2, "id": "tv_title_only_title", "textFontWeight": "500", "lineSpacingMultiplier": "1.1", "maxLines": 4, "ellipsize": "end", "textSize": "@dimen/dp_12", "textColor": "@color/widget_card_title", "textAlignment": "viewStart"}, {"type": "text", "layout_width": "match_parent", "layout_height": "0", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintVertical_chainStyle": "spread_inside", "layout_marginStart": 0, "layout_marginEnd": 0, "layout_marginTop": 0, "layout_marginBottom": 0, "textFontWeight": "500", "id": "tv_title_only_date", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_12", "textColor": "@color/widget_card_date", "textAlignment": "viewStart"}]}, {"visibility": "gone", "type": "constraint", "layout_width": "match_parent", "layout_height": "0", "layout_constraintTop_toBottomOf": "con_title_bar", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginTop": 8, "layout_marginStart": 16, "layout_marginEnd": 16, "layout_marginBottom": 16, "id": "con_fill", "child": [{"type": "text", "layout_width": "match_parent", "layout_height": 0, "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toTopOf": "tv_fill_date", "layout_constraintVertical_chainStyle": "spread_inside", "layout_marginStart": 0, "layout_marginEnd": 0, "layout_marginTop": 0, "layout_marginBottom": 2, "id": "tv_fill_content", "textFontWeight": "400", "lineSpacingMultiplier": "1.1", "maxLines": 4, "ellipsize": "end", "textSize": "@dimen/dp_12", "textColor": "@color/widget_card_content", "textAlignment": "viewStart"}, {"type": "text", "layout_width": "match_parent", "layout_height": "0", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintVertical_chainStyle": "spread_inside", "layout_marginStart": 0, "layout_marginEnd": 0, "layout_marginTop": 0, "layout_marginBottom": 0, "textFontWeight": "500", "id": "tv_fill_date", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_12", "textColor": "@color/widget_card_date", "textAlignment": "viewStart"}]}, {"type": "constraint", "layout_width": "match_parent", "layout_height": "0", "layout_constraintTop_toBottomOf": "con_title_bar", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginTop": 8, "layout_marginStart": 16, "layout_marginEnd": 16, "layout_marginBottom": 16, "layout_constraintVertical_chainStyle": "spread", "id": "con_pic", "visibility": "gone", "child": [{"type": "text", "layout_width": "match_parent", "layout_height": "0", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintVertical_chainStyle": "spread", "layout_marginStart": 0, "layout_marginEnd": 0, "layout_marginTop": 0, "layout_marginBottom": 0, "id": "tv_pic_title", "textFontWeight": "500", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_12", "textColor": "@color/widget_card_title", "textAlignment": "viewStart"}, {"type": "image", "layout_width": "76", "layout_height": "42", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toBottomOf": "tv_pic_title", "layout_constraintBottom_toTopOf": "tv_pic_date", "layout_constraintVertical_chainStyle": "spread", "layout_marginStart": 0, "layout_marginEnd": 0, "layout_marginTop": 5, "layout_marginBottom": 2, "scaleType": "centerCrop", "id": "tv_pic_content", "cornerRadius": 6}, {"type": "image", "layout_width": "62", "layout_height": "36", "visibility": "gone", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toBottomOf": "tv_pic_title", "layout_constraintBottom_toTopOf": "tv_pic_date", "layout_constraintVertical_chainStyle": "spread", "layout_marginStart": 0, "layout_marginEnd": 0, "layout_marginTop": 5, "layout_marginBottom": 2, "scaleType": "centerCrop", "id": "tv_pic_content_fold", "cornerRadius": 6}, {"type": "text", "layout_width": "match_parent", "layout_height": "0", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintVertical_chainStyle": "spread", "layout_marginStart": 0, "layout_marginEnd": 0, "layout_marginTop": 0, "layout_marginBottom": 0, "textFontWeight": "500", "id": "tv_pic_date", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_12", "textColor": "@color/widget_card_date", "textAlignment": "viewStart"}]}, {"type": "constraint", "layout_width": "match_parent", "layout_height": "0", "layout_constraintTop_toBottomOf": "con_title_bar", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginTop": 8, "layout_marginStart": 16, "layout_marginEnd": 16, "layout_marginBottom": 16, "layout_constraintVertical_chainStyle": "spread", "id": "con_loading", "visibility": "gone", "child": [{"type": "text", "layout_width": "83", "layout_height": "18", "layout_constraintBottom_toTopOf": "load_content_1", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintVertical_chainStyle": "spread_inside", "id": "load_title", "background": "@drawable/widget_card_load"}, {"type": "text", "layout_width": "116", "layout_height": "16", "layout_constraintBottom_toTopOf": "load_content_2", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toBottomOf": "load_title", "layout_constraintVertical_chainStyle": "spread_inside", "layout_marginTop": 5, "id": "load_content_1", "background": "@drawable/widget_card_load"}, {"type": "text", "layout_width": "116", "layout_height": "16", "layout_constraintBottom_toTopOf": "load_date", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toBottomOf": "load_content_1", "layout_constraintVertical_chainStyle": "spread_inside", "id": "load_content_2", "layout_marginTop": 5, "background": "@drawable/widget_card_load"}, {"type": "text", "layout_width": "50", "layout_height": "14", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toBottomOf": "load_content_2", "layout_constraintVertical_chainStyle": "spread_inside", "layout_marginTop": 8, "id": "load_date", "background": "@drawable/widget_card_load"}]}, {"type": "constraint", "layout_width": "match_parent", "layout_height": "0", "layout_constraintTop_toBottomOf": "con_title_bar", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "id": "con_empty", "visibility": "gone", "child": [{"type": "text", "layout_width": "match_parent", "layout_height": "wrap_content", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_marginTop": 9, "id": "tv_empty", "maxLines": 1, "ellipsize": "end", "fontFamily": "sans-serif-medium", "textSize": "@dimen/dp_12", "textColor": "@color/widget_card_tips", "text": "@string/memo_sync_toast_no_note", "gravity": "center"}]}, {"visibility": "gone", "type": "constraint", "layout_width": "match_parent", "layout_height": "match_parent", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_marginStart": 16, "layout_marginEnd": 16, "layout_marginBottom": 16, "id": "con_note_agree_privacy", "child": [{"type": "text", "layout_width": "match_parent", "layout_height": "match_parent", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintBottom_toTopOf": "btn_goto_agree", "id": "tv_view_privacy_middle", "textSize": "@dimen/dp_12", "maxLines": 2, "ellipsize": "end", "textFontWeight": "500", "text": "@string/agree_statement_use_note", "textColor": "@color/widget_card_tips", "textAlignment": "center", "gravity": "center"}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "background": "@drawable/view_privacy", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "text": "@string/go_agree", "textSize": "@dimen/dp_12", "textFontWeight": "500", "textColor": "@color/coui_color_white", "id": "btn_goto_agree", "scaleType": "fitXY", "textAlignment": "center", "gravity": "center", "onClick": [{"type": "activity", "packageName": "com.oneplus.note", "action": "action.nearme.note.allnote", "flag": "67108864|268435456", "params": {"action_from": "app_note_card"}}]}]}, {"visibility": "gone", "type": "constraint", "layout_width": "match_parent", "layout_height": "match_parent", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_marginStart": 16, "layout_marginEnd": 16, "layout_marginBottom": 16, "id": "con_select_notebook", "child": [{"type": "text", "layout_width": "match_parent", "layout_height": "match_parent", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintBottom_toTopOf": "btn_goto_select", "textSize": "@dimen/dp_12", "maxLines": 2, "ellipsize": "end", "textFontWeight": "500", "text": "@string/click_select_notebook", "textColor": "@color/widget_card_tips", "textAlignment": "center", "gravity": "center"}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "background": "@drawable/view_privacy", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "text": "@string/select", "textSize": "@dimen/dp_12", "textFontWeight": "500", "textColor": "@color/coui_color_white", "id": "btn_goto_select", "textAlignment": "center", "gravity": "center"}]}, {"visibility": "visible", "type": "constraint", "layout_width": "match_parent", "layout_height": "wrap_content", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "id": "con_title_bar", "child": [{"type": "image", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_marginTop": 14, "layout_marginStart": 16, "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "src": "@drawable/note_card_notebook_icon", "id": "img_notebook_icon", "contentDescription": "@string/note_notebook"}, {"type": "text", "layout_width": "0", "layout_height": "wrap_content", "layout_constraintBottom_toBottomOf": "img_notebook_icon", "layout_constraintTop_toTopOf": "img_notebook_icon", "layout_constraintStart_toEndOf": "img_notebook_icon", "layout_constraintEnd_toStartOf": "notebook_img_add_parent", "layout_marginEnd": 16, "id": "tv_notebook_title", "textFontWeight": "600", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_14", "textColor": "?attr/couiColorPrimary", "textAlignment": "viewStart"}, {"visibility": "visible", "type": "constraint", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "paddingTop": 12, "paddingEnd": 12, "layout_constraintStart_toEndOf": "tv_notebook_title", "id": "notebook_img_add_parent", "child": [{"type": "image", "layout_width": "wrap_content", "layout_height": "wrap_content", "background": "@drawable/todo_card_add", "layout_constraintEnd_toEndOf": "parent", "id": "note_img_add", "contentDescription": "@string/add_note"}]}]}]}