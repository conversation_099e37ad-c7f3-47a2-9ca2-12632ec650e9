/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - CloudMetaDataFileInfoProxy.java
** Description:
** Version: 1.0
** Date : 2022/8/15
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  AAA       2022/8/15      1.0     create file
****************************************************************/
package com.oplus.cloudkit.lib;

import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataFileInfo;
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord;

import java.util.ArrayList;
import java.util.List;

public class CloudMetaDataFileInfoProxy {

    public static List<CloudMetaDataFileInfo> toList(List<CloudMetaDataFileInfoProxy> oriList) {
        ArrayList<CloudMetaDataFileInfo> list = new ArrayList<>(oriList.size());
        for (CloudMetaDataFileInfoProxy item: oriList){
            list.add(item.mMetaDataFileInfo);
        }
        return list;
    }

    private CloudMetaDataFileInfo mMetaDataFileInfo;

    public CloudMetaDataFileInfoProxy(String ocloudId,String fileCheckPayload) {
        mMetaDataFileInfo = new CloudMetaDataFileInfo(ocloudId, fileCheckPayload);
    }

    public String getOcloudId() {
        return mMetaDataFileInfo.getOcloudId();
    }

    public void setOcloudId(String ocloudId) {
        mMetaDataFileInfo.setOcloudId(ocloudId);
    }

    public String getFileCheckPayload() {
        return mMetaDataFileInfo.getFileCheckPayload();
    }

    public void setFileCheckPayload(String fileCheckPayload) {
        mMetaDataFileInfo.setFileCheckPayload(fileCheckPayload);
    }

    @Override
    public String toString() {
        return mMetaDataFileInfo.toString();
    }
}
