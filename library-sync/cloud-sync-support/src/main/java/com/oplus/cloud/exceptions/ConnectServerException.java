package com.oplus.cloud.exceptions;

import java.io.IOException;

public class ConnectServerException extends IOException {
    private static final long serialVersionUID = 1L;

    public static final int PUBLIC_KEY_ERROR = 222;
    public static final int BAD_REQUEST = 400;
    public static final int FORBIDDEN = 403;
    public static final int NOT_FOUND = 404;
    public static final int NOT_ACCEPTABLE = 406;
    public static final int PARAM_ERROR = 444;
    public static final int SERVER_ERROR = 500;
    public static final int RSP_BODY_ERROR = 600;
    public static final int UNDEFINED = 0;

    private int mType = UNDEFINED;

    public ConnectServerException() {}

    public ConnectServerException(String detailMessage, Throwable throwable) {
        super(detailMessage, throwable);
    }

    public ConnectServerException(int type) {
        super();
        mType = type;
    }

    public ConnectServerException(int type, Throwable throwable) {
        super(throwable);
        mType = type;
    }

    public ConnectServerException(int type, String detailMessage) {
        super(detailMessage);
        mType = type;
    }

    public ConnectServerException(String detailMessage) {
        super(detailMessage);
    }

    public ConnectServerException(Throwable throwable) {
        super(throwable);
    }

    public int getType() {
        return mType;
    }
}
