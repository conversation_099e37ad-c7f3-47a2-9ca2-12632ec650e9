/**
 * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * File           : InputContent.kt
 * Description    : InputContent.kt
 * Version        : 1.0
 * Date           : 2024/6/6
 * Author         : Chandler
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * Chandler     2024/6/6         1.0           create
 */
package com.oplus.notes.webviewcoverpaint.container.api

import androidx.annotation.Keep

@Keep
data class InputContent(
    private val text: String = "",
    private val isFinal: Boolean = true,
    private val keepSelection: Boolean = false,
    private val insertToEnd: Boolean = false,
    private val focusedEditor: Int = TiptapFocusInfo.UNKNOWN_FOCUSED,
    private val isDragDrop: Boolean = false
) {
    override fun toString(): String {
        return "{ length: ${text.length}, isFinal: $isFinal, keepSelection: $keepSelection, insertToEnd: $insertToEnd, focusedEditor:$focusedEditor }"
    }
}