package com.oplus.notes.webviewcoverpaint.container.api

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.annotation.ColorInt
import androidx.annotation.StyleRes
import com.heytap.tbl.webkit.WebView
import com.oplus.note.repo.note.entity.CardAttr
import com.oplus.note.view.WVScrollbarView
import com.oplus.notes.webviewcoverpaint.container.WVSearchData
import com.oplus.notes.webviewcoverpaint.container.api.touchevent.IWVDispatchTouchEventListener
import com.oplus.notes.webviewcoverpaint.container.api.touchevent.IWVInterceptStylusTouchEventListener
import com.oplus.notes.webviewcoverpaint.container.api.touchevent.IWVTouchEventListener
import com.oplus.notes.webviewcoverpaint.data.BasicCssParams
import com.oplus.notes.webviewcoverpaint.data.CaptureElementInfo
import com.oplus.notes.webviewcoverpaint.data.SkinCssParams
import com.oplus.notes.webviewcoverpaint.data.TiptapContentInfo
import com.oplus.notes.webviewcoverpaint.data.clipboard.PasteResult

interface IWebViewContainerCoverpaint {
    fun webViewSupported(): Boolean
    fun isUsingTBLWebView(): Boolean
    fun createWebView(
        context: Context,
        @StyleRes themeResId: Int,
        container: ViewGroup,
        layoutParams: ViewGroup.LayoutParams?,
        backGroundView: View?
    ): WebView?

    fun isInitContent(): Boolean
    /**
     *  @param url Url of the webview
     *  @param javascriptInterface Interfaces for calls from JS without callback.
     *  @return
     */
    fun loadUrl(url: String, javascriptInterface: IMyJavascriptInterface? = null)

    fun setJsInterfaceConsumer(consumer: IMyJavascriptInterface?)

    fun addScrollChangedListener(listener: IScrollChangedListener?)
    fun removeScrollChangedListener(listener: IScrollChangedListener?)

    /**
     *  WebView请求获得焦点
     */
    fun requestFocus()

    /**
     * 设置是否支持垂直滚动条
     */
    fun setScrollbarView(scrollbarView: WVScrollbarView?)
    /**
     * 刷新scrollbar颜色模式
     * 0-> 跟随亮暗色模式变化
     * >0 固定亮色
     * <0 固定暗色
     */
    fun refreshScrollbarColor(mode: Int)
    fun setPaintScrollScale(scale: Float)
    fun hasJavascriptMethod(name: String, cb: CallJSResponse)
    fun getTextAndHtml(cb: CallJSResponse)
    fun getSelectText(cb: CallJSResponse)
    fun initContent(contentInfo: TiptapContentInfo, cb: CallJSResponse? = null)
    fun updateContent(contentInfo: TiptapContentInfo, cb: CallJSResponse? = null)
    fun setText(
        text: InputContent,
        cb: CallJSResponse? = null
    )

    fun updateContentMargin(margin: Int, cb: ((String) -> Unit)?)

    fun replaceAllContent(text: String, isFinal: Boolean = true, cb: ((String) -> Unit)? = null)
    fun clearContent(cb: CallJSResponse? = null)

    /**
     *  清除编辑器中的光标
     */
    fun clearFocus(cb: CallJSResponse? = null)
    fun focus(position: String = "", focusedEditor: Int = TiptapFocusInfo.UNKNOWN_FOCUSED, cb: CallJSResponse? = null)
    fun setBold(bold: Boolean, cb: CallJSResponse? = null)
    fun setItalic(italic: Boolean, cb: CallJSResponse? = null)
    fun setUnderline(underline: Boolean, cb: CallJSResponse? = null)
    fun setStrikethrough(strikethrough: Boolean, cb: CallJSResponse? = null)
    fun setFontSize(size: Int, cb: CallJSResponse? = null)
    fun setBulletList(bullet: Boolean, cb: CallJSResponse? = null)
    fun setBulletListHX(bullet: Boolean, cb: CallJSResponse? = null)
    fun setOrderedList(ordered: Boolean, cb: CallJSResponse? = null)
    fun setTaskList(task: Boolean, cb: CallJSResponse? = null)
    fun setTextAlign(align: String, cb: CallJSResponse? = null, history: Boolean = true)
    fun insertImage(
        imageInfo: ImageInfo,
        addToPrevGroup: Boolean = false,
        insertToEndInNonEditMode: Boolean = true,
        cb: CallJSResponse? = null
    )
    fun notifyImageSrcExist(attachId: String, cb: CallJSResponse? = null)

    fun updateImage(oldAttachId: String?, imageInfo: ImageInfo, cb: CallJSResponse? = null)

    fun updateCard(cardAttr: CardAttr, oldAttachId: String?, cb: ((String) -> Unit)? = null)

    fun replaceNodeByCard(oldAttachId: String, cardAttr: CardAttr, cb: CallJSResponse? = null)

    fun replaceNodeByImage(oldAttachId: String?, imageInfo: ImageInfo, cb: CallJSResponse? = null)

    @Suppress("LongParameterList")
    fun replaceNodeByPaint(
        oldAttachId: String?,
        paintId: String,
        paintSrc: String,
        imageId: String,
        imageSrc: String,
        imageWidth: Int,
        imageHeight: Int,
        tips: String?,
        needShowTips: Boolean,
        cb: CallJSResponse? = null
    )

    fun replaceNodeByDocument(
        oldAttachId: String,
        fileCard: FileCardData,
        addToPrevGroup: Boolean,
        cb: CallJSResponse? = null
    )

    fun replaceNodeByVideo(
        oldAttachId: String,
        videoData: VideoData,
        addToPrevGroup: Boolean,
        cb: CallJSResponse? = null
    )

    fun replaceNodeByAudio(
        oldAttachId: String,
        recordAttr: RecordAttr,
        addToPrevGroup: Boolean,
        cb: CallJSResponse? = null
    )
    fun replaceAttachmentByText(attachId: String, text: String, cb: CallJSResponse? = null)
    fun focusCoordinate(x: Int, y: Int, textNodeRestricted: Boolean = false)
    fun updateContentHeight(newHeight: Int, cb: CallJSResponse? = null)

    //    fun getContentHeight(cb: CallJSResponse)
    fun onDestroy()
    fun scrollIntoView(cb: CallJSResponse? = null)
    fun getNodeRectByCoords(left: Int, top: Int, cb: CallJSResponse? = null)
    fun getImageRectBySrc(src: String, cb: CallJSResponse? = null)
    fun getRecordDetailRect(attachId: String, cb: CallJSResponse? = null)
    fun getRecordCardRect(attachId: String, cb: CallJSResponse? = null)
    fun scrollBy(interval: Int, cb: CallJSResponse? = null)
    fun undo(cb: CallJSResponse? = null)
    fun redo(cb: CallJSResponse? = null)
    fun setBackgroundColor(@ColorInt color: Int, cb: CallJSResponse? = null)
    fun unsetBackgroundColor(cb: CallJSResponse? = null)

    /**
     *  获取默认的屏幕密度与当前屏幕密度的比例
     */
    fun setDensityScale(scale: Float, cb: CallJSResponse? = null)
    fun onTipTapMounted()
    fun setTextSelection(fromPos: Int, toPos: Int, cb: CallJSResponse? = null)
    fun setTextSelectionAll(cb: CallJSResponse? = null)
    fun cancelTextSelectionAll(cb: CallJSResponse? = null)
    fun setUiMode(mode: Int, cb: CallJSResponse? = null)
    fun updateRecordState(attachId: String, state: Int, cb: CallJSResponse? = null)
    fun updateRecordHasCallLogs(attachId: String, hasCallLogs: Boolean, cb: CallJSResponse? = null)
    fun updateRecord(oldAttachId: String, src: String, attachId: String, recordId: String, cb: CallJSResponse? = null)
    fun setRecordCurrentTime(attachId: String, time: Long, totalDuration: Long, cb: CallJSResponse? = null)

    /**
     * 搜索文本集合+附件+滚动
     * */
    fun search(searchData: WVSearchData, cb: CallJSResponse? = null)
    fun matchPrevious(cb: CallJSResponse? = null)
    fun matchNext(cb: CallJSResponse? = null)
    fun clearSearchResult(cb: CallJSResponse? = null)
    fun smoothScrollTo(dx: Int, dy: Int, duration: Int)
    fun deleteNodeByAttachId(attachId: String, cb: CallJSResponse? = null)
    fun insertCard(
        cardJson: CardAttr,
        addToPrevGroup: Boolean = false,
        insertToEndInNonEditMode: Boolean = true,
        pasteInsert: Boolean = false,
        cb: CallJSResponse? = null
    )
    fun insertContactCard(cardJson: ContactCardAttr, addToPrevGroup: Boolean = false, cb: CallJSResponse? = null)
    fun insertScheduleCard(cardJson: ScheduleCardAttr, addToPrevGroup: Boolean = false, cb: CallJSResponse? = null)
    fun insertRecordCard(
        recordAttr: RecordAttr,
        addToPrevGroup: Boolean = false,
        insertToEndInNonEditMode: Boolean = true,
        cb: CallJSResponse? = null
    )
    fun insertFileCard(
        fileCard: FileCardData,
        addToPrevGroup: Boolean = false,
        insertToEndInNonEditMode: Boolean = true,
        cb: CallJSResponse? = null
    )
    fun setSummaryEntity(entities: String, marks: String, cb: CallJSResponse? = null)
    fun setEditorScale(scale: Float, cb: CallJSResponse? = null)

    /**
     *  获取WebView的Scale。
     *  值来自CoverDoodlePresenter.setScaleInit中计算的Scale
     *  这个Scale并不会作用于WebView的scaleX和scaleY，但会作用于html中的rem单位。
     *  1rem = 16px * Scale * screen density
     */
    fun getEditorScale(): Float

    @Suppress("LongParameterList")
    fun insertPaint(paintAttr: PaintAttr, addToPrevGroup: Boolean = false, insertToEndInNonEditMode: Boolean = true, cb: CallJSResponse? = null)

    fun updatePaint(originPaintId: String, newPaintAttr: PaintAttr, cb: CallJSResponse? = null)

    fun deletePaint(paintId: String, cb: CallJSResponse? = null)

    fun updateOverlayPaintHeight(height: Int, cb: CallJSResponse? = null)

    fun insertHintText(text: String, cb: CallJSResponse? = null)
    fun deleteHintText(cb: CallJSResponse? = null)

    /**
     *  Insert hint text from phone call
     *  @param text hint text
     */
    fun insertPhoneHintText(text: String, cb: CallJSResponse? = null)

    fun onGlobalDragEnter(cb: CallJSResponse? = null)
    fun onGlobalDragEnd(cb: CallJSResponse? = null)
    fun loadImageTipsLottieAnimation(attachId: String, cb: CallJSResponse? = null)
    fun destroyImageTipsLottieAnimation(attachId: String, cb: CallJSResponse? = null)
    fun setInterceptTouchEventListener(listener: IWVTouchEventListener?)
    fun setDispatchTouchEventListener(listener: IWVDispatchTouchEventListener?)
    fun setInterceptStylusTouchEventListener(listener: IWVInterceptStylusTouchEventListener?)
    fun setActionModeCallbackCreator(creator: () -> WVActionModeWrapper?)
    fun removeActionModeCallbackCreator()
    fun clearActionMode(needFinish: Boolean)
    fun isViewModeStylusTouch(): Boolean

    fun drawDoodle(cb: CallJSResponse?)

    fun getAttachments(cb: CallJSResponse?)
    fun getEditorsHeight(cb: CallJSResponse? = null)
    fun setBasicCssParams(basicCssParams: BasicCssParams, priority: Int, cb: CallJSResponse? = null)
    fun setSkinCssParams(skinCssParams: SkinCssParams, cb: CallJSResponse? = null)
    fun attachTipTapComponent(cb: CallJSResponse? = null)
    fun detachTipTapComponent(cb: CallJSResponse? = null)
    fun insertSummaryStreamTip(paras: SummaryStreamTipParams, cb: ((String) -> Unit)? = null)
    fun updateSummaryStreamTip(paras: SummaryStreamTipParams, cb: ((String) -> Unit)? = null)
    fun deleteSummaryStreamTip(cb: ((String) -> Unit)? = null)
    fun interceptWebEditorClick(isIntercept: Boolean, cb: ((String) -> Unit)? = null)
    fun captureElement(captureElementInfos: List<CaptureElementInfo>, cb: ((String) -> Unit)? = null)
    fun setOnRenderProcessGoneCb(cb: (() -> Unit)? = null)
    fun disableRecord(attachId: String, disable: Boolean, cb: ((String) -> Unit)? = null)
    fun performVibrate()
    fun enableImageAnimation(enable: Boolean, cb: ((String) -> Unit)? = null)
    fun moveAttachToSelection(type: String, attachId: String, originPos: Int, cb: ((String) -> Unit)? = null)

    /**
     * 异步获取编辑器中目标区域选中的文本
     *
     * @param editor 获取文本的目标区域。
     *              0：标题区域；
     *              1：内容区域;
     *              -1：和当前焦点所在区域一致，如果标题和正文区域都无焦点，则以正文区域为准
     * @param cb 返回选中的字符串。未选中任何内容时将返回空字符串
     */
    fun getSelectedText(editor: Int = TiptapFocusInfo.UNKNOWN_FOCUSED, cb: ((String) -> Unit)? = null)


    /**
     * 插入语音卡片之后, 使用语音修正功能
     * 选中并获取编辑器中目标区域的所有文本
     *
     * @param editor 获取文本的目标区域。
     *              0：标题区域；
     *              1：内容区域；
     *              -1：和当前焦点所在区域一致，如果标题和正文区域均无焦点，则以正文区域为准
     * @param cb 返回选中的字符串。
     *          目标区域无光标时，先选中所有文本并返回选中的文本；
     *          目标区域有光标但是未选中文本时，先选中文本并选中的文本；
     *          目标区域有光标且有选中部分文本时，直接返回选中的文本；
     *
     */
    fun selectRangeTextAIGC(from: Int, to: Int, editor: Int = TiptapFocusInfo.UNKNOWN_FOCUSED, cb: ((String) -> Unit)? = null)

    /**
     * 选中并获取编辑器中目标区域的所有文本
     *
     * @param editor 获取文本的目标区域。
     *              0：标题区域；
     *              1：内容区域；
     *              -1：和当前焦点所在区域一致，如果标题和正文区域均无焦点，则以正文区域为准
     * @param cb 返回选中的字符串。
     *          目标区域无光标时，先选中所有文本并返回选中的文本；
     *          目标区域有光标但是未选中文本时，先选中文本并选中的文本；
     *          目标区域有光标且有选中部分文本时，直接返回选中的文本；
     *
     */
    fun selectAndGetAllText(editor: Int = TiptapFocusInfo.UNKNOWN_FOCUSED, cb: ((String) -> Unit)? = null)

    /**
     * 选中并获取编辑器中目标区域光标前面的所有文本
     *
     * @param editor 获取文本的目标区域。
     *              0：标题区域；
     *              1：内容区域；
     *              -1：和当前焦点所在区域一致，如果标题和正文区域均无焦点，则以正文区域为准
     * @param cb 返回选中的字符串。
     *          目标区域无光标时，先选中所有文本并返回所有文本；
     *          目标区域有光标但是未选中文本时，先选中目标区域内光标前的所有文本并返回选中的文本；
     *          目标区域有光标且有选中文本时，直接返回已选中的文本
     */
    fun selectAndGetForwardAllText(editor: Int = TiptapFocusInfo.UNKNOWN_FOCUSED, cb: ((String) -> Unit)? = null)


    /**
     * 获取编辑器中目标区域的所有文本
     */
    fun getAllHTML(editor: Int = TiptapFocusInfo.UNKNOWN_FOCUSED, cb: ((String) -> Unit)? = null)

    /**
     * AIGC转写开始，通知webview，此时会显示“生成中”的snackbar,需要将该snackbar的高度传给webview
     * @param “生成中”的snackbar 距离屏幕底部位置
     * @param aigcOption ai改写操作 定义在[com.oplus.note.baseres.R.string.aigc_action]
     * @param isExport 是否是外销，用于Web端做流式上屏时速度限制区分
     */
    fun onAIGCRewriteStart(snackBarMargin: Int, aigcOption: String, isExport: Boolean, cb: ((String) -> Unit)? = null)

    /**
     * AIGC转写结束，通知webview
     */
    fun onAIGCRewriteFinish(manualStop: Boolean, margin: Int, cb: ((String) -> Unit)? = null)

    /**
     * 传输AIGC转写的内容给webview上屏
     * @param content AIGC转写的内容,web端拼接
     * @param isFinish 该次转写是否结束
     * @param aiHint  由 OPPO AI 产生，仅供参考 这个词条的多语言
     */
    fun onAIGCRewriteResult(
        content: String,
        isFinish: Boolean,
        aiHint: String,
        prePageText: String,
        nextPageText: String,
        cb: ((String) -> Unit)? = null
    )

    /**
     * 生成结果菜单，点击删除按钮
     */
    fun onAIGCRewriteDelete(cb: ((String) -> Unit)? = null)

    /**
     * @param subStringLength 字数超长时，需要截取内容的前 subStringLength 个字符插入
     * 生成结果菜单，点击插入当前位置按钮
     */
    fun onAIGCRewriteInsert(subStringLength: Int, cb: ((String) -> Unit)? = null)

    /**
     * 生成结果菜单，点击替换原文
     */
    fun onAIGCRewriteReplace(cb: ((String) -> Unit)? = null)

    /**
     * 将选区结束位置滚动到距离可视区域底部xx位置, contentEditor无焦点时不会滚动.
     * 如果margin值与当前选区结束位置之和大于内容总高度，则会自动对contentEditor的高度进行临时扩充。
     *
     * @param scrollToBottom 是否需要滚动。为true时会自动进行位置计算并执行高度扩充和滚动；为false时则只将高度扩充重置为0
     * @param marginBottom 滚动到距离可视区域底部的距离，单位dp。
     * @param marginExtra 滚动到距离可视区域底部的附件距离，单位dp。 滚动的总距离为 marginBottom + marginExtra
     * @param cb
     */
    fun scrollToBottom(scrollToBottom: Boolean, marginBottom: Int, marginExtra: Int, cb: ((String) -> Unit)? = null)

    fun startSpeechRecognize(cb: ((String) -> Unit)? = null)

    fun getFocusedEditor(cb: ((String) -> Unit)? = null)

    fun updateElementAttrs(elementSelectors: String, updateAttributes: Map<String, String>, cb: ((String) -> Unit)? = null)

    fun onScrollStateChanged(oldState: Int, newState: Int, cb: ((String) -> Unit)? = null)
    fun setTitleStyle(titleStyle: Boolean, type: String, cb: CallJSResponse? = null)
    fun setContentStyle(contentStyle: Boolean, cb: CallJSResponse? = null)
    fun setTextColorType(colorType: String, cb: CallJSResponse? = null)
    fun setTableColorType(tableColor: String, cb: CallJSResponse? = null)
    fun setAidTextStyle(aidTextStyle: Boolean, cb: CallJSResponse? = null)
    fun decreaseIndent(cb: CallJSResponse? = null)
    fun increaseIndent(cb: CallJSResponse? = null)
    fun setBlockQuote(set: Boolean, cb: CallJSResponse? = null)

    fun getCursorStartPos(cb: CallJSResponse? = null)
    fun ignoreActionModeInvalidate(ignore: Boolean)
    fun clearWebkitSelection()
    fun getAIGCHtmlContent(cb: CallJSResponse? = null)
    fun selectAllAIGCText(cb: CallJSResponse? = null)
    fun getSelectedAIGCHtmlContent(cb: CallJSResponse? = null)

    fun onAISummaryStartStop(isStart: Boolean)

    fun setPaintIsEmpty(isEmpty: Boolean)

    fun replaceSummaryContent(all: String, end: String, isFinal: Boolean, cb: ((String) -> Unit)? = null)

    fun hasLinkCard(cb: ((String) -> Unit)? = null)

    fun onChangeWebViewWidthScale(start: Boolean)

    /**
     * 表格相关接口
     */
    fun doInsertTable(cb: CallJSResponse? = null)
    fun addRowInTable(cb: CallJSResponse? = null)
    fun addColumnInTable(cb: CallJSResponse? = null)
    fun removeRowInTable(cb: CallJSResponse? = null)
    fun removeColumnInTable(cb: CallJSResponse? = null)
    /**
     * 交换行
     * dir < 0表示将光标处的行和前一行交换；>= 0表示将光标处的行和后一行交换
     */
    fun moveRowInTable(dir: Int, cb: CallJSResponse? = null)
    /**
     * 交换列
     * @param dir < 0表示将光标处的行和前一列交换；>= 0表示将光标处的行和后一列交换
     */
    fun moveColumnInTable(dir: Int, cb: CallJSResponse? = null)
    fun cutInTable(cb: CallJSResponse? = null)
    fun copyInTable(cb: CallJSResponse? = null)
    fun pasteInTable(pasteResult: PasteResult, cb: CallJSResponse? = null)

    fun shareTableToPicture(cb: CallJSResponse? = null)

    fun onActionItemClickedBefore(actionId: String, cb: CallJSResponse? = null)

    fun onActionItemClickedAfter(actionId: String, cb: CallJSResponse? = null)

    fun getTableFullDisplayWidth(startCapture: Boolean, cb: CallJSResponse? = null)

    fun onTableMenuDismiss(cb: CallJSResponse? = null)

    fun onTableSelectMenuDismiss(cb: CallJSResponse? = null)

    fun notifyScrollIdle(cb: CallJSResponse? = null)

    /**
     * 插入视频预览图，web端做加载动效
     */
    fun insertVideoPlaceHolder(
        videoData: VideoData,
        addToPrevGroup: Boolean = false,
        insertToEndInNonEditMode: Boolean = true,
        cb: CallJSResponse? = null
    )

    /**
     * 传入视频文件信息，web端刷新
     */
    fun updateVideo(videoData: VideoData, addToPrevGroup: Boolean = false, insertToEndInNonEditMode: Boolean, cb: CallJSResponse? = null)

    /**
     * 更新文档卡片上文档预览图
     *
     * @param attachId 附件id
     * @param docAttachId 文档附件id
     * @param thumbnail 预览图地址
     * @param cb
     */
    fun updateDocThumbnail(attachId: String, docAttachId: String, thumbnail: String, cb: ((String) -> Unit)?)
}