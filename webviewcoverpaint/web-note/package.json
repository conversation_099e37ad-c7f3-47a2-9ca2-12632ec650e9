{"name": "web-note", "version": "0.1.0", "private": true, "scripts": {"serve": "vite", "build": "npm install && npm run clean-dist-icons && run-p build-only && npm run clean-dest && npm run copy-dist", "build-only": "vite build", "test": "npm run test:unit", "test:unit": "jest", "clean-dest": "rimraf ../container-impl/src/main/assets/tiptapcoverpaint/", "clean-dist-icons": "rimraf dist/icons/", "copy-dist": "copyfiles -a -u 1 -E dist/*.html.gz dist/assets/*.css dist/assets/*.js dist/assets/*.gz dist/assets/*.webp dist/assets/*.svg dist/icons/*.svg dist/lottie/*.gz ../container-impl/src/main/assets/tiptapcoverpaint"}, "dependencies": {"prosemirror-commands": "1.5.2", "@tiptap/extension-focus": "2.1.16", "@tiptap/extension-document": "2.1.16", "@tiptap/extension-list-item": "2.1.16", "@tiptap/extension-placeholder": "2.1.16", "@tiptap/extension-text": "2.1.16", "@tiptap/extension-text-style": "2.1.16", "@tiptap/extension-heading": "2.1.16", "@tiptap/extension-paragraph": "2.1.16", "@tiptap/extension-hard-break": "2.1.16", "@tiptap/html": "2.1.16", "@tiptap/pm": "2.6.4", "@tiptap/core": "2.6.4", "@tiptap/vue-3": "^2.1.0", "@tweenjs/tween.js": "^23.1.1", "@vueuse/core": "^10.9.0", "bezier-easing": "^2.1.0", "core-js": "^3.8.3", "dom-to-image": "^2.6.0", "htmlparser2": "^9.1.0", "libphonenumber-js": "^1.10.53", "linkifyjs": "^4.1.1", "lottie-web": "^5.12.2", "scroll-into-view-if-needed": "^3.1.0", "vue": "^3.2.47", "html2canvas": "1.4.1"}, "devDependencies": {"sass": "^1.62.0", "braces": "^3.0.3", "@types/element-resize-detector": "^1.1.6", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-typescript": "^9.1.0", "@vue/tsconfig": "^0.1.3", "copyfiles": "^2.4.1", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "npm-run-all": "^4.1.5", "postcss-pxtorem": "^6.0.0", "rimraf": "^5.0.5", "sass-loader": "^13.3.2", "typescript": "~4.5.5", "unplugin-icons": "^0.17.3", "unplugin-vue-components": "^0.25.2", "vite": "^4.1.4", "vite-plugin-compression": "^0.5.1", "vue-tsc": "^1.2.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended", "@vue/typescript"], "parserOptions": {"parser": "@typescript-eslint/parser"}, "rules": {"no-case-declarations": "off", "no-extra-semi": "off", "no-constant-condition": "off", "prefer-const": "off", "no-var": "off", "no-unused-vars": "off", "no-empty": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}