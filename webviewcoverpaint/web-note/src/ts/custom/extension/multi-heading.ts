import { Extension } from '@tiptap/core'
import * as Log from '@ts/utils/Logger'
import { nodeWrapInFilterFunc } from '../helpers'
import { removeMark } from '../helpers/removeMark'


export interface HeadingOptions {
    /**
     * The types where the indent attribute can be applied.
     * @default []
     * @example ['heading', 'paragraph']
     */
    types: string[],
    levels: string[],
    HTMLAttributes: Record<string, any>,
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        multiHeading: {
            /**
             * Set a heading node
             * exmaple: setMultiHeading({ multiHeading: "1" })
             */
            setMultiHeading: (attributes: { multiHeading: string }) => ReturnType,
            /**
             * unset the heading
             */
            unsetHeading: () => ReturnType,
        }
    }
}

/**
 * This extension allows you to set text indent.
 */
export const MultiHeading = Extension.create<HeadingOptions>({
    name: 'MultiHeading',

    addOptions() {
        return {
            types: ['paragraph'],
            levels: ["1", "2", "3"],
            HTMLAttributes: {},
        }
    },

    addGlobalAttributes() {
        return [
            {
                types: this.options.types,
                attributes: {
                    multiHeading: {
                        default: null,
                        keepOnSplit: false,
                        parseHTML: element => {
                            return element.getAttribute('multi-heading')
                        },
                        renderHTML: attributes => {
                            if (attributes.multiHeading && this.options.levels.includes(attributes.multiHeading)) {
                                return { 'multi-heading': `${attributes.multiHeading}` }
                            } else {
                                attributes.multiHeading = null
                                return {}
                            }
                        },
                    },
                },
            },
        ]
    },

    addCommands() {
        return {
            setMultiHeading: (attributes) => ({ commands, state, tr, dispatch }) => {
                const level = attributes.multiHeading
                if (!this.options.levels.includes(level)) {
                    Log.e(`Tiptap`, `setMultiHeading: ${level} is out of range`, true)
                    return false
                }
                console.log(`setMultiHeading: ${level}`)
                const { from, to } = state.selection
                state.doc.nodesBetween(from, to, (node, pos) => {
                    if (nodeWrapInFilterFunc(node)) {
                        return false
                    }
                    if (this.options.types.includes(node.type.name)) {
                        removeMark(tr, node, pos, state.schema.marks.textStyle, { fontSize: null })
                        const attrs = {
                            ...node.attrs,
                            multiHeading: attributes.multiHeading
                        }
                        dispatch(
                            tr.setNodeMarkup(pos, undefined, attrs)
                        )
                        // if (commands.setNode("paragraph", attrs)) {
                        //     return false
                        // }
                    }
                })
                return true
            },
            unsetHeading: () => ({ commands, state, tr, dispatch }) => {
                console.log(`unsetHeading:`)
                const { from, to } = state.selection
                state.doc.nodesBetween(from, to, (node, pos) => {
                    if (nodeWrapInFilterFunc(node)) {
                        return false
                    }
                    if (this.options.types.includes(node.type.name)) {
                        const attrs = {
                            ...node.attrs,
                            multiHeading: null
                        }
                        dispatch(
                            tr.setNodeMarkup(pos, undefined, attrs)
                        )
                        // if (commands.setNode("paragraph", attrs)) {
                        //     return false
                        // }
                    }
                })
                return true
            },
        }

    },
})