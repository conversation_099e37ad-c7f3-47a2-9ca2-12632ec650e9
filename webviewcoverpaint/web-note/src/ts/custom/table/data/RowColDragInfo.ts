import { TableRect } from "@tiptap/pm/tables";

export class RowColDragInfo {
    touchDownX = -1;
    touchDownY = -1;
    lastTouchX = -1;
    lastTouchY = -1;
    eventTarget = null;
    attachId = null;
    tableEle: HTMLTableElement = null;
    tableWrapperRect: DOMRect = null
    tableBorderWidth: number = 1;

    rect: TableRect = null;
    columnStartIndex = 0;
    columnEndIndex = 0;
    columnDotMenuWidthL = 0;
    columnDotMenuWidthR = 0;

    rowStartIndex = 0;
    rowEndIndex = 0;
    rowDotMenuHeightT = 0;
    rowDotMenuHeightB = 0;
}