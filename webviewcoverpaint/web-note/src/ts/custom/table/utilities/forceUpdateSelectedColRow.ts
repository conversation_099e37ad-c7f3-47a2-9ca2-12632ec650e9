import { Constants } from "@/ts/utils/Constants"
import { selectedRect } from "@tiptap/pm/tables"
import { Editor } from "@tiptap/vue-3"
import { isColumnCellSelection, isRowCellSelection } from "./isCellSelection"

/**
 * 强制更新选中的列/行
 * @param editor 
 */
export function forceUpdateSelectedColRow(editor: Editor) {
    const isColCellSel = isColumnCellSelection(editor.state.selection)
    const isRowCellSel = isRowCellSelection(editor.state.selection)
    let rect
    if (isColCellSel) {
        rect = selectedRect(editor.state)
        editor.chain().selectTableColumn(rect.left, rect.right - rect.left).setMeta(Constants.META_FORCE_UPDATE_SELECTED_COL, true).blur().run()
    } else if (isRowCellSel) {
        rect = selectedRect(editor.state)
        editor.chain().selectTableRow(rect.top, rect.bottom - rect.top).setMeta(Constants.META_FORCE_UPDATE_SELECTED_ROW, true).blur().run()
    }

}