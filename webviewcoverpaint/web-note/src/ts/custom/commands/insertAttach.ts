import { Content, RawCommands } from '@tiptap/core';
import { isListNode } from '../helpers';
import { directiveCursor, afterClass } from '@ts/utils/attachCursor'
import { DEBUG_ON_WEB} from '@ts/EditorUtils'

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        insertAttach: {
            insertAttach: (attachContent: Content, scrollIntoView?: boolean, needKeyboard?:boolean) => ReturnType
        }
    }
}
/**
 * 插入附件之后光标需要显示在附件的右侧
 * needKeyboard 插入完成后, 是否进入编辑态(键盘弹起, 将光标显示在附件右侧) 传入false 文档的聚焦元素依然会是附件右侧的元素
*/
export const insertAttach: RawCommands['insertAttach'] = (attachContent, scrollIntoView = true, needKeyboard = true) => ({ state, chain }) => {
        const isImeVisible = (window as any).injectedObject && (window as any).injectedObject.isImeVisible()      
        const { from, to, $to } = state.selection;
        //附件插入位置的定义为选中结束位置，因此就算selection不为空，也不用删除任何节点。
        let insertPos = to;
        const toPosDepth = $to.depth;
        chain()
          .command(({ state, tr, commands }) => {
            if (toPosDepth > 0) {
              const toNode = $to.node(1);
              if (insertPos + (toPosDepth - 1) == $to.end(1)) {
                const node = state.doc.resolve(to).node(1)
                // 内容为空不需要添加toPosDepth(是一个空行)  直接执行insertContentAt操作
                if(node.textContent !== '') {
                  //在当前节点内容的末尾
                  insertPos += toPosDepth;
                }
              } else if (insertPos - (toPosDepth - 1) == $to.start(1)) {
                //在当前节点内容的起始位置
                insertPos -= toPosDepth;
              } else {
                if (isListNode(toNode)) {
                  if (insertPos + 1 == $to.end(2)) {
                    //在列表项末尾，将位置移动到列表项li标签后面再split
                    tr.split(insertPos + 2);
                    insertPos += toPosDepth;
                  } else if (insertPos - 1 == $to.start(2)) {
                    tr.split(insertPos - 2);
                    insertPos -= toPosDepth;
                  } else {
                    tr.split(insertPos, toPosDepth);
                    insertPos += toPosDepth;
                  }
                } else {
                  tr.split(insertPos, toPosDepth);
                  insertPos += toPosDepth;
                }
              }
            }
              return commands.insertContentAt({from: insertPos, to: insertPos}, attachContent);
          })
          .command(({ commands }) => {
            if (scrollIntoView == true) {
              setTimeout(() => {
                commands.smoothScrollTo(300, 10, window.innerHeight / 3);
              }, 0);
            }
            return true;
          })
          .run();
    // }
    // @ts-ignore
    const attachId = attachContent?.attrs?.attachid
    // debug 模式 没有attachid 或者 不需要scroll 的情况下, return
    if(DEBUG_ON_WEB || !attachId || !scrollIntoView) return
    /**
     * [attachid=bba25182-99b2-416e-a2d9-d9979bcae111]
     * [attachid=97988424-078f-4c5a-8048-2e98ec6e4411]
    */
    setTimeout(() => {
        let targetEle = null
        // 直接通过 document.querySelector('[attachid=xxx]') 的方式, 某些卡片下会报无效的选择器异常
        for(let item of document.querySelectorAll('[attachid]')) {
            if(item.getAttribute('attachid') === attachId) {
                targetEle = item
                while(!targetEle.classList.contains(directiveCursor)) {
                    targetEle = targetEle.parentElement
                    if(targetEle === document.body) return
                }

                let after = targetEle.getElementsByClassName(afterClass)[0] as HTMLDivElement
                // click事件处理函数会调起软键盘,  focus只是将文档的聚焦元素设置为after
                needKeyboard ? after.click() : void 0
                // 再没有弹起键盘的情况下, 监听window.resiz变化, 后执行附件滚动动画
                if(!isImeVisible && needKeyboard) {
                    // window.after = after
                    window.onresize = function () {
                        setTimeout(() => {
                            console.log('window.onresize', innerHeight, scrollY, targetEle.getBoundingClientRect().bottom, targetEle.getBoundingClientRect().bottom - innerHeight + 10)
                            window.scrollBy({
                                top: after.getBoundingClientRect().bottom - innerHeight + 10,
                                behavior: 'smooth'
                            })
                            window.onresize = null
                        }, 100);
                    }
                }
                return
            }
        }
    });
    return true
}

