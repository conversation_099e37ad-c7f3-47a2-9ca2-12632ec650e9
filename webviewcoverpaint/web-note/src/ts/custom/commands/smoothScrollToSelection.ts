import { RawCommands } from "@tiptap/vue-3";

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    smoothScrollTo: {
      /**
       * 平滑滚动到光标结束位置.如果光标本身就在上下边界限制范围内则不滚动，否则就滚动到上下边界限制位置
       * @param duration 滚动持续时间
       * @boundaryTop 滚动目标位置距离顶部的最小偏移量，默认值是0
       * @boundaryBottom 滚动目标位置距离底部的最小偏移量，默认值是0
       * @param interpolator 插值器
       * @returns 
       */
      smoothScrollTo: (duration: number, boundaryTop?: number, boundaryBottom?: number, interpolator?: (t: number) => number) => ReturnType;
    }
  }
}
let isScrolling = false
let scrollAnimationFrame: number | null = null
let lastTargetY: number | null = null
export const smoothScrollTo: RawCommands['smoothScrollTo'] = (duration, boundaryTop?, boundaryBottom?, interpolator?) => ({ editor }) => {
  if (isScrolling && scrollAnimationFrame !== null) {
    cancelAnimationFrame(scrollAnimationFrame)
    if (lastTargetY != null) {
      console.log(`smoothScrollTo, isScrolling. currScrollY:${window.scrollY}, lastTargetY:${lastTargetY}, `)
    }
  }

  const position = editor.view.coordsAtPos(editor.state.selection.to)
  const viewportHeight = window.innerHeight || document.documentElement.clientHeight
  const boundaryTopInner = boundaryTop || 0
  const boundaryBottomInner = boundaryBottom || 0

  let scrollOffset = 0
  if (position.top < boundaryTopInner) {
    scrollOffset = position.top - boundaryTopInner
  } else if (position.bottom > viewportHeight - boundaryBottomInner) {
    scrollOffset = position.bottom - (viewportHeight - boundaryBottomInner)
  }
  console.log(`smoothScrollTo, selection:${editor.state.selection.to}, position:${position.top}-${position.bottom}, boundary:${boundaryTopInner}-${boundaryBottomInner}, scrollOffset:${scrollOffset}, viewHeight:${viewportHeight}`)
  if (scrollOffset == 0) {
    console.log(`smoothScrollTo, no need scroll`)
    return
  }

  const targetY = scrollOffset + window.scrollY
  lastTargetY = targetY
  const start = window.scrollY
  const startTime = performance.now()

  const defaultInterpolator = (t: number) => t
  const useInterpolator = interpolator || defaultInterpolator

  isScrolling = true
  function scroll() {
    const currTime = performance.now()
    const timeElapsed = currTime - startTime
    const progress = Math.min(timeElapsed / duration, 1)
    const interpolatorProgress = useInterpolator(progress)
    window.scrollTo(0, start + (targetY - start) * interpolatorProgress)
    if (timeElapsed < duration) {
      scrollAnimationFrame = requestAnimationFrame(scroll)
    } else {
      isScrolling = false
    }
  }
  scrollAnimationFrame = requestAnimationFrame(scroll)
  return true
}