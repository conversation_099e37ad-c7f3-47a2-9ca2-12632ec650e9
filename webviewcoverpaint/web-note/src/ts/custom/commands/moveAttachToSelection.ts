import { RawCommands } from "@tiptap/vue-3"
import * as Log from '@ts/utils/Logger'
import { isAttachNode, isListNode } from "../helpers"

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        moveAttachToSelection: {
            moveAttachToSelection: (attchType: string, attachId: string, attachOriginPos: number) => ReturnType
        }
    }
}

export const moveAttachToSelection: RawCommands['moveAttachToSelection'] = (attchType, attachId, originPos) => ({ editor, state, chain }) => {
    const { from, to } = state.selection
    Log.d("TipTap", `moveAttachToSelection, origin:${originPos}, selection:${from}-${to}`)
    let toBeMovedNode
    let toBeMovedAttachPos = originPos
    state.doc.descendants((node, pos) => {
        if (!toBeMovedNode && isAttachNode(node) && node.attrs.attachid == attachId) {
            toBeMovedNode = node
            if (pos != originPos) {
                Log.i('TipTap', `moveAttachToSelection, originPos != pox! origin:${originPos}, pos:${pos}`)
            }
            toBeMovedAttachPos = pos
        }
        return false
    })
    let insertPos = to
    if (toBeMovedNode) {
        let needMove = true
        return chain().command(({ tr, state }) => {
            //1. 分割列表等节点
            const $insertPos = state.doc.resolve(insertPos)
            const insertPosDepth = $insertPos.depth;
            if (insertPosDepth > 0) {
                const toNode = $insertPos.node(1);
                if (insertPos - (insertPosDepth - 1) == $insertPos.start(1)) {
                    //在当前节点内容的起始位置
                    insertPos -= insertPosDepth;
                    if ((insertPos == toBeMovedAttachPos) || (insertPos == toBeMovedAttachPos + toBeMovedNode.nodeSize)) {
                        //位置不变，不需要移动
                        needMove = false
                        console.log(`moveAttachToSelection, no need move for node start, pos:${insertPos}`)
                        return false
                    }
                } else if (insertPos + (insertPosDepth - 1) == $insertPos.end(1)) {
                    //在当前节点内容的末尾
                    insertPos += insertPosDepth;
                    if ((insertPos == toBeMovedAttachPos) || (insertPos == toBeMovedAttachPos + toBeMovedNode.nodeSize)) {
                        //位置不变，不需要移动
                        needMove = false
                        console.log(`moveAttachToSelection, no need move for node end, pos:${insertPos}`)
                        return false
                    }
                } else {
                    if (isListNode(toNode)) {
                        if (insertPos + 1 == $insertPos.end(2)) {
                            //在列表项末尾，将位置移动到列表项li标签后面再split
                            tr.split(insertPos + 2);
                            insertPos += insertPosDepth;
                        } else if (insertPos - 1 == $insertPos.start(2)) {
                            tr.split(insertPos - 2);
                            insertPos -= insertPosDepth;
                        } else {
                            tr.split(insertPos, insertPosDepth);
                            insertPos += insertPosDepth;
                        }
                    } else {
                        tr.split(insertPos, insertPosDepth);
                        insertPos += insertPosDepth;
                    }
                }
            } 
            // else {
            //     const node = state.doc.nodeAt(Math.max(0, insertPos - 1)) //直接用insertPos去获取node，由于insertPos是to位置有可能会获取下一个节点，因此此处使用insertPos - 1
            //     if (isAttachNode(node)) {
            //         if (toBeMovedAttachPos == insertPos - node.nodeSize) {
            //             needMove = false
            //             console.log(`moveAttachToSelection, no need move for attach node. oldPos:${toBeMovedAttachPos}, insertPos:${insertPos}`)
            //             return false
            //         }
            //         if (toBeMovedAttachPos >= insertPos) {
            //             //需要移动的附件在选区附件的后面，则将该附件移动到选区附件前面，否则移动到选区附件后面
            //             insertPos -= node.nodeSize
            //         } else {
            //             insertPos += node.nodeSize
            //         }   
            //     }
            // }
            tr.insert(insertPos, toBeMovedNode)
            return true
        }).command(({ tr }) => {
            //3.删除被move的节点
            if (needMove) {
                const newPos = tr.mapping.map(toBeMovedAttachPos)
                console.log(`moveAttachToSelection, delete moved node. originPos:${toBeMovedAttachPos}, newPos:${newPos}`)
                tr.deleteRange(newPos, newPos + toBeMovedNode.nodeSize)
            }
            return true
        }).run()
    } else {
        Log.e('TipTap', `moveAttachToSelection, can't found the moveNode, type:${attchType}, id:${attachId}, pos:${originPos}, selection:${from}-${to}`)
        return false
    }
}
