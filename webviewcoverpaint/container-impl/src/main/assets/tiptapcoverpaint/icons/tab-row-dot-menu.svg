<svg width="4" height="20" viewBox="0 0 4 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<style>
    circle {
        fill: rgba(0, 0, 0, 0.26);
    }   

    @media (prefers-color-scheme: dark) {
        circle {
            fill: rgba(255, 255, 255, 0.30);
        }
    }
</style>
<circle cx="2" cy="2" r="2" transform="rotate(-180 2 2)" fill="black"/>
<circle cx="2" cy="10" r="2" transform="rotate(-180 2 10)" fill="black"/>
<circle cx="2" cy="18" r="2" transform="rotate(-180 2 18)" fill="black"/>
</svg>
