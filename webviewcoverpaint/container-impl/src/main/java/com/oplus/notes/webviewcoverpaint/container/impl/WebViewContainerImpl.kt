/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : WebViewContainerImpl.kt
 * Description    : WebViewContainerImpl.kt
 * Version        : 1.0
 * Date           : 2023/6/5
 * Author         : Chandler
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * Chandler     2023/6/5         1.0           create
 */
package com.oplus.notes.webviewcoverpaint.container.impl

import android.content.Context
import android.content.MutableContextWrapper
import android.view.ContextThemeWrapper
import android.view.View
import android.view.ViewGroup
import androidx.annotation.StyleRes
import com.heytap.tbl.webkit.WebView
import com.nearme.note.util.Injector
import com.oplus.note.logger.AppLogger
import com.oplus.notes.webviewcoverpaint.cache.api.IWebViewProxyCache
import com.oplus.notes.webviewcoverpaint.container.WVSearchData
import com.oplus.notes.webviewcoverpaint.container.api.CallJSResponse
import com.oplus.note.repo.note.entity.CardAttr
import com.oplus.notes.webviewcoverpaint.container.api.ContactCardAttr
import com.oplus.notes.webviewcoverpaint.container.api.FileCardData
import com.oplus.notes.webviewcoverpaint.container.api.IMyJavascriptInterface
import com.oplus.notes.webviewcoverpaint.container.api.IScrollChangedListener
import com.oplus.notes.webviewcoverpaint.container.api.IWebViewContainer
import com.oplus.notes.webviewcoverpaint.container.api.ImageInfo
import com.oplus.notes.webviewcoverpaint.container.api.InputContent
import com.oplus.notes.webviewcoverpaint.container.api.PaintAttr
import com.oplus.notes.webviewcoverpaint.container.api.RecordAttr
import com.oplus.notes.webviewcoverpaint.container.api.ScheduleCardAttr
import com.oplus.notes.webviewcoverpaint.container.api.SummaryStreamTipParams
import com.oplus.notes.webviewcoverpaint.container.api.WVActionModeWrapper
import com.oplus.note.view.WVScrollbarView
import com.oplus.notes.webviewcoverpaint.container.api.IWebViewContainerCoverpaint
import com.oplus.notes.webviewcoverpaint.container.api.VideoData
import com.oplus.notes.webviewcoverpaint.container.api.touchevent.IWVDispatchTouchEventListener
import com.oplus.notes.webviewcoverpaint.container.api.touchevent.IWVInterceptStylusTouchEventListener
import com.oplus.notes.webviewcoverpaint.container.api.touchevent.IWVTouchEventListener
import com.oplus.notes.webviewcoverpaint.container.web.WebViewProxy
import com.oplus.notes.webviewcoverpaint.data.BasicCssParams
import com.oplus.notes.webviewcoverpaint.data.CaptureElementInfo
import com.oplus.notes.webviewcoverpaint.data.SkinCssParams
import com.oplus.notes.webviewcoverpaint.data.TiptapContentInfo
import com.oplus.notes.webviewcoverpaint.data.clipboard.PasteResult

class WebViewContainerImpl : IWebViewContainerCoverpaint {
    companion object {
        private const val TAG = "WebViewContainerImpl"
    }
    private var webViewProxy: WebViewProxy? = null
    private val webViewProxyCache = if (IWebViewProxyCache.ENABLE_CACHE) {
        Injector.injectFactory<IWebViewProxyCache>()
    } else {
        null
    }
    override fun webViewSupported(): Boolean {
        return true
    }

    override fun isUsingTBLWebView(): Boolean {
        return webViewProxy?.webView?.isUsingTBLWebView ?: false
    }

    override fun createWebView(
        context: Context,
        @StyleRes themeResId: Int,
        container: ViewGroup,
        layoutParams: ViewGroup.LayoutParams?,
        backGroundView: View?
    ): WebView? {
        webViewProxy = if (IWebViewProxyCache.ENABLE_CACHE) {
            webViewProxyCache?.acquireWebViewProxy(context, themeResId) as? WebViewProxy
        } else {
            val contextWrapper = MutableContextWrapper(ContextThemeWrapper(context, themeResId))
            WebViewProxy.with(contextWrapper).load()
        }
        val webViewCacheWidth = webViewProxy?.webView?.measuredWidth ?: 0
        AppLogger.BASIC.d(TAG, "createWebView: webViewCacheWidth=$webViewCacheWidth, proxy=$webViewProxy")
        if (webViewCacheWidth != 0) {
            layoutParams?.width = webViewCacheWidth
            backGroundView?.layoutParams?.width = webViewCacheWidth
        }
        container.addView(webViewProxy?.webView, 0, layoutParams)
        return webViewProxy?.webView
    }

    override fun isInitContent(): Boolean {
        checkProxy()
        return webViewProxy?.isInitContent() == true
    }

    override fun loadUrl(url: String, javascriptInterface: IMyJavascriptInterface?) {
        checkProxy()
        webViewProxy?.initJsInterface("injectedObject", javascriptInterface)
        webViewProxy?.loadUrl(url)
    }

    override fun setJsInterfaceConsumer(consumer: IMyJavascriptInterface?) {
        checkProxy()
        webViewProxy?.setJsInterfaceConsumer(consumer)
    }

    override fun addScrollChangedListener(listener: IScrollChangedListener?) {
        checkProxy()
        webViewProxy?.addScrollChangedListener(listener)
    }

    override fun removeScrollChangedListener(listener: IScrollChangedListener?) {
        checkProxy()
        webViewProxy?.removeScrollChangedListener(listener)
    }

    override fun requestFocus() {
        checkProxy()
        webViewProxy?.requestFocus()
    }

    override fun refreshScrollbarColor(mode: Int) {
        checkProxy()
        webViewProxy?.refreshScrollbarColor(mode)
    }

    override fun setScrollbarView(scrollbarView: WVScrollbarView?) {
        checkProxy()
        webViewProxy?.setScrollbarView(scrollbarView)
    }

    override fun setPaintScrollScale(scale: Float) {
        checkProxy()
        webViewProxy?.setPaintScrollScale(scale)
    }

    override fun hasJavascriptMethod(name: String, cb: CallJSResponse) {
        checkProxy()
        webViewProxy?.hasJavascriptMethod(name, cb)
    }

    override fun getTextAndHtml(cb: CallJSResponse) {
        checkProxy()
        webViewProxy?.getTextAndHtml(cb)
    }

    override fun getSelectText(cb: CallJSResponse) {
        checkProxy()
        webViewProxy?.getSelectText(cb)
    }

    override fun initContent(contentInfo: TiptapContentInfo, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.initContent(contentInfo, false, cb)
    }

    override fun updateContent(contentInfo: TiptapContentInfo, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.initContent(contentInfo, true, cb)
    }

    override fun updateContentMargin(margin: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.updateContentMargin(margin, cb)
    }

    override fun setText(text: InputContent, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setText(text, cb)
    }

    override fun replaceAllContent(text: String, isFinal: Boolean, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.replaceAllContent(text, isFinal, cb)
    }

    override fun clearContent(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.clearContent(cb)
    }

    override fun clearFocus(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.clearFocus(cb)
    }

    override fun focus(position: String, focusedEditor: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.focus(position, focusedEditor, cb)
    }

    override fun setBold(bold: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setBold(bold, cb)
    }

    override fun setItalic(italic: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setItalic(italic, cb)
    }

    override fun setUnderline(underline: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setUnderline(underline, cb)
    }

    override fun setStrikethrough(strikethrough: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setStrikethrough(strikethrough, cb)
    }

    override fun setFontSize(size: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setFontSize(size, cb)
    }

    override fun setBulletList(bullet: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setBulletList(bullet, cb)
    }

    override fun setBulletListHX(bullet: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setBulletListHX(bullet, cb)
    }

    override fun setOrderedList(ordered: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setOrderedList(ordered, cb)
    }

    override fun setTaskList(task: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setTaskList(task, cb)
    }

    override fun setTextAlign(align: String, cb: CallJSResponse?, history: Boolean) {
        checkProxy()
        webViewProxy?.setTextAlign(align, cb, history)
    }

    override fun insertImage(imageInfo: ImageInfo, addToPrevGroup: Boolean, insertToEndInNonEditMode: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.insertImage(imageInfo, addToPrevGroup, insertToEndInNonEditMode, cb)
    }

    override fun notifyImageSrcExist(attachId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.notifyImageSrcExist(attachId, cb)
    }

    override fun updateImage(oldAttachId: String?, imageInfo: ImageInfo, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updateImage(oldAttachId, imageInfo, cb)
    }

    override fun updateCard(cardAttr: CardAttr, oldAttachId: String?, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.updateCard(cardAttr, oldAttachId, cb)
    }

    override fun replaceNodeByCard(oldAttachId: String, cardAttr: CardAttr, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.replaceNodeByCard(oldAttachId, cardAttr, cb)
    }

    override fun replaceNodeByImage(oldAttachId: String?, imageInfo: ImageInfo, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.replaceNodeByImage(oldAttachId, imageInfo, cb)
    }

    override fun replaceAttachmentByText(attachId: String, text: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.replaceAttachmentByText(attachId, text, cb)
    }

    @Suppress("LongParameterList")
    override fun replaceNodeByPaint(
        oldAttachId: String?,
        paintId: String,
        paintSrc: String,
        imageId: String,
        imageSrc: String,
        imageWidth: Int,
        imageHeight: Int,
        tips: String?,
        needShowTips: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.replaceNodeByPaint(
            oldAttachId = oldAttachId,
            paintId = paintId,
            paintSrc = paintSrc,
            imageId = imageId,
            imageSrc = imageSrc,
            imageWidth = imageWidth,
            imageHeight = imageHeight,
            tips = tips,
            needShowTips = needShowTips,
            cb = cb
        )
    }

    override fun replaceNodeByDocument(
        oldAttachId: String,
        fileCard: FileCardData,
        addToPrevGroup: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.replaceNodeByDocument(oldAttachId, fileCard, addToPrevGroup, cb)
    }

    override fun replaceNodeByVideo(
        oldAttachId: String,
        videoData: VideoData,
        addToPrevGroup: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.replaceNodeByVideo(oldAttachId, videoData, addToPrevGroup, cb)
    }

    override fun replaceNodeByAudio(
        oldAttachId: String,
        recordAttr: RecordAttr,
        addToPrevGroup: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.replaceNodeByAudio(oldAttachId, recordAttr, addToPrevGroup, cb)
    }

    override fun focusCoordinate(x: Int, y: Int, textNodeRestricted: Boolean) {
        checkProxy()
        webViewProxy?.focusCoordinate(x, y, textNodeRestricted)
    }

    override fun updateContentHeight(newHeight: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updateContentHeight(newHeight, cb)
    }

    override fun onDestroy() {
        checkProxy()
        webViewProxy?.let {
            it.onDestroy()
            if (IWebViewProxyCache.ENABLE_CACHE) {
                webViewProxyCache?.recycleWebViewProxy(it)
            }
            it.setJsInterfaceConsumer(null)
        }
        webViewProxy = null
    }

    override fun scrollIntoView(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.scrollIntoView(cb)
    }

    override fun getNodeRectByCoords(left: Int, top: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getNodeRectByCoords(left, top, cb)
    }

    override fun getImageRectBySrc(src: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getImageRectBySrc(src, cb)
    }

    override fun getRecordDetailRect(attachId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getRecordDetailRect(attachId, cb)
    }

    override fun getRecordCardRect(attachId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getRecordCardRect(attachId, cb)
    }

    override fun scrollBy(interval: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.scrollBy(interval, cb)
    }

    override fun undo(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.undo(cb)
    }

    override fun redo(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.redo(cb)
    }

    override fun setBackgroundColor(color: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setBackgroundColor(color, cb)
    }

    override fun unsetBackgroundColor(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.unsetBackgroundColor(cb)
    }

    override fun setDensityScale(scale: Float, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setDensityScale(scale, cb)
    }

    override fun onTipTapMounted() {
        checkProxy()
        webViewProxy?.onTipTapMounted()
    }

    override fun setTextSelection(fromPos: Int, toPos: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setTextSelection(fromPos, toPos, cb)
    }

    override fun setTextSelectionAll(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setTextSelectionAll(cb)
    }

    override fun cancelTextSelectionAll(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.cancelTextSelectionAll(cb)
    }

    override fun setUiMode(mode: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setUiMode(mode, cb)
    }

    override fun updateRecordState(attachId: String, state: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updateRecordState(attachId, state, cb)
    }

    override fun updateRecordHasCallLogs(attachId: String, hasCallLogs: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updateRecordHasCallLogs(attachId, hasCallLogs, cb)
    }

    override fun updateRecord(oldAttachId: String, src: String, attachId: String, recordId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updateRecord(oldAttachId, src, attachId, recordId, cb)
    }

    override fun setRecordCurrentTime(attachId: String, time: Long, totalDuration: Long, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setRecordCurrentTime(attachId, time, totalDuration, cb)
    }

    override fun search(searchData: WVSearchData, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.search(searchData, cb)
    }

    override fun matchPrevious(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.matchPrevious(cb)
    }

    override fun matchNext(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.matchNext(cb)
    }

    override fun clearSearchResult(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.clearSearchResult(cb)
    }

    override fun smoothScrollTo(dx: Int, dy: Int, duration: Int) {
        checkProxy()
        webViewProxy?.smoothScrollTo(dx, dy, duration)
    }

    override fun deleteNodeByAttachId(attachId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.deleteNodeByAttachId(attachId, cb)
    }

    override fun insertCard(
        cardJson: CardAttr,
        addToPrevGroup: Boolean,
        insertToEndInNonEditMode: Boolean,
        pasteInsert: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.insertCard(cardJson, addToPrevGroup, insertToEndInNonEditMode, pasteInsert, cb)
    }

    override fun insertContactCard(cardJson: ContactCardAttr, addToPrevGroup: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.insertContactCard(cardJson, addToPrevGroup, cb)
    }

    override fun insertScheduleCard(cardJson: ScheduleCardAttr, addToPrevGroup: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.insertScheduleCard(cardJson, addToPrevGroup, cb)
    }

    override fun insertRecordCard(
        recordAttr: RecordAttr,
        addToPrevGroup: Boolean,
        insertToEndInNonEditMode: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.insertRecordCard(recordAttr, addToPrevGroup, insertToEndInNonEditMode, cb)
    }

    override fun insertFileCard(
        fileCard: FileCardData,
        addToPrevGroup: Boolean,
        insertToEndInNonEditMode: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.insertFileCard(fileCard, addToPrevGroup, insertToEndInNonEditMode, cb)
    }

    override fun setSummaryEntity(entities: String, marks: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setSummaryEntity(entities, marks, cb)
    }

    override fun setEditorScale(scale: Float, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setEditorScale(scale, cb)
    }

    override fun getEditorScale(): Float {
        checkProxy()
        return webViewProxy?.getEditorScale() ?: 1.0F
    }

    private fun checkProxy() {
        if (webViewProxy == null) {
            AppLogger.BASIC.i(TAG, "checkProxy: webViewProxy is null, this:$this")
        }
    }

    override fun insertPaint(paintAttr: PaintAttr, addToPrevGroup: Boolean, insertToEndInNonEditMode: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.insertPaint(paintAttr, addToPrevGroup = addToPrevGroup, cb = cb)
    }

    override fun updatePaint(originPaintId: String, newPaintAttr: PaintAttr, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updatePaint(
            originPaintId = originPaintId,
            newPaintAttr = newPaintAttr,
            cb = cb
        )
    }

    override fun deletePaint(paintId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.deletePaint(paintId = paintId, cb = cb)
    }

    override fun updateOverlayPaintHeight(height: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updateOverlayPaintHeight(height, cb)
    }

    override fun insertHintText(text: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.insertHintText(text, cb)
    }

    override fun deleteHintText(cb: CallJSResponse?) {
        insertHintText("")
    }

    override fun insertPhoneHintText(text: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.insertPhoneHintText(text, cb)
    }

    override fun onGlobalDragEnter(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onGlobalDragEnter(cb)
    }

    override fun onGlobalDragEnd(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onGlobalDragEnd(cb)
    }

    override fun loadImageTipsLottieAnimation(attachId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.loadImageTipsLottieAnimation(attachId, cb)
    }

    override fun destroyImageTipsLottieAnimation(attachId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.destroyImageTipsLottieAnimation(attachId, cb)
    }

    override fun setInterceptTouchEventListener(listener: IWVTouchEventListener?) {
        checkProxy()
        webViewProxy?.setInterceptTouchEventListener(listener)
    }

    override fun setDispatchTouchEventListener(listener: IWVDispatchTouchEventListener?) {
        checkProxy()
        webViewProxy?.setDispatchTouchEventListener(listener)
    }

    override fun setInterceptStylusTouchEventListener(listener: IWVInterceptStylusTouchEventListener?) {
        checkProxy()
        webViewProxy?.setInterceptStylusTouchEventListener(listener)
    }

    override fun setActionModeCallbackCreator(creator: () -> WVActionModeWrapper?) {
        checkProxy()
        webViewProxy?.setActionModeCallbackCreator(creator)
    }

    override fun removeActionModeCallbackCreator() {
        checkProxy()
        webViewProxy?.removeActionModeCallbackCreator()
    }

    override fun clearActionMode(needFinish: Boolean) {
        checkProxy()
        webViewProxy?.clearActionMode(needFinish)
    }

    override fun isViewModeStylusTouch(): Boolean {
        checkProxy()
        return webViewProxy?.isViewModeStylusTouch() == true
    }

    override fun drawDoodle(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.drawDoodle(cb)
    }

    override fun getAttachments(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getAttachments(cb)
    }

    override fun getEditorsHeight(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getEditorsHeight(cb)
    }

    override fun setBasicCssParams(basicCssParams: BasicCssParams, priority: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setBasicCssParams(basicCssParams, priority, cb)
    }

    override fun setSkinCssParams(skinCssParams: SkinCssParams, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setSkinCssParams(skinCssParams, cb)
    }

    override fun attachTipTapComponent(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.attachTipTapComponent(cb)
    }

    override fun detachTipTapComponent(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.detachTipTapComponent(cb)
    }

    override fun insertSummaryStreamTip(paras: SummaryStreamTipParams, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.insertSummaryStreamTip(paras, cb)
    }
    override fun updateSummaryStreamTip(paras: SummaryStreamTipParams, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.updateSummaryStreamTip(paras, cb)
    }
    override fun deleteSummaryStreamTip(cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.deleteSummaryStreamTip(cb)
    }

    override fun captureElement(captureElementInfos: List<CaptureElementInfo>, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.captureElement(captureElementInfos, cb)
    }

    override fun setOnRenderProcessGoneCb(cb: (() -> Unit)?) {
        checkProxy()
        webViewProxy?.setOnRenderProcessGoneCb(cb)
    }
    override fun interceptWebEditorClick(isIntercept: Boolean, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.interceptWebEditorClick(isIntercept, cb)
    }
    override fun disableRecord(attachId: String, disable: Boolean, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.disableRecord(attachId, disable, cb)
    }

    override fun performVibrate() {
        checkProxy()
        webViewProxy?.performVibrate()
    }

    override fun enableImageAnimation(enable: Boolean, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.enableImageAnimation(enable, cb)
    }

    override fun moveAttachToSelection(type: String, attachId: String, originPos: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.moveAttachToSelection(type, attachId, originPos, cb)
    }

    override fun getSelectedText(editor: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.getSelectedText(editor, cb)
    }

    override fun selectRangeTextAIGC(from: Int, to: Int, editor: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.selectRangeTextAIGC(from, to, editor, cb)
    }

    override fun selectAndGetAllText(editor: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.selectAndGetAllText(editor, cb)
    }

    override fun getAllHTML(editor: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.getAllHTML(editor, cb)
    }
    override fun selectAndGetForwardAllText(editor: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.selectAndGetForwardAllText(editor, cb)
    }

    override fun onAIGCRewriteStart(snackBarMargin: Int, aigcOption: String, isExport: Boolean, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onAIGCRewriteStart(snackBarMargin, aigcOption, isExport, cb)
    }

    override fun onAIGCRewriteFinish(manualStop: Boolean, margin: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onAIGCRewriteFinish(manualStop, margin, cb)
    }

    override fun onAIGCRewriteResult(
        content: String,
        isFinish: Boolean,
        aiHint: String,
        prePageText: String,
        nextPageText: String,
        cb: ((String) -> Unit)?
    ) {
        checkProxy()
        webViewProxy?.onAIGCRewriteResult(content, isFinish, aiHint, prePageText, nextPageText, cb)
    }

    override fun onAIGCRewriteDelete(cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onAIGCRewriteDelete(cb)
    }

    override fun onAIGCRewriteInsert(subStringLength: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onAIGCRewriteInsert(subStringLength, cb)
    }

    override fun onAIGCRewriteReplace(cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onAIGCRewriteReplace(cb)
    }

    override fun scrollToBottom(scrollToBottom: Boolean, marginBottom: Int, marginExtra: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.scrollToBottom(scrollToBottom, marginBottom, marginExtra, cb)
    }

    override fun setTitleStyle(titleStyle: Boolean, type: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setTitleStyle(titleStyle, type, cb)
    }

    override fun setContentStyle(contentStyle: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setContentStyle(contentStyle, cb)
    }

    override fun setTextColorType(colorType: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setTextColorType(colorType, cb)
    }

    override fun setTableColorType(tableColorPicker: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setTableColorType(tableColorPicker, cb)
    }

    override fun setAidTextStyle(aidTextStyle: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setAidTextStyle(aidTextStyle, cb)
    }

    override fun startSpeechRecognize(cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.startSpeechRecognize(cb)
    }

    override fun getFocusedEditor(cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.getFocusedEditor(cb)
    }

    override fun updateElementAttrs(elementSelectors: String, updateAttributes: Map<String, String>, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.updateElementAttrs(elementSelectors, updateAttributes, cb)
    }

    override fun onScrollStateChanged(oldState: Int, newState: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onScrollStateChanged(oldState, newState, cb)
    }

    override fun decreaseIndent(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.decreaseIndent(cb)
    }

    override fun increaseIndent(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.increaseIndent(cb)
    }

    override fun setBlockQuote(set: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setBlockQuote(set, cb)
    }

    override fun getCursorStartPos(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getCursorStartPos(cb)
    }

    override fun ignoreActionModeInvalidate(ignore: Boolean) {
        checkProxy()
        webViewProxy?.ignoreActionModeInvalidate(ignore)
    }

    override fun clearWebkitSelection() {
        checkProxy()
        webViewProxy?.clearWebkitSelection()
    }

    override fun doInsertTable(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.doInsertTable(cb)
    }

    override fun addRowInTable(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.addRowInTable(cb)
    }

    override fun addColumnInTable(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.addColumnInTable(cb)
    }

    override fun removeRowInTable(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.removeRowInTable(cb)
    }

    override fun removeColumnInTable(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.removeColumnInTable(cb)
    }

    override fun moveRowInTable(dir: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.moveRowInTable(dir, cb)
    }

    override fun moveColumnInTable(dir: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.moveColumnInTable(dir, cb)
    }

    override fun cutInTable(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.cutInTable(cb)
    }

    override fun copyInTable(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.copyInTable(cb)
    }

    override fun pasteInTable(pasteResult: PasteResult, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.pasteInTable(pasteResult, cb)
    }

    override fun shareTableToPicture(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.shareTableToPicture(cb)
    }

    override fun onAISummaryStartStop(isStart: Boolean) {
        checkProxy()
        webViewProxy?.onAISummaryStartStop(isStart)
    }

    override fun setPaintIsEmpty(isEmpty: Boolean) {
        checkProxy()
        webViewProxy?.setPaintIsEmpty(isEmpty)
    }

    override fun replaceSummaryContent(
        all: String,
        end: String,
        isFinal: Boolean,
        cb: ((String) -> Unit)?
    ) {
        checkProxy()
        webViewProxy?.setSummaryContent(all, end, isFinal, cb)
    }

    override fun hasLinkCard(cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.hasLinkCard(cb)
    }

    override fun onChangeWebViewWidthScale(start: Boolean) {
        checkProxy()
        webViewProxy?.onChangeWebViewWidthScale(start)
    }

    override fun getAIGCHtmlContent(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getAIGCHtmlContent(cb)
    }

    override fun selectAllAIGCText(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.selectAllAigcText(cb)
    }

    override fun getSelectedAIGCHtmlContent(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getSelectedAigcHtmlContent(cb)
    }

    override fun onActionItemClickedBefore(actionId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onActionItemClickedBefore(actionId, cb)
    }

    override fun onActionItemClickedAfter(actionId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onActionItemClickedAfter(actionId, cb)
    }

    override fun getTableFullDisplayWidth(startCapture: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getTableFullDisplayWidth(startCapture, cb)
    }

    override fun onTableMenuDismiss(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onTableMenuDismiss(cb)
    }

    override fun onTableSelectMenuDismiss(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onTableSelectMenuDismiss(cb)
    }

    override fun insertVideoPlaceHolder(
        videoData: VideoData,
        addToPrevGroup: Boolean,
        insertToEndInNonEditMode: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.insertVideoPlaceHolder(videoData, addToPrevGroup, insertToEndInNonEditMode, cb)
    }

    override fun updateVideo(
        videoData: VideoData,
        addToPrevGroup: Boolean,
        insertToEndInNonEditMode: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.updateVideo(videoData, addToPrevGroup, insertToEndInNonEditMode, cb)
    }

    override fun updateDocThumbnail(
        attachId: String,
        docAttachId: String,
        thumbnail: String,
        cb: ((String) -> Unit)?
    ) {
        checkProxy()
        webViewProxy?.updateDocthumbnail(attachId, docAttachId, thumbnail, cb)
    }

    override fun notifyScrollIdle(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.notifyScrollIdle(cb)
    }
}