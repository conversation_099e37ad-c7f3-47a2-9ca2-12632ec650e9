/**
 * Copyright (C), 2010-2030, OPLUS Mobile Comm Corp., Ltd.
 * File           : JsObjectCache.kt
 * Description    : description
 * Version        : 1.0
 * Date           : 2024/4/10
 * Author         : XinYang.Hu
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * XinYang.Hu     2024/4/10        1.0           create
 */
package com.oplus.notes.webviewcoverpaint.cache

import android.webkit.JavascriptInterface
import com.oplus.note.logger.AppLogger
import com.oplus.notes.webviewcoverpaint.container.api.IMyJavascriptInterface

class JsObjectCache : IMyJavascriptInterface {
    companion object {
        private const val TAG = "JsObjectCache"
        private const val LOG_LEVEL_V = "v"
        private const val LOG_LEVEL_D = "d"
        private const val LOG_LEVEL_I = "i"
        private const val LOG_LEVEL_E = "e"
    }

    private var jsObjectConsumer: IMyJavascriptInterface? = null
    private var isMounted = false

    fun setJavascriptInterfaceConsumer(consumer: IMyJavascriptInterface?) {
        jsObjectConsumer = consumer
        AppLogger.BASIC.d(TAG,"setJavascriptInterfaceConsumer consumer:${consumer} isMounted:$isMounted")
        if (consumer != null && isMounted) {
            AppLogger.BASIC.i(TAG, "already onMounted")
            consumer.onTipTapMounted()
        }
    }

    /**
     * 网页加载完成
     */
    @JavascriptInterface
    override fun onTipTapMounted() {
        AppLogger.BASIC.i(TAG, "onTipTapMounted, jsObjectConsumer:$jsObjectConsumer")
        isMounted = true
        jsObjectConsumer?.onTipTapMounted()
    }

    /**
     * 标题获得焦点
     */
    @JavascriptInterface
    override fun onTitleFocus(focus: Boolean) {
        jsObjectConsumer?.onTitleFocus(focus)
    }

    /**
     * 内容焦点切换
     */
    @JavascriptInterface
    override fun onContentFocus(focus: Boolean) {
        jsObjectConsumer?.onContentFocus(focus)
    }

    /**
     * 文件摘要附件获得焦点
     */
    @JavascriptInterface
    override fun onDocumentFileClick(path: String) {
        jsObjectConsumer?.onDocumentFileClick(path)
    }

    /**
     * 网页点击文本
     */
    @JavascriptInterface
    override fun onTextClick(pos: String, fromEditor: Int) {
        jsObjectConsumer?.onTextClick(pos, fromEditor)
    }

    /**
     * 网页点击图片
     */
    @JavascriptInterface
    override fun onImageClick(src: String) {
        jsObjectConsumer?.onImageClick(src)
    }

    /**
     * 视频点击
     */
    @JavascriptInterface
    override fun onVideoClick(src: String, duration: String) {
        jsObjectConsumer?.onVideoClick(src, duration)
    }

    /**
     * 网页点击卡片
     */
    @JavascriptInterface
    override fun onCardClick(url: String) {
        jsObjectConsumer?.onCardClick(url)
    }

    /**
     * 网页点击任务
     */
    @JavascriptInterface
    override fun onTaskClick(clicked: String) {
        jsObjectConsumer?.onTaskClick(clicked)
    }

    /**
     * 网页点击链接
     */
    @JavascriptInterface
    override fun onLinkClick(href: String, eventX: Int, eventY: Int, pos: String) {
        jsObjectConsumer?.onLinkClick(href, eventX, eventY, pos)
    }

    /**
     * 网页长按可拖拽的元素
     */
    @JavascriptInterface
    override fun onLongClick(type: String, rect: String, outerHTML: String, attachId: String, position: Int, x: Float, y: Float): Boolean {
        return jsObjectConsumer?.onLongClick(type, rect, outerHTML, attachId, position, x, y) ?: false
    }

    /**
     * 网页是否有选中内容
     */
    @JavascriptInterface
    override fun onSelection(selection: String) {
        jsObjectConsumer?.onSelection(selection)
    }

    /**
     * 网页双击图片
     */
    @JavascriptInterface
    override fun onImageDoubleClick(src: String) {
        jsObjectConsumer?.onImageDoubleClick(src)
    }

    /**
     * 网页选中文本的富文本状态
     */
    @JavascriptInterface
    override fun isActive(mark: String, enable: Boolean, active: Boolean, isInTable: Boolean) {
        jsObjectConsumer?.isActive(mark, enable, active, isInTable)
    }

    /**
     * tiptap编辑器中光标所在位置的字体大小
     */
    @JavascriptInterface
    override fun onFontSizeActive(enable: Boolean, size: Int) {
        jsObjectConsumer?.onFontSizeActive(enable, size)
    }

    @JavascriptInterface
    override fun onTextAlignActive(enable: Boolean, align: String) {
        jsObjectConsumer?.onTextAlignActive(enable, align)
    }

    /**
     * 网页内文本拖拽开始
     */
    @JavascriptInterface
    override fun onTextDragStart() {
        jsObjectConsumer?.onTextDragStart()
    }

    @JavascriptInterface
    override fun onTextDragEnd() {
        jsObjectConsumer?.onTextDragEnd()
    }

    /**
     * 由网页内部回调的拖拽结束
     */
    @JavascriptInterface
    override fun handleDrop(focusPos: Int) {
        jsObjectConsumer?.handleDrop(focusPos)
    }

    /**
     * 文字输入
     */
    @JavascriptInterface
    override fun onTextInput(text: String) {
        jsObjectConsumer?.onTextInput(text)
    }

    /**
     * 文字变化
     */
    @JavascriptInterface
    override fun onTextChange(length: Int) {
        jsObjectConsumer?.onTextChange(length)
    }

    /**
     * 标题变化
     */
    @JavascriptInterface
    override fun onTitleChange(title: String) {
        jsObjectConsumer?.onTitleChange(title)
    }

    /**
     * 点击编辑器有效内容区外部
     */
    @JavascriptInterface
    override fun onClickEditorOutside() {
        jsObjectConsumer?.onClickEditorOutside()
    }

    /**
     * undo是否可用
     */
    @JavascriptInterface
    override fun undoAvailable(available: Boolean) {
        jsObjectConsumer?.undoAvailable(available)
    }

    /**
     * redo是否可用
     */
    @JavascriptInterface
    override fun redoAvailable(available: Boolean) {
        jsObjectConsumer?.redoAvailable(available)
    }

    /**
     *  @param height dp
     */
    @JavascriptInterface
    override fun onTitleHeightChange(height: Int) {
        jsObjectConsumer?.onTitleHeightChange(height)
    }

    /**
     * line-height CSS 属性用于设置多行元素的空间量，如多行文本的间距。
     *  @param height dp
     */
    @JavascriptInterface
    override fun onContentLineHeightChange(height: Int) {
        jsObjectConsumer?.onContentLineHeightChange(height)
    }

    @JavascriptInterface
    override fun scrollend() {
        jsObjectConsumer?.scrollend()
    }

    @JavascriptInterface
    override fun onRecordPlayClick(rect: String, isDetailVisible: Boolean, attachId: String) {
        jsObjectConsumer?.onRecordPlayClick(rect, isDetailVisible, attachId)
    }

    @JavascriptInterface
    override fun onWebAudioComponentLoaded(attachId: String) {
        AppLogger.BASIC.d(TAG, "onWebAudioComponentLoaded# attachId=$attachId")
        jsObjectConsumer?.onWebAudioComponentLoaded(attachId)
    }

    @JavascriptInterface
    override fun onRecordStopTrackingTouch(progress: Int, picAttachId: String) {
        jsObjectConsumer?.onRecordStopTrackingTouch(progress, picAttachId)
    }

    @JavascriptInterface
    override fun onPerformFeedback(progress: Int, max: Int, min: Int, isDragging: Boolean) {
        jsObjectConsumer?.onPerformFeedback(progress, max, min, isDragging)
    }

    @JavascriptInterface
    override fun onRequestDisallowInterceptTouchEvent(disallow: Boolean) {
        jsObjectConsumer?.onRequestDisallowInterceptTouchEvent(disallow)
    }

    @JavascriptInterface
    override fun onAudioDetailClick(attachId: String) {
        jsObjectConsumer?.onAudioDetailClick(attachId)
    }

    @JavascriptInterface
    override fun onObserveRecordCardVisible(visible: Boolean, attachId: String?) {
        jsObjectConsumer?.onObserveRecordCardVisible(visible, attachId)
    }

    @JavascriptInterface
    override fun onMatchResultUpdate(matchedSize: Int, matchedIndex: Int) {
        jsObjectConsumer?.onMatchResultUpdate(matchedSize, matchedIndex)
    }

    @JavascriptInterface
    override fun onNodeDeleted(name: String, attachId: String) {
        jsObjectConsumer?.onNodeDeleted(name, attachId)
    }

    @JavascriptInterface
    override fun onNodeAdded(name: String, attrs: String) {
        jsObjectConsumer?.onNodeAdded(name, attrs)
    }

    @JavascriptInterface
    override fun onJsLogPrint(level: String, tag: String, msg: String) {
        when (level) {
            LOG_LEVEL_V -> AppLogger.BASIC.v(tag, msg)
            LOG_LEVEL_D -> AppLogger.BASIC.d(tag, msg)
            LOG_LEVEL_I -> AppLogger.BASIC.i(tag, msg)
            LOG_LEVEL_E -> AppLogger.BASIC.e(tag, msg)
            else -> AppLogger.BASIC.d(tag, msg)
        }
    }

    @JavascriptInterface
    override fun onPaintClick(paintId: String, paintSrc: String, imageId: String, imageSrc: String) {
        jsObjectConsumer?.onPaintClick(paintId, paintSrc, imageId, imageSrc)
    }

    @JavascriptInterface
    override fun onPaintTipsClick(paintId: String, paintSrc: String, imageId: String, imageSrc: String) {
        jsObjectConsumer?.onPaintTipsClick(paintId, paintSrc, imageId, imageSrc)
    }

    @JavascriptInterface
    override fun onCopyViewClick(attrId: String, type: String) {
        jsObjectConsumer?.onCopyViewClick(attrId, type)
    }

    @JavascriptInterface
    override fun onContactCallClick(phone: String?) {
        jsObjectConsumer?.onContactCallClick(phone)
    }

    @JavascriptInterface
    override fun onContactSaveClick(phone: String?, name: String?, company: String?, position: String?) {
        jsObjectConsumer?.onContactSaveClick(phone, name, company, position)
    }

    @JavascriptInterface
    override fun onScheduleAddClick(attrId: String) {
        jsObjectConsumer?.onScheduleAddClick(attrId)
    }

    @JavascriptInterface
    override fun onAddressNavigateClick(address: String) {
        jsObjectConsumer?.onAddressNavigateClick(address)
    }

    @JavascriptInterface
    override fun onShowPictureGuideTips(rect: String, attachId: String?) {
        jsObjectConsumer?.onShowPictureGuideTips(rect, attachId)
    }

    @JavascriptInterface
    override fun onShowReachTextLimitToast() {
        jsObjectConsumer?.onShowReachTextLimitToast()
    }

    /**
     * webview文本框粘贴事件的回调
     * 判断是否拦截并返回文本内
     */
    @JavascriptInterface
    override fun handlePaste(selectionInTable: Boolean): String? {
        return jsObjectConsumer?.handlePaste(selectionInTable)
    }

    /**
     *  @param undo true: undo; false: redo
     */
    @JavascriptInterface
    override fun onDoodleUndoRedo(undo: Boolean) {
        jsObjectConsumer?.onDoodleUndoRedo(undo)
    }

    /**
     *  检测文件路径是否存在
     *  @param path 相对路径
     */
    @JavascriptInterface
    override fun pathExists(path: String): Boolean {
        return jsObjectConsumer?.pathExists(path) ?: false
    }

    @JavascriptInterface
    override fun onSummaryCancelClick() {
        jsObjectConsumer?.onSummaryCancelClick()
    }

    @JavascriptInterface
    override fun onSummaryRetryClick() {
        jsObjectConsumer?.onSummaryRetryClick()
    }

    @JavascriptInterface
    override fun onSummaryStopClick() {
        jsObjectConsumer?.onSummaryStopClick()
    }

    @JavascriptInterface
    override fun onInterceptWebEditorClickCall() {
        jsObjectConsumer?.onInterceptWebEditorClickCall()
    }
    fun detachTipTapComponent() {
        //分离TipTap组件时，需要重置已挂载状态，避免快速打开关闭便签详情触发TipTap快速分离/挂载出现TipTap未挂载成功前js消息就发送到TipTap组件的问题
        isMounted = false
    }

    @JavascriptInterface
    override fun onTitleJsonUpdate(json: String) {
        jsObjectConsumer?.onTitleJsonUpdate(json)
    }

    @JavascriptInterface
    override fun onContentJsonUpdate(json: String) {
        jsObjectConsumer?.onContentJsonUpdate(json)
    }

    @JavascriptInterface
    override fun startDragFromJs(type: String, attachId: String, position: Int, rect: String, captureElementInfo: String): Boolean {
        return jsObjectConsumer?.startDragFromJs(type, attachId, position, rect, captureElementInfo) ?: false
    }

    @JavascriptInterface
    override fun onTextStyleActive(enable: Boolean, textStyle: String) {
        jsObjectConsumer?.onTextStyleActive(enable, textStyle)
    }

    @JavascriptInterface
    override fun onTextColorActive(enable: Boolean, color: String) {
        jsObjectConsumer?.onTextColorActive(enable, color)
    }

    @JavascriptInterface
    override fun onTableColorActive(enable: Boolean, color: String) {
        jsObjectConsumer?.onTableColorActive(enable, color)
    }

    @JavascriptInterface
    override fun checkInCall(): Boolean {
        return jsObjectConsumer?.checkInCall() ?: false
    }

    @JavascriptInterface
    override fun getAIGCCount(num: Int): String {
        return jsObjectConsumer?.getAIGCCount(num) ?: ""
    }

    @JavascriptInterface
    override fun setOverScrollEnable(enable: Boolean) {
        jsObjectConsumer?.setOverScrollEnable(enable)
    }

    @JavascriptInterface
    override fun isOverScrolling(): Boolean {
        return jsObjectConsumer?.isOverScrolling() ?: false
    }

    @JavascriptInterface
    override fun onBulletListSymbolChange(symbol: String) {
        jsObjectConsumer?.onBulletListSymbolChange(symbol)
    }

    @JavascriptInterface
    override fun isExport(): Boolean {
        return jsObjectConsumer?.isExport() ?: false
    }

    @JavascriptInterface
    override fun getOverlayPaintHeight(): Float {
        return jsObjectConsumer?.getOverlayPaintHeight() ?: 0f
    }

    @JavascriptInterface
    override fun getAigcGenerateBarHeight(): Int {
        return jsObjectConsumer?.getAigcGenerateBarHeight() ?: 0
    }

    @JavascriptInterface
    override fun handleCopyOrCut(isCutAction: Boolean, isCellSelection: Boolean, plainText: String?, htmlText: String?) {
        jsObjectConsumer?.handleCopyOrCut(isCutAction, isCellSelection, plainText, htmlText)
    }

    @JavascriptInterface
    override fun handlePasteAttach(pasteAttachJson: String): String? {
        return jsObjectConsumer?.handlePasteAttach(pasteAttachJson)
    }

    @JavascriptInterface
    override fun onClickTableMenu(x: Float, y: Float, menuInfo: String) {
        jsObjectConsumer?.onClickTableMenu(x, y, menuInfo)
    }

    @JavascriptInterface
    override fun isImeVisible(): Boolean {
        return jsObjectConsumer?.isImeVisible() ?: false
    }

    @JavascriptInterface
    override fun onAigcShareClick(rect: String) {
        jsObjectConsumer?.onAigcShareClick(rect)
    }

    @JavascriptInterface
    override fun onTableCellContentOverflow() {
        jsObjectConsumer?.onTableCellContentOverflow()
    }

    @JavascriptInterface
    override fun onTableRowsOverLimit() {
        jsObjectConsumer?.onTableRowsOverLimit()
    }

    @JavascriptInterface
    override fun onTableColumnsOverLimit() {
        jsObjectConsumer?.onTableColumnsOverLimit()
    }

    @JavascriptInterface
    override fun onTablesOverLimit() {
        jsObjectConsumer?.onTablesOverLimit()
    }

    @JavascriptInterface
    override fun pastedUnsupportedDataInTable() {
        jsObjectConsumer?.pastedUnsupportedDataInTable()
    }

    @JavascriptInterface
    override fun onShowTableSelectMenu(x: Float, y: Float, menuInfo: String) {
        jsObjectConsumer?.onShowTableSelectMenu(x, y, menuInfo)
        AppLogger.BASIC.d(TAG, "onShowTableSelectMenu")
    }

    @JavascriptInterface
    override fun performVibrate() {
        jsObjectConsumer?.performVibrate()
    }

    @JavascriptInterface
    override fun onContentHasTable(hasTable: Boolean) {
        jsObjectConsumer?.onContentHasTable(hasTable)
    }
}