/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : ByWebView.kt
 * Description    : ByWebView.kt
 * Version        : 1.0
 * Date           : 2023/6/5
 * Author         : Chandler
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * Chandler     2023/6/5         1.0           create
 */
package com.oplus.notes.webviewcoverpaint.container.web

import android.annotation.SuppressLint
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.webkit.JavascriptInterface
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.heytap.tbl.webkit.TBLSdk
import com.heytap.tbl.webkit.WebSettings
import com.heytap.tbl.webkit.WebView
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.CardAttr
import com.oplus.notes.webviewcoverpaint.cache.JsObjectCache
import com.oplus.notes.webviewcoverpaint.cache.api.IAppVueJavaScriptInterface
import com.oplus.notes.webviewcoverpaint.cache.api.IWebViewProxyCache
import com.oplus.notes.webviewcoverpaint.container.WVSearchData
import com.oplus.notes.webviewcoverpaint.container.api.AttachData
import com.oplus.notes.webviewcoverpaint.container.api.AttrMenuData
import com.oplus.notes.webviewcoverpaint.container.api.CallJSResponse
import com.oplus.notes.webviewcoverpaint.container.api.ContactCardAttr
import com.oplus.notes.webviewcoverpaint.container.api.FileCardData
import com.oplus.notes.webviewcoverpaint.container.api.IMyJavascriptInterface
import com.oplus.notes.webviewcoverpaint.container.api.IScrollChangedListener
import com.oplus.notes.webviewcoverpaint.container.api.IWVJBResponseCallback
import com.oplus.notes.webviewcoverpaint.container.api.ImageInfo
import com.oplus.notes.webviewcoverpaint.container.api.InputContent
import com.oplus.notes.webviewcoverpaint.container.api.PaintAttr
import com.oplus.notes.webviewcoverpaint.container.api.RecordAttr
import com.oplus.notes.webviewcoverpaint.container.api.ScheduleCardAttr
import com.oplus.notes.webviewcoverpaint.container.api.SummaryStreamTipParams
import com.oplus.notes.webviewcoverpaint.container.api.TiptapFocusInfo
import com.oplus.notes.webviewcoverpaint.container.api.WVActionModeWrapper
import com.oplus.note.view.WVScrollbarView
import com.oplus.notes.webviewcoverpaint.container.api.ReplaceAttachmentData
import com.oplus.notes.webviewcoverpaint.container.api.VideoData
import com.oplus.notes.webviewcoverpaint.container.api.touchevent.IWVDispatchTouchEventListener
import com.oplus.notes.webviewcoverpaint.container.api.touchevent.IWVInterceptStylusTouchEventListener
import com.oplus.notes.webviewcoverpaint.container.api.touchevent.IWVTouchEventListener
import com.oplus.notes.webviewcoverpaint.container.common.ConfigUtils
import com.oplus.notes.webviewcoverpaint.container.common.CssHelper.convertToCssHexColor
import com.oplus.notes.webviewcoverpaint.container.impl.BuildConfig
import com.oplus.notes.webviewcoverpaint.container.web.WVJBWebView.Companion.DEFAULT_MSG_PRIORITY
import com.oplus.notes.webviewcoverpaint.container.web.WVJBWebView.Companion.EDITOR_SCALE_LEVEL_PRIORITY
import com.oplus.notes.webviewcoverpaint.container.web.WVJBWebView.Companion.LEVEL_1_PRIORITY
import com.oplus.notes.webviewcoverpaint.data.BasicCssParams
import com.oplus.notes.webviewcoverpaint.data.CacheRecycleParams
import com.oplus.notes.webviewcoverpaint.data.CaptureElementInfo
import com.oplus.notes.webviewcoverpaint.data.SkinCssParams
import com.oplus.notes.webviewcoverpaint.data.TiptapContentInfo
import com.oplus.notes.webviewcoverpaint.data.UpdateElementAttrs
import com.oplus.notes.webviewcoverpaint.data.clipboard.PasteResult
import org.json.JSONArray
import org.json.JSONObject
import java.util.UUID

class WebViewProxy private constructor(val builder: Builder) : IAppVueJavaScriptInterface {

    private var editorScale: Float = 1.0F
    private val context: Context = builder.context
    private val jsObjectCache = JsObjectCache()
    var webView: WVJBWebView?
    val cacheRecycleParams: CacheRecycleParams? = ConfigUtils.getWebViewCacheRecycleParams(context)
    private var isInitContent: Boolean? = null

    init {
        webView = WVJBWebView(context).apply {
            isHorizontalScrollBarEnabled = false
            isVerticalScrollBarEnabled = false
            setWebViewClientCb(ByWebViewClient(context))
        }
        val usingTBLWebView = webView?.isUsingTBLWebView == true
        AppLogger.BASIC.d(TAG, "isUsingTBLWebView=$usingTBLWebView")
        kotlin.runCatching {
            if (usingTBLWebView) {
                val version = TBLSdk.getCoreVersion()
                AppLogger.THIRDLOG.d(TAG, "53010101,isUsingTBL:true, version:$version")
            } else {
                AppLogger.THIRDLOG.d(TAG, "53010101,isUsingTBL:false")
            }
        }
        // 配置
        initWebViewSetting()
        initJsInterface("injectedAppVueObject", this)
        initJsInterface("injectedObject", jsObjectCache)
        // 屏蔽TBL的滚动条
        kotlin.runCatching {
            webView?.setTBLVerticalSliderEnabled(false)
            webView?.setTBLVerticalSliderDrawable(null)
        }.onFailure {
            AppLogger.BASIC.w(TAG, "setTBL fail:${it.message}")
            /*
             setTBLVerticalSliderEnabled必须在loadurl之前调用，但是当前会概率性会出现AwContents未初始化的空指针异常，异常原因为：
             AwContents在源码中是在线程中初始化的，源码中WebViewChromium所有对外接口都会通过checkNeedsPost判断是否需要丢到线程中执行，确保AwContents能先初始化完成。
             而新增的setTBLVerticalSliderEnabled等接口当前并未使用checkNeedsPost判断，但是又直接访问了AwContents对象。目前该问题已经反馈给tbl sdk，需要下个版本解决。
             当前先简单进行异常捕获处理，造成的影响：页面可能会显示tbl中自定义的滚动条。
             */
        }
        kotlin.runCatching {
            webView?.setTBLDragAndDropEnabled(true)
        }.onFailure {
            AppLogger.BASIC.w(TAG, "setTBLDragAndDropEnabled fail:${it.message}")
        }

        if (IWebViewProxyCache.ENABLE_CACHE) {
            loadUrl(URL)
        }
        removeUnsafeJsInterface()
    }

    @JavascriptInterface
    override fun onAppVueMounted(): String {
        val jsObject = JSONObject().apply {
            put("colorOSVersion", webView?.onAppVueMounted())
        }
        return jsObject.toString()
    }

    fun setJsInterfaceConsumer(consumer: IMyJavascriptInterface?) {
        jsObjectCache.setJavascriptInterfaceConsumer(consumer)
    }

    @SuppressLint("JavascriptInterface", "AddJavascriptInterface")
    fun initJsInterface(name: String, obj: Any?) {
        if (name.isNotEmpty() && obj != null) {
            webView?.addJavascriptInterface(obj, name)
        }
    }

    fun loadUrl(url: String) {
        if (url.isNotEmpty()) {
            webView?.loadUrl(url)
            AppLogger.BASIC.d(TAG, "load url done")
        }
    }

    fun addScrollChangedListener(listener: IScrollChangedListener?) {
        webView?.addScrollChangedListener(listener)
    }

    fun removeScrollChangedListener(listener: IScrollChangedListener?) {
        webView?.removeScrollChangedListener(listener)
    }

    private fun removeUnsafeJsInterface() {
        // 移除有风险的WebView系统隐藏接口
        webView?.apply {
            this.removeJavascriptInterface("searchBoxJavaBridge_")
            this.removeJavascriptInterface("accessibility")
            this.removeJavascriptInterface("accessibilityTraversal")
        }
    }

    @SuppressLint("JavascriptInterface", "AddJavascriptInterface")
    private fun initJsInterface(builder: Builder) {
        val interfaceName = builder.interfaceName
        val interfaceObj = builder.interfaceObj
        if (!interfaceName.isNullOrEmpty() && interfaceObj != null) {
            webView?.addJavascriptInterface(interfaceObj, interfaceName)
        }
    }

    @SuppressLint("SetJavaScriptEnabled", "WebviewDebug")
    private fun initWebViewSetting() {
        webView?.settings?.apply {
            // 是否应该支持使用其屏幕缩放控件和手势缩放
            this.builtInZoomControls = false
            this.setSupportZoom(false)
            this.displayZoomControls = false
            // 设置缓存模式
            this.cacheMode = WebSettings.LOAD_DEFAULT
            // 网页内容的宽度自适应屏幕
            this.loadWithOverviewMode = false
            this.useWideViewPort = false
            // 告诉WebView启用JavaScript执行。默认的是false。
            this.javaScriptEnabled = true
            // 使用localStorage则必须打开
            this.domStorageEnabled = false
            this.allowFileAccess = false
            // 排版适应屏幕
            this.layoutAlgorithm = android.webkit.WebSettings.LayoutAlgorithm.NORMAL
            // WebView从5.0开始默认不允许混合模式,https中不能加载http资源,需要设置开启。
            this.mixedContentMode = WebSettings.MIXED_CONTENT_NEVER_ALLOW
            if (BuildConfig.DEBUG) {
                WebView.setWebContentsDebuggingEnabled(true)
            }
            // 禁止缩放，避免修改系统字体大小时，WebView自身缩放和setDensityScale逻辑冲突
            this.textZoom = TEXT_ZOOM_100
        }
    }

    fun onResume() {
        webView?.onResume()
    }

    fun onPause() {
        webView?.onPause()
    }

    fun onDestroy(forceDestroyWebView: Boolean = false) {
        removeActionModeCallbackCreator()
        isInitContent = null
        webView?.apply {
            visibility = View.INVISIBLE
            if (!isDestroyed) {
                this.recycle(forceDestroyWebView)
                this.stopLoading()
                this.onPause()
                this.clearHistory()
                this.clearCache(true)
            }
            this.removeAllViews()
            val parent = this.parent as? ViewGroup
            parent?.removeView(webView)
            if (forceDestroyWebView || !IWebViewProxyCache.ENABLE_CACHE) {
                // destroy 需要放在 removeView后面
                this.destroy()
            }
        }
        if (forceDestroyWebView || !IWebViewProxyCache.ENABLE_CACHE) {
            AppLogger.BASIC.d(TAG, "forceDestroyWebView")
            webView = null
        }
    }

    fun getTextAndHtml(cb: (String) -> Unit) {
        AppLogger.BASIC.d(TAG, "getTextAndHtml: request")
        webView?.callHandler(METHOD_GET_TEXT_AND_HTML, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "getTextAndHtml: onResult, length=${data.length}")
                cb(data)
            }
        })
    }

    fun getSelectText(cb: (String) -> Unit) {
        AppLogger.BASIC.d(TAG, "getSelectText: request")
        webView?.callHandler(METHOD_GET_SELECT_TEXT, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                cb(data)
            }
        })
    }

    fun isInitContent(): Boolean {
        return isInitContent == true
    }

    fun initContent(contentInfo: TiptapContentInfo, isUpdate: Boolean = false, cb: ((String) -> Unit)? = null) {
        AppLogger.BASIC.d(TAG, "initContent: isUpdate=$isUpdate")
        val dependentHandlerNames = mutableListOf(
            METHOD_ATTACH_TIP_TAP_COMPONENT,
            METHOD_SET_BASIC_CSS_PARAMS, METHOD_SET_SKIN_CSS_PARAMS
        )
        dependentHandlerNames.add(METHOD_SET_DENSITY_SCALE)
        dependentHandlerNames.add(METHOD_SET_EDITOR_SCALE)
        if (isUpdate) {
            // 如果是isUpdate则直接设置为true
            isInitContent = true
            webView?.callHandler(METHOD_UPDATE_CONTENT, Gson().toJson(contentInfo), object : IWVJBResponseCallback {
                override fun onResult(data: String) {
                    AppLogger.BASIC.d(TAG, "update content: onResult = $data")
                    cb?.invoke(data)
                }
            }, dependentHandlerNames = dependentHandlerNames)
        } else {
            // 如果是新建初始化则先设置成false,初始化完成再true
            isInitContent = false
            webView?.callHandler(METHOD_INIT_CONTENT, Gson().toJson(contentInfo), object : IWVJBResponseCallback {
                override fun onResult(data: String) {
                    AppLogger.BASIC.d(TAG, "initContent: onResult = $data")
                    isInitContent = true
                    cb?.invoke(data)
                }
            }, dependentHandlerNames = dependentHandlerNames)
        }
    }

    fun setText(text: InputContent, cb: ((String) -> Unit)? = null) {
        AppLogger.BASIC.d(TAG, "setText: $text")
        val jsonObject = Gson().toJson(text)
        webView?.callHandler(METHOD_SET_TEXT, jsonObject, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setText: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun updateContentMargin(margin: Int, cb: ((String) -> Unit)? = null) {
        AppLogger.BASIC.d(TAG, "updateContentMargin: margin=$margin")
        webView?.callHandler(UPDATE_CONTENT_MARGIN, margin.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "updateContentMargin: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun replaceAllContent(text: String, isFinal: Boolean, cb: ((String) -> Unit)? = null) {
        AppLogger.BASIC.d(TAG, "replaceAllContent isFinal = $isFinal")
        val jsonObject = JSONObject().apply {
            put("text", text)
            put("isFinal", isFinal)
        }
        webView?.callHandler(METHOD_REPLACE_ALL_CONTENT, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "replaceAllContent: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun insertSummaryStreamTip(paras: SummaryStreamTipParams, cb: ((String) -> Unit)? = null) {
        AppLogger.BASIC.d(TAG, "insertSummaryStreamTip paras $paras")
        webView?.callHandler(METHOD_INSERT_SUMMARY_STREAM_TIP, Gson().toJson(paras), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "insertSummaryStreamTip: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun updateSummaryStreamTip(paras: SummaryStreamTipParams, cb: ((String) -> Unit)? = null) {
        AppLogger.BASIC.d(TAG, "updateSummaryStreamTip paras $paras")
        webView?.callHandler(METHOD_UPDATE_SUMMARY_STREAM_TIP, Gson().toJson(paras), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "updateSummaryStreamTip: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun deleteSummaryStreamTip(cb: ((String) -> Unit)? = null) {
        AppLogger.BASIC.d(TAG, "deleteSummaryStreamTip")
        webView?.callHandler(METHOD_DELETE_SUMMARY_STREAM_TIP, "", object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "deleteSummaryStreamTip: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun clearContent(cb: ((String) -> Unit)? = null) {
        AppLogger.BASIC.d(TAG, "clearContent: ")
        webView?.callHandler(METHOD_CLEAR_CONTENT, "", object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "clearContent: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun clearFocus(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "clearFocus:")
        webView?.callHandler(METHOD_CLEAR_FOCUS, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "clearFocus: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun focus(position: String, focusedEditor: Int, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "focus: position = $position, focusedEditor=$focusedEditor")
        val focusInfo = TiptapFocusInfo(position, focusedEditor)
        webView?.callHandler(METHOD_FOCUS, Gson().toJson(focusInfo), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "focus: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun hasJavascriptMethod(name: String, cb: CallJSResponse) {
        AppLogger.BASIC.d(TAG, "hasJavascriptMethod: $name")
        webView?.hasJavascriptMethod(METHOD_HAS_METHOD, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "hasJavascriptMethod: $data")
                cb(data)
            }
        })
    }

    fun setTitleStyle(titleStyle: Boolean, type: String, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setTitleStyle: $titleStyle,type:$type")
        val jsonObject = JSONObject().apply {
            put("titleStyle", titleStyle)
            put("type", type)
        }
        webView?.callHandler(METHOD_SET_TITLE_STYLE, jsonObject.toString(), object : IWVJBResponseCallback {
                override fun onResult(data: String) {
                    AppLogger.BASIC.d(TAG, "setTitleStyle: onResult = $data")
                    cb?.invoke(data)
                }
            })
    }
    fun setContentStyle(contentStyle: Boolean, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setContentStyle: $contentStyle")
        webView?.callHandler(METHOD_SET_CONTENT_STYLE, contentStyle.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setContentStyle: onResult = $data")
                cb?.invoke(data)
            }
        })
    }
    fun setAidTextStyle(aidTextStyle: Boolean, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setAidTextStyle: $aidTextStyle")
        webView?.callHandler(METHOD_SET_AID_TEXT_STYLE, aidTextStyle.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setAidTextStyle: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun setTextColorType(colorType: String, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setTextColorType: $colorType")
        webView?.callHandler(METHOD_SET_TEXT_COLOR, colorType, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setTextColorType: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun setTableColorType(tableColorPicker: String, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setTableColorType: $tableColorPicker")
        webView?.callHandler(METHOD_SET_TABLE_COLOR, tableColorPicker, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setTableColorType: onResult = $data")
                cb?.invoke(data)
            }
        })
    }


    fun setBold(bold: Boolean, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setBold: $bold")
        webView?.callHandler(METHOD_SET_BOLD, bold.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setBold: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun setItalic(italic: Boolean, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setItalic: $italic")
        webView?.callHandler(METHOD_SET_ITALIC, italic.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setItalic: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun setUnderline(underline: Boolean, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setUnderline: $underline")
        webView?.callHandler(METHOD_SET_UNDERLINE, underline.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setUnderline: onResult = $data")
                cb?.invoke(data)
            }
        })
    }
    fun setStrikethrough(strikethrough: Boolean, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "Strikethrough: $strikethrough")
        webView?.callHandler(METHOD_SET_CROSS_LINE, strikethrough.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setStrikethrough: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun setFontSize(size: Int, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setFontSize: fontSize: $size")
        webView?.callHandler(
            METHOD_SET_FONT_SIZE,
            size.toString(),
            object : IWVJBResponseCallback {
                override fun onResult(data: String) {
                    AppLogger.BASIC.d(TAG, "setFontSize: onResult = $data")
                    cb?.invoke(data)
                }
            })
    }

    fun setDensityScale(scale: Float, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setDensityScale: $scale")
        webView?.callHandler(
            METHOD_SET_DENSITY_SCALE,
            scale.toString(),
            object : IWVJBResponseCallback {
                override fun onResult(data: String) {
                    AppLogger.BASIC.d(TAG, "setDensityScale: onResult = $data")
                    cb?.invoke(data)
                }
            }, LEVEL_1_PRIORITY
        )
    }

    fun setBulletList(bullet: Boolean, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setBulletList: $bullet")
        webView?.callHandler(METHOD_TOGGLE_BULLET_LIST, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setBulletList: onResult = $data")
                cb?.invoke(data)
            }
        })
    }
    fun setBulletListHX(bulletHX: Boolean, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setBulletListHX: $bulletHX")
        webView?.callHandler(METHOD_TOGGLE_BULLET_LIST_HX, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setBulletListHX: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun setOrderedList(ordered: Boolean, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setBulletList: $ordered")
        webView?.callHandler(METHOD_TOGGLE_ORDERED_LIST, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setBulletList: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun setTaskList(task: Boolean, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setTaskList: $task")
        webView?.callHandler(METHOD_TOGGLE_TASK_LIST, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setTaskList: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun setTextAlign(align: String, cb: ((String) -> Unit)?, history: Boolean) {
        AppLogger.BASIC.d(TAG, "setTextAlign: $align, history:$history")
        val jsonObject = JSONObject().apply {
            put("align", align)
            put("history", history)
        }
        webView?.callHandler(METHOD_SET_TEXT_ALIGN, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setTextAlign: onResult = $data")
                cb?.invoke(data)
            }
        }, priority = DEFAULT_MSG_PRIORITY - 1)
    }

    fun insertImage(
        imageInfo: ImageInfo,
        addToPrevGroup: Boolean = false,
        insertToEndInNonEditMode: Boolean = true,
        cb: ((String) -> Unit)?
    ) {
        AppLogger.BASIC.d(TAG, "insertImage: imageInfo=$imageInfo")
        val attachData =
            AttachData(addToPrevGroup = addToPrevGroup, insertToEndInNonEditMode = insertToEndInNonEditMode, image = imageInfo)
        webView?.callHandler(METHOD_INSERT_IMAGE, Gson().toJson(attachData), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setImage: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun notifyImageSrcExist(attachId: String, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "notifyImageSrcExist: $attachId")
        val key = attachId + METHOD_NOTIFY_IMAGE_SRC_EXIT
        webView?.callHandler(key, attachId, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "notifyImageSrcExist: $attachId, onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun updateImage(oldAttachId: String?, imageInfo: ImageInfo, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "replaceImage: oldAttachId=$oldAttachId,imageInfo=$imageInfo")
        if (oldAttachId == null) {
            AppLogger.BASIC.d(TAG, "replaceImage: return idNull")
            return
        }
        val menuData = AttrMenuData(clickAttachId = oldAttachId, image = imageInfo)
        webView?.callHandler(METHOD_UPDATE_IMAGE, Gson().toJson(menuData), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "replaceImage: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun updateCard(cardAttr: CardAttr, oldAttachId: String?, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "updateCard: cardAttr = $cardAttr, oldAttachId = $oldAttachId")

        if (oldAttachId == null) {
            AppLogger.BASIC.d(TAG, "updateCard: return idNull")
            return
        }
        val menuData = AttrMenuData(clickAttachId = oldAttachId, card = cardAttr)
        webView?.callHandler(METHOD_UPDATE_CARD, Gson().toJson(menuData), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "updateCard: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    /**
     * 附件菜单 卡片替换图片
     *
     */
    fun replaceNodeByCard(oldAttachId: String, cardAttr: CardAttr, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "replaceNodeByCard: cardAttr = $cardAttr")

        val menuData = AttrMenuData(clickAttachId = oldAttachId, card = cardAttr)
        val cardJson = GsonBuilder().disableHtmlEscaping().create().toJson(menuData)
        webView?.callHandler(METHOD_REPLACE_NODE_BY_CARD, cardJson, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "replaceNodeByCard: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun replaceNodeByImage(oldAttachId: String?, imageInfo: ImageInfo, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "replaceNodeByImage: imageInfo=$imageInfo")
        if (oldAttachId == null) {
            AppLogger.BASIC.d(TAG, "replaceNodeByImage: return idNull")
            return
        }
        val attrMenuData = AttrMenuData(clickAttachId = oldAttachId, image = imageInfo)
        webView?.callHandler(METHOD_REPLACE_NODE_BY_IMAGE, Gson().toJson(attrMenuData), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "replaceCardByImage: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun replaceAttachmentByText(attachId: String, text: String, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "replaceAttachmentByText: attachId = $attachId, text=$text")
        val menuData = AttrMenuData(clickAttachId = attachId, text = text)
        webView?.callHandler(
            METHOD_REPLACE_ATTACHMENT_BY_TEXT, Gson().toJson(menuData),
            object : IWVJBResponseCallback {
                override fun onResult(data: String) {
                    AppLogger.BASIC.d(TAG, "replaceAttachmentByText: onResult = $data")
                    cb?.invoke(data)
                }
            })
    }

    @Suppress("LongParameterList")
    fun replaceNodeByPaint(
        oldAttachId: String?,
        paintId: String,
        paintSrc: String,
        imageId: String,
        imageSrc: String,
        imageWidth: Int,
        imageHeight: Int,
        tips: String?,
        needShowTips: Boolean,
        cb: ((String) -> Unit)?
    ) {
        AppLogger.BASIC.d(TAG, "replaceNodeByPaint: oldAttachId=$oldAttachId")
        val jsonObject = JSONObject().apply {
            put("oldPaintId", oldAttachId)
            put("newAttr", JSONObject().apply {
                put("paintId", paintId)
                put("paintSrc", paintSrc)
                put("imageId", imageId)
                put("imageSrc", imageSrc)
                put("imageWidth", imageWidth)
                put("imageHeight", imageHeight)
                put("tips", tips)
                put("needShowTips", needShowTips)
            })
        }
        webView?.callHandler(METHOD_REPLACE_NODE_BY_PAINT, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "replaceNodeByPaint: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun replaceNodeByDocument(
        oldAttachId: String,
        fileCard: FileCardData,
        addToPrevGroup: Boolean,
        cb: CallJSResponse?
    ) {
        val data = ReplaceAttachmentData(oldAttachId, AttachData(addToPrevGroup, fileCard = fileCard))
        webView?.callHandler(METHOD_REPLACE_NODE_BY_DOCUMENT, Gson().toJson(data), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "replaceNodeByDocument: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun replaceNodeByVideo(
        oldAttachId: String,
        videoData: VideoData,
        addToPrevGroup: Boolean,
        cb: CallJSResponse?
    ) {
        val data = ReplaceAttachmentData(oldAttachId, AttachData(addToPrevGroup, videoCard = videoData))
        webView?.callHandler(METHOD_REPLACE_NODE_BY_VIDEO, Gson().toJson(data), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "replaceNodeByVideo: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun replaceNodeByAudio(
        oldAttachId: String,
        recordAttr: RecordAttr,
        addToPrevGroup: Boolean,
        cb: CallJSResponse?
    ) {
        val data = ReplaceAttachmentData(oldAttachId, AttachData(addToPrevGroup, record = recordAttr))
        webView?.callHandler(METHOD_REPLACE_NODE_BY_AUDIO, Gson().toJson(data), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "replaceNodeByAudio: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun smoothScrollTo(
        dx: Int,
        dy: Int,
        duration: Int
    ) {
        webView?.smoothScrollTo(dx, dy, duration)
    }

    fun focusCoordinate(x: Int, y: Int, textNodeRestricted: Boolean) {
        val jsonObject = JSONObject().apply {
            put("x", x)
            put("y", y)
            put("textRestricted", textNodeRestricted)
        }
        AppLogger.BASIC.d(TAG, "focusCoordinate: x = $x, y = $y")
        webView?.callHandler(METHOD_FOCUS_COORDINATE, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "focusCoordinate: onResult = $data")
            }
        })
    }

    fun updateContentHeight(newHeight: Int, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "updateContentHeight: $newHeight")
        webView?.callHandler(
            METHOD_UPDATE_CONTENT_HEIGHT,
            newHeight.toString(),
            object : IWVJBResponseCallback {
                override fun onResult(data: String) {
                    AppLogger.BASIC.d(TAG, "updateContentHeight: onResult = $data")
                    cb?.invoke(data)
                }
            })
    }


    fun requestFocus() {
        if (webView?.isAttachedToWindow == true) {
            AppLogger.BASIC.d(TAG, "requestFocus")
            webView?.requestFocus()
        }
    }

    fun setScrollbarView(scrollbarView: WVScrollbarView?) {
        webView?.setScrollbarView(scrollbarView)
    }

    fun refreshScrollbarColor(mode: Int) {
        webView?.refreshScrollbarColor(mode)
    }

    fun setPaintScrollScale(scale: Float) {
        webView?.setPaintScrollScale(scale)
    }

    fun scrollIntoView(cb: CallJSResponse?) {
        AppLogger.BASIC.d(TAG, "scrollIntoView: ")
        webView?.callHandler(METHOD_SCROLL_INTO_VIEW, "", object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "scrollIntoView: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun disableRecord(attachId: String, disable: Boolean, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "isIntercept: $disable")
        val jsonObject = JSONObject().apply {
            put("attachId", attachId)
            put("disable", disable)
        }
        webView?.callHandler(
            METHOD_DISABLE_RECORD,
            jsonObject.toString(),
            object : IWVJBResponseCallback {
                override fun onResult(data: String) {
                    AppLogger.BASIC.d(TAG, "disableAudio: onResult = $data")
                    cb?.invoke(data)
                }
            })
    }

    fun performVibrate() {
        webView?.performVibrate()
    }

    fun interceptWebEditorClick(isIntercept: Boolean, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "interceptWebEditorClick: $isIntercept")
        webView?.callHandler(
            METHOD_INTERCEPT_CLICKON_EDIT_AREA,
            isIntercept.toString(),
            object : IWVJBResponseCallback {
                override fun onResult(data: String) {
                    AppLogger.BASIC.d(TAG, "interceptWebEditorClick: onResult = $data")
                    cb?.invoke(data)
                }
            })
    }
    fun getNodeRectByCoords(left: Int, top: Int, cb: ((String) -> Unit)?) {
        val jsonObject = JSONObject().apply {
            put("left", left)
            put("top", top)
        }
        AppLogger.BASIC.d(TAG, "getNodeRectByCoords: left = $left, top = $top")
        webView?.callHandler(METHOD_GET_NODE_RECT_BY_COORDS, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "getNodeRectByCoords: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun getImageRectBySrc(src: String, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "getImageRectBySrc: src = $src")
        webView?.callHandler(METHOD_GET_NODE_RECT_BY_SRC, src, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "getImageRectBySrc: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun getRecordDetailRect(attachId: String, cb: ((String) -> Unit)?) {
        webView?.callHandler(METHOD_GET_RECORD_DETAIL_RECT, attachId, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "getRecordDetailRect: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun getRecordCardRect(attachId: String, cb: ((String) -> Unit)?) {
        webView?.callHandler(METHOD_GET_RECORD_CARD_RECT, attachId, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "getRecordCardRect: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun scrollBy(interval: Int, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "scrollBy: interval = $interval")
        webView?.callHandler(METHOD_SCROLL_BY, interval.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "scrollBy: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun undo(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "undo:")
        webView?.callHandler(METHOD_UNDO, "", object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "undo: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun redo(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "redo:")
        webView?.callHandler(METHOD_REDO, "", object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "redo: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun setBackgroundColor(color: Int, cb: ((String) -> Unit)?) {
        val cssColor = convertToCssHexColor(color)
        AppLogger.BASIC.d(TAG, "setBackgroundColor: $cssColor")
        webView?.callHandler(METHOD_SET_BACKGROUND_COLOR, cssColor, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setBackgroundColor: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun unsetBackgroundColor(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "unsetBackgroundColor:")
        webView?.callHandler(METHOD_UNSET_BACKGROUND_COLOR, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "unsetBackgroundColor: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun onTipTapMounted() {
        webView?.onTipTapMounted()
    }

    fun setTextSelection(fromPos: Int, toPos: Int, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setTextSelection: $fromPos, $toPos")
        val jsonObject = JSONObject().apply {
            put("from", fromPos)
            put("to", toPos)
        }
        webView?.callHandler(METHOD_SET_TEXT_SELECTION, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setTextSelection: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun setTextSelectionAll(cb: ((String) -> Unit)?) {
        webView?.callHandler(METHOD_SET_TEXT_SELECTION_ALL, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setTextSelectionAll: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun cancelTextSelectionAll(cb: ((String) -> Unit)?) {
        webView?.callHandler(METHOD_CANCEL_TEXT_SELECTION_ALL, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "cancelTextSelectionAll: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun setUiMode(mode: Int, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setUiMode: mode=$mode")
        val dependentHandlerNames = mutableListOf(METHOD_SET_BASIC_CSS_PARAMS)
        webView?.callHandler(METHOD_SET_UI_MODE, mode.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setUiMode: onResult = $data")
                cb?.invoke(data)
            }
        }, priority = DEFAULT_MSG_PRIORITY - 1, dependentHandlerNames = dependentHandlerNames)
    }

    fun updateRecordState(attachId: String, state: Int, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "updateRecordState: play state=$state")
        val jsonObject = JSONObject().apply {
            put("attachId", attachId)
            put("state", state)
        }
        webView?.callHandler(METHOD_UPDATE_RECORD_STATE, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "updateRecordState: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun updateRecordHasCallLogs(attachId: String, hasCallLogs: Boolean, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "updateRecordHasCallLogs: attachId=$attachId，hasCallLogs：$hasCallLogs")
        val jsonObject = JSONObject().apply {
            put("attachId", attachId)
            put("hasCallLogs", hasCallLogs)
        }
        webView?.callHandler(METHOD_UPDATE_RECORD_CALLLOGS, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "updateRecordHasCallLogs: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun updateRecord(oldAttachId: String, src: String, attachId: String, recordId: String, cb: ((String) -> Unit)?) {
        val jsonObject = JSONObject().apply {
            put("oldAttachId", oldAttachId)
            put("src", src)
            put("attachId", attachId)
            put("recordId", recordId)
        }

        AppLogger.BASIC.d(TAG, "updateRecord: jsonObject=$jsonObject")
        webView?.callHandler(METHOD_UPDATE_RECORD, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "updateRecord: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun setRecordCurrentTime(attachId: String, time: Long, totalDuration: Long, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setRecordCurrentTime: time=$time,totalDuration=$totalDuration")
        val jsonObject = JSONObject().apply {
            put("attachId", attachId)
            put("currentTime", time)
            put("duration", totalDuration)
        }
        webView?.callHandler(METHOD_SET_RECORD_CURRENTTIME, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setRecordCurrentTime: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun search(searchData: WVSearchData, cb: ((String) -> Unit)?) {
        val searchList = searchData.searchList
        val attachId = searchData.searchAttachmentId
        val highLightFirstOne = searchData.highLightFirstOne
        val scrollToFirstOne = searchData.scrollToFirstOne
        val isFromSearchNoteList = searchData.isFromSearchNoteList
        AppLogger.BASIC.d(
            TAG,
            "searchList:${searchList.size},attachId:$attachId,highLightFirstOne:$highLightFirstOne,scrollToFirstOne:$scrollToFirstOne"
        )
        val jsonObject = JSONObject().apply {
            put("searchList", JSONArray(searchList))
            put("attachId", attachId)
            put("highLightFirstOne", highLightFirstOne)
            put("scrollToFirstOne", scrollToFirstOne)
            put("isFromSearchNoteList", isFromSearchNoteList)
        }
        webView?.callHandler(METHOD_SEARCH, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "search: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun matchPrevious(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "matchPrevious: ")
        webView?.callHandler(METHOD_MATCH_PREVIOUS, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "matchPrevious: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun matchNext(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "matchNext: ")
        webView?.callHandler(METHOD_MATCH_NEXT, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "matchNext: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun clearSearchResult(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "clearSearchResult: ")
        webView?.callHandler(METHOD_CLEAR_SEARCH_RESULT, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "clearSearchResult: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun deleteNodeByAttachId(attachId: String, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "deleteNodeByAttachId: $attachId")
        webView?.callHandler(METHOD_DELETE_NODE_BY_ATTACH_ID, attachId, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "deleteNodeByAttachId: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun insertCard(
        cardAttr: CardAttr,
        addToPrevGroup: Boolean = false,
        insertToEndInNonEditMode: Boolean = true,
        pasteInsert: Boolean = false,
        cb: ((String) -> Unit)?
    ) {
        AppLogger.BASIC.d(TAG, "insertCard: cardJson=$cardAttr")
        val attachData =
            AttachData(addToPrevGroup = addToPrevGroup, card = cardAttr, insertToEndInNonEditMode = insertToEndInNonEditMode)
        val cardJson = GsonBuilder().disableHtmlEscaping().create().toJson(attachData)
        webView?.callHandler(METHOD_INSERT_CARD, cardJson, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "insertCard: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun insertContactCard(cardAttr: ContactCardAttr, addToPrevGroup: Boolean = false, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "insertContactCard: cardJson=$cardAttr")
        val attachData = AttachData(addToPrevGroup = addToPrevGroup, contactCard = cardAttr)
        webView?.callHandler(METHOD_INSERT_CONTACT_CARD, Gson().toJson(attachData), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "insertContactCard: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun insertScheduleCard(cardAttr: ScheduleCardAttr, addToPrevGroup: Boolean = false, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "insertScheduleCard: cardJson=$cardAttr")
        val attachData = AttachData(addToPrevGroup = addToPrevGroup, scheduleCard = cardAttr)
        webView?.callHandler(METHOD_INSERT_SCHEDULE_CARD, Gson().toJson(attachData), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "insertScheduleCard: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun insertRecordCard(
        recordAttr: RecordAttr,
        addToPrevGroup: Boolean = false,
        insertToEndInNonEditMode: Boolean = true,
        cb: ((String) -> Unit)?
    ) {
        AppLogger.BASIC.d(TAG, "insertRecordCard: recordAttr=$recordAttr insertToEndInNonEditMode=$insertToEndInNonEditMode")
        val attachData =
            AttachData(addToPrevGroup = addToPrevGroup, record = recordAttr, insertToEndInNonEditMode = insertToEndInNonEditMode)
        webView?.callHandler(METHOD_INSERT_RECORD_CARD, Gson().toJson(attachData), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "insertRecordCard: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun insertFileCard(
        fileCard: FileCardData,
        addToPrevGroup: Boolean = false,
        insertToEndInNonEditMode: Boolean = true,
        cb: ((String) -> Unit)?
    ) {
        AppLogger.BASIC.d(TAG, "insertFileCard: FileCard=$fileCard")
        val attachData =
            AttachData(addToPrevGroup = addToPrevGroup, fileCard = fileCard, insertToEndInNonEditMode = insertToEndInNonEditMode)
        webView?.callHandler(METHOD_INSERT_FILE_CARD, Gson().toJson(attachData), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "insertFileCard: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun setSummaryEntity(entities: String, marks: String, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setSummaryEntity: entities=${entities.length}, marks=${marks.length}")
        val jsonObject = JSONObject().apply {
            put("entities", entities)
            put("marks", marks)
        }
        val dependentHandlerNames = listOf(METHOD_INIT_CONTENT)
        webView?.callHandler(METHOD_SET_SUMMARY_ENTITY, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setSummaryEntity: onResult = $data")
                cb?.invoke(data)
            }
        }, dependentHandlerNames = dependentHandlerNames)
    }

    fun setEditorScale(scale: Float, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setEditorScale: scale=$scale")
        editorScale = scale
        webView?.callHandler(METHOD_SET_EDITOR_SCALE, scale.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setEditorScale: onResult = $data scale:${webView?.scale}")
                cb?.invoke(data)
            }
        }, priority = EDITOR_SCALE_LEVEL_PRIORITY)
    }

    fun getEditorScale(): Float {
        return editorScale
    }

    fun setBasicCssParams(basicCssParams: BasicCssParams, priority: Int = DEFAULT_MSG_PRIORITY - 1, cb: ((String) -> Unit)?) {
        val basicCssData = Gson().toJson(basicCssParams)
        AppLogger.BASIC.d(TAG, "setBasicCssParams: data=$basicCssData")
        webView?.callHandler(METHOD_SET_BASIC_CSS_PARAMS, basicCssData, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setBasicCssParams: onResult = $data")
                cb?.invoke(data)
            }
        }, priority = priority)
    }

    fun setSkinCssParams(skinCssParams: SkinCssParams, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setSkinCssParams: data=$skinCssParams")
        val skinCssData = Gson().toJson(skinCssParams)
        webView?.callHandler(METHOD_SET_SKIN_CSS_PARAMS, skinCssData, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setSkinCssParams: onResult = $data")
                cb?.invoke(data)
            }
        }, priority = DEFAULT_MSG_PRIORITY - 1)
    }

    fun insertPaint(paintAttr: PaintAttr, addToPrevGroup: Boolean = false, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "insertPaint: paintId=${paintAttr.paintId}")
        val attachData = AttachData(addToPrevGroup = addToPrevGroup, paint = paintAttr)
        webView?.callHandler(
            METHOD_INSERT_PAINT,
            Gson().toJson(attachData),
            object : IWVJBResponseCallback {
                override fun onResult(data: String) {
                    AppLogger.BASIC.d(TAG, "insertPaint: onResult = $data")
                    cb?.invoke(data)
                }
            })
    }

    fun updatePaint(originPaintId: String, newPaintAttr: PaintAttr, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "updatePaint: originPaintId=$originPaintId")
        val jsonObject = JSONObject().apply {
            put("oldPaintId", originPaintId)
            put("newAttr", newPaintAttr.toJsonObject())
        }
        webView?.callHandler(METHOD_UPDATE_PAINT, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "updatePaint: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun deletePaint(paintId: String, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "deletePaint: paintId=$paintId")
        webView?.callHandler(METHOD_DELETE_PAINT, paintId, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "deletePaint: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun updateOverlayPaintHeight(height: Int, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "updateOverlayPaintHeight: height=$height")
        webView?.callHandler(METHOD_UPDATE_OVERLAY_PAINT_HEIGHT, height.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "updateOverlayPaintHeight: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun insertHintText(text: String, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "insertHintText: text=$text")
        webView?.callHandler(METHOD_INSERT_HINT_TEXT, text, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "insertHintText: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun insertPhoneHintText(text: String, cb: ((String) -> Unit)?) {
        webView?.callHandler(METHOD_INSERT_PHONE_HINT_TEXT, text, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "insertPhoneHintText: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun onGlobalDragEnter(cb: ((String) -> Unit)?) {
        webView?.callHandler(METHOD_ON_GLOBAL_DRAG_ENTER, "", object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "onGlobalDragEnter: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun onGlobalDragEnd(cb: ((String) -> Unit)?) {
        clearWebkitSelection()
        webView?.callHandler(METHOD_ON_GLOBAL_DRAG_END, "", object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "onGlobalDragEnd: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun loadImageTipsLottieAnimation(attachId: String, cb: CallJSResponse?) {
        val key = attachId + METHOD_LOAD_IMAGE_TIPS_LOTTIE_ANIMATION
        webView?.callHandler(key, attachId, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "$key: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun destroyImageTipsLottieAnimation(attachId: String, cb: CallJSResponse?) {
        val key = attachId + METHOD_DESTROY_IMAGE_TIPS_LOTTIE_ANIMATION
        webView?.callHandler(key, attachId, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "$key: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun setInterceptTouchEventListener(listener: IWVTouchEventListener?) {
        webView?.interceptTouchEventListener = listener
    }

    fun setDispatchTouchEventListener(listener: IWVDispatchTouchEventListener?) {
        webView?.dispatchTouchEventListener = listener
    }

    fun setInterceptStylusTouchEventListener(listener: IWVInterceptStylusTouchEventListener?) {
        webView?.interceptStylusTouchEventListener = listener
    }

    fun setActionModeCallbackCreator(creator: () -> WVActionModeWrapper?) {
        webView?.actionModeCallbackCreator = creator
    }

    fun removeActionModeCallbackCreator() {
        webView?.actionModeCallbackCreator = null
    }

    fun clearActionMode(needFinish: Boolean) {
        webView?.clearActionMode(needFinish)
    }

    fun isViewModeStylusTouch(): Boolean {
        return webView?.isViewModeStylusTouch == true
    }

    fun drawDoodle(cb: ((String) -> Unit)?) {
        webView?.callHandler(METHOD_INSERT_DOODLE_COMMAND, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "drawDoodle: onResult = $data")
                cb?.invoke(data)
            }
        })
    }


    fun getAttachments(cb: ((String) -> Unit)?) {
        webView?.callHandler(METHOD_GET_ALL_ATTACHMENTS, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "getAttachments: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun getEditorsHeight(cb: ((String) -> Unit)?) {
        webView?.callHandler(METHOD_GET_EDITORS_HEIGHT, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "getEditorsHeight: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun attachTipTapComponent(cb: ((String) -> Unit)? = null) {
        webView?.callHandler(METHOD_ATTACH_TIP_TAP_COMPONENT, true.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "attachTipTapComponent: onResult = $data")
                cb?.invoke(data)
            }
        }, priority = 0, dependentHandlerNames = listOf(ON_APP_VUE_MOUNTED)) // attach tiptap需要最高优先级
    }

    fun detachTipTapComponent(cb: ((String) -> Unit)? = null) {
        webView?.callHandler(METHOD_ATTACH_TIP_TAP_COMPONENT, false.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "detachTipTapComponent: onResult = $data")
                cb?.invoke(data)
            }
        }, dependentHandlerNames = listOf(ON_APP_VUE_MOUNTED))
        jsObjectCache.detachTipTapComponent()
    }

    fun captureElement(captureElementInfos: List<CaptureElementInfo>, cb: ((String) -> Unit)? = null) {
        webView?.callHandler(METHOD_CAPTURE_ELEMENT, Gson().toJson(captureElementInfos), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "captureElement: onResult")
                cb?.invoke(data)
            }
        }, dependentHandlerNames = listOf(METHOD_INIT_CONTENT))
    }

    fun enableImageAnimation(enable: Boolean, cb: ((String) -> Unit)? = null) {
        webView?.callHandler(METHOD_ENABLE_IMG_ANIMATION, enable.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "enableImageAnimation: onResult")
                cb?.invoke(data)
            }
        })
    }

    fun moveAttachToSelection(type: String, attachId: String, originPos: Int, cb: ((String) -> Unit)? = null) {
        val jsonObject = JSONObject().apply {
            put("type", type)
            put("attachId", attachId)
            put("originPos", originPos)
        }
        webView?.callHandler(METHOD_MOVE_ATTACH_TO_SELECTION, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "moveAttachToSelection: onResult")
                cb?.invoke(data)
            }
        })
    }

    fun scrollToBottom(scrollToBottom: Boolean, marginBottom: Int, marginExtra: Int, cb: ((String) -> Unit)?) {
        val jsonObject = JSONObject().apply {
            put("scrollToBottom", scrollToBottom)
            put("marginBottom", marginBottom)
            put("marginExtra", marginExtra)
        }
        webView?.callHandler(METHOD_SCROLL_TO_BOTTOM, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "scrollToBottom: onResult:$data")
                cb?.invoke(data)
            }
        })
    }

    fun onAIGCRewriteStart(snackBarMargin: Int, aigcOption: String, isExport: Boolean, cb: ((String) -> Unit)?) {
        val jsonObject = JSONObject().apply {
            put("margin", snackBarMargin)
            put("aigcOption", aigcOption)
            put("isExport", isExport)
        }
        webView?.callHandler(METHOD_ON_AIGC_REWRITE_START, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "onAIGCRewriteStart: onResult")
                cb?.invoke(data)
            }
        })
    }

    fun onAIGCRewriteFinish(manualStop: Boolean, margin: Int, cb: ((String) -> Unit)?) {
        val jsonObject = JSONObject().apply {
            put("margin", margin)
            put("manualStop", manualStop)
        }
        webView?.callHandler(METHOD_ON_AIGC_REWRITE_FINISH, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "onAIGCRewriteFinish: onResult")
                cb?.invoke(data)
            }
        })
    }

    fun startSpeechRecognize(cb: ((String) -> Unit)?) {
        webView?.callHandler(METHOD_START_SPEECH_RECOGNIZE, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "startSpeechRecognize: onResult:$data")
                cb?.invoke(data)
            }
        })
    }

    fun getFocusedEditor(cb: ((String) -> Unit)?) {
        webView?.callHandler(METHOD_GET_FOCUSED_EDITOR, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "getFocusedEditor: onResult:$data")
                cb?.invoke(data)
            }
        })
    }
    fun getAllHTML(editor: Int, cb: ((String) -> Unit)?) {
        webView?.callHandler(METHOD_GET_ALL_HTML, editor.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "getAllHTML result:${data.length}")
                cb?.invoke(data)
            }
        })
    }
    fun onAIGCRewriteResult(
        content: String,
        isFinish: Boolean,
        aiHint: String,
        prePageText: String,
        nextPageText: String,
        cb: ((String) -> Unit)?
    ) {
        val jsonObject = JSONObject().apply {
            put("content", content)
            put("isFinish", isFinish)
            put("aiHint", aiHint)
            put("prePageText", prePageText)
            put("nextPageText", nextPageText)
        }
        webView?.callHandler(
            METHOD_ON_AIGC_REWRITE_RESULT,
            jsonObject.toString(),
            object : IWVJBResponseCallback {
                override fun onResult(data: String) {
                    AppLogger.BASIC.d(TAG, "onAIGCRewriteResult: onResult")
                    cb?.invoke(data)
                }
            })
    }

    fun onAIGCRewriteDelete(
        cb: ((String) -> Unit)?
    ) {
        AppLogger.BASIC.d(TAG, "onAIGCRewriteDelete in")
        webView?.callHandler(METHOD_ON_AIGC_REWRITE_DELETE, "", object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "onAIGCRewriteDelete: onResult")
                cb?.invoke(data)
            }
        })
    }

    fun onAIGCRewriteInsert(
        subStringLength: Int,
        cb: ((String) -> Unit)?
    ) {
        val jsObject = JSONObject().apply {
            put("subStringLength", subStringLength)
        }
        webView?.callHandler(METHOD_ON_AIGC_REWRITE_INSERT, jsObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "onAIGCRewriteInsert: onResult")
                cb?.invoke(data)
            }
        })
    }

    fun onAIGCRewriteReplace(
        cb: ((String) -> Unit)?
    ) {
        webView?.callHandler(METHOD_ON_AIGC_REWRITE_REPLACE, "", object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "onAIGCRewriteReplace: onResult")
                cb?.invoke(data)
            }
        })
    }

    fun getSelectedText(editor: Int, cb: ((String) -> Unit)?) {
        webView?.callHandler(METHOD_GET_SELECTED_TEXT, editor.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "getSelectedText result:${data.length}")
                cb?.invoke(data)
            }
        })
    }

    fun selectRangeTextAIGC(from: Int, to: Int, editor: Int, cb: ((String) -> Unit)?) {
        val jsonObject = JSONObject().apply {
            put("from", from)
            put("to", to)
            put("editor", editor)
        }
        webView?.callHandler(METHOD_SELECT_RANGE_TEXT_AIGC, jsonObject.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "selectRangeText result:${data.length}")
                cb?.invoke(data)
            }
        })
    }

    fun selectAndGetAllText(editor: Int, cb: ((String) -> Unit)?) {
        webView?.callHandler(METHOD_SELECT_AND_GET_ALL_TEXT, editor.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "selectAndGetAllText result:${data.length}")
                cb?.invoke(data)
            }
        })
    }

    fun selectAndGetForwardAllText(editor: Int, cb: ((String) -> Unit)?) {
        webView?.callHandler(METHOD_SELECT_AND_GET_FORWARD_ALL_TEXT, editor.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "selectAndGetForwardAllText size:${data.length}")
                cb?.invoke(data)
            }
        })
    }

    fun updateElementAttrs(elementSelectors: String, updateAttributes: Map<String, Any>, cb: ((String) -> Unit)?) {
        val updateElementAttrs = UpdateElementAttrs(elementSelectors, updateAttributes)
        webView?.callHandler(METHOD_UPDATE_ELEMENT_ATTRIBUTES, Gson().toJson(updateElementAttrs), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "updateElementAttrs:$data")
                cb?.invoke(data)
            }
        })
    }

    fun onScrollStateChanged(oldState: Int, newState: Int, cb: ((String) -> Unit)?) {
        if (newState == BounceLayout.SCROLL_STATE_IDLE) {
            webView?.setScrollInProgress(false)
        } else if (newState == BounceLayout.SCROLL_STATE_DRAGGING && oldState == BounceLayout.SCROLL_STATE_IDLE) {
            webView?.setScrollInProgress(true)
        }
        val state = if (oldState != BounceLayout.SCROLL_STATE_IDLE && newState == BounceLayout.SCROLL_STATE_IDLE) {
            '0'
        } else {
            '1'
        }
        webView?.callHandler(METHOD_NOTIFY_IDLE, state.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "notifyIdle:$data")
                cb?.invoke(data)
            }
        })
        val isUserScroll = newState == BounceLayout.SCROLL_STATE_DRAGGING
        webView?.callHandler(METHOD_SET_USER_SCROLL, isUserScroll.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setUserScroll:$data")
                cb?.invoke(data)
            }
        })
    }

    fun decreaseIndent(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "decreaseIndent:")
        webView?.callHandler(METHOD_DECREASE_INDENT, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "decreaseIndent:$data")
                cb?.invoke(data)
            }
        })
    }

    fun increaseIndent(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "increaseIndent:")
        webView?.callHandler(METHOD_INCREASE_INDENT, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "addIndent:$data")
                cb?.invoke(data)
            }
        })
    }

    fun getCursorStartPos(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "getCursorStartPos:")
        webView?.callHandler(METHOD_GET_CURSOR_START_POSITION, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "getCursorStartPos:$data")
                cb?.invoke(data)
            }
        })
    }

    fun setOnRenderProcessGoneCb(cb: (() -> Unit)?) {
        webView?.onRenderProcessGoneCb = cb
    }

    fun setBlockQuote(set: Boolean, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "setBlockQuote: $set")
        webView?.callHandler(METHOD_SET_BLOCK_QUOTE, set.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setBlockQuote:$data")
                cb?.invoke(data)
            }
        })
    }


    fun hasLinkCard(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "hasLinkCard")
        webView?.callHandler(METHOD_GET_HAS_LINK_CARD, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "hasLinkCard:$data")
                cb?.invoke(data)
            }
        })
    }


    fun getAIGCHtmlContent(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "getAIGCHtmlContent")
        webView?.callHandler(METHOD_GET_AIGC_HTMLCONTENT, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "getAIGCHtmlContent:${data.length}")
                cb?.invoke(data)
            }
        })
    }

    fun selectAllAigcText(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "selectAllAigcText")
        webView?.callHandler(METHOD_SELECT_ALL_AIGC_TEXT, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "selectAllAigcText result:${data.length}")
                cb?.invoke(data)
            }
        })
    }

    fun getSelectedAigcHtmlContent(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "getSelectedAigcHtmlContent")
        webView?.callHandler(METHOD_GET_SELECTED_AIGC_HTMLCONTENT, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "getSelectedAigcHtmlContent:${data.length}")
                cb?.invoke(data)
            }
        })
    }

    fun onActionItemClickedBefore(actionId: String, cb: CallJSResponse?): Boolean {
        AppLogger.BASIC.d(TAG, "onActionItemClickedBefore:$actionId")
        webView ?: return false
        webView?.callHandler(METHOD_ON_ACTION_ITEM_CLICKED_BEFORE, actionId, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "onActionItemClickedBefore:${data.length}")
                cb?.invoke(data)
            }
        })
        return true
    }

    fun onActionItemClickedAfter(actionId: String, cb: CallJSResponse?): Boolean {
        AppLogger.BASIC.d(TAG, "onActionItemClickedAfter:$actionId")
        webView ?: return false
        webView?.callHandler(METHOD_ON_ACTION_ITEM_CLICKED_AFTER, actionId, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "onActionItemClickedAfter:${data.length}")
                cb?.invoke(data)
            }
        })
        return true
    }

    fun getTableFullDisplayWidth(startCapture: Boolean, cb: CallJSResponse?) {
        AppLogger.BASIC.d(TAG, "getTableFullDisplayWidth startCapture=$startCapture")
        webView?.callHandler(METHOD_GET_TABLE_FULL_DISPLAY_WIDTH, startCapture.toString(), object: IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "getTableFullDisplayWidth result=$data")
                cb?.invoke(data)
            }
        })
    }

    fun onTableMenuDismiss(cb: CallJSResponse?) {
        AppLogger.BASIC.d(TAG, "onTableMenuDismiss")
        webView?.callHandler(METHOD_ON_TABLE_MENU_DISMISS, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "onTableMenuDismiss:${data.length}")
                cb?.invoke(data)
            }
        })
    }

    fun onTableSelectMenuDismiss(cb: CallJSResponse?) {
        AppLogger.BASIC.d(TAG, "onTableSelectMenuDismiss")
        webView?.callHandler(METHOD_ON_TABLE_SELECT_MENU_DISMISS, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "onTableSelectMenuDismiss:${data.length}")
                cb?.invoke(data)
            }
        })
    }

    fun notifyScrollIdle(cb: CallJSResponse?) {
        AppLogger.BASIC.d(TAG, "onNotifyScrollIdle")
        webView?.callHandler(METHOD_NOTIFY_IDLE, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "onNotifyScrollIdle")
                cb?.invoke(data)
            }
        })
    }

    fun insertVideoPlaceHolder(
        videoData: VideoData,
        addToPrevGroup: Boolean = false,
        insertToEndInNonEditMode: Boolean = true,
        cb: ((String) -> Unit)?
    ) {
        AppLogger.BASIC.d(TAG, "insertVideoPlaceHolder: videoData=$videoData")
        val attrHistoryData = AttachData(
            addToPrevGroup = addToPrevGroup, videoCard = videoData, insertToEndInNonEditMode = insertToEndInNonEditMode)
        webView?.callHandler(METHOD_INSERT_VIDEO_PLACEHOLDER, Gson().toJson(attrHistoryData), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "insertVideoPlaceHolder: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun updateVideo(videoData: VideoData, addToPrevGroup: Boolean = false, insertToEndInNonEditMode: Boolean = true, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "updateVideo: videoData=$videoData")
        val attrHistoryData = AttachData(addToPrevGroup = addToPrevGroup, videoCard = videoData, insertToEndInNonEditMode = insertToEndInNonEditMode)
        webView?.callHandler(METHOD_UPDATE_VIDEO, Gson().toJson(attrHistoryData), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "updateVideo: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    fun updateDocthumbnail(attachId: String, docAttachId: String, thumbnail: String, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "updateDocthumbnail: attachId=$attachId thumbnail= $thumbnail")
        val fileCard = FileCardData(src = "", attachId = attachId, docAttachId = docAttachId, docThumbnail = thumbnail)
        webView?.callHandler(METHOD_UPDATE_DOC_THUMBNAIL, Gson().toJson(fileCard), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "updateDocthumbnail: onResult = $data")
                cb?.invoke(data)
            }
        })
    }

    class Builder(val context: Context) {
        var url = ""
        var interfaceName: String? = null
        var interfaceObj: Any? = null

        /**
         * 直接获取ByWebView，避免一定要调用loadUrl()才能获取ByWebView的情况
         */
        fun get(): WebViewProxy {
            return WebViewProxy(this)
        }

        fun load(): WebViewProxy {
            return get()
        }
    }

    fun ignoreActionModeInvalidate(ignore: Boolean) {
        webView?.ignoreActionModeInvalidate(ignore)
    }

    fun clearWebkitSelection() {
        AppLogger.BASIC.d(TAG, "clearWebkitSelection")
        webView?.clearSelection()
    }

    fun doInsertTable(cb: ((String) -> Unit)?) {
        val attachId = UUID.randomUUID().toString()
        AppLogger.BASIC.d(TAG, "doInsertTable")
        webView?.callHandler(METHOD_INSERT_TABLE, attachId, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "doInsertTable:$data")
                cb?.invoke(data)
            }
        })
    }

    fun addRowInTable(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "addRowInTable")
        webView?.callHandler(METHOD_ADD_ROW_IN_TABLE, "", object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "addRowInTable:$data")
                cb?.invoke(data)
            }
        })
    }

    fun addColumnInTable(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "addColumnInTable")
        webView?.callHandler(METHOD_ADD_COLUMN_IN_TABLE, "", object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "addColumnInTable:$data")
                cb?.invoke(data)
            }
        })
    }

    fun removeRowInTable(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "removeRowInTable")
        webView?.callHandler(METHOD_REMOVE_ROW_IN_TABLE, "", object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "removeRowInTable:$data")
                cb?.invoke(data)
            }
        })
    }

    fun removeColumnInTable(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "removeColumnInTable")
        webView?.callHandler(METHOD_REMOVE_COLUMN_IN_TABLE, "", object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "removeColumnInTable:$data")
                cb?.invoke(data)
            }
        })
    }

    fun moveRowInTable(dir: Int, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "moveRowInTable, dir=$dir")
        webView?.callHandler(METHOD_MOVE_ROW_IN_TABLE, dir.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "moveRowInTable:$data")
                cb?.invoke(data)
            }
        })
    }

    fun moveColumnInTable(dir: Int, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "moveColumnInTable, dir=$dir")
        webView?.callHandler(METHOD_MOVE_COLUMN_IN_TABLE, dir.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "moveColumnInTable:$data")
                cb?.invoke(data)
            }
        })
    }

    fun cutInTable(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "cutInTable")
        webView?.callHandler(METHOD_CUT_IN_TABLE, "", object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "cutInTable:$data")
                cb?.invoke(data)
            }
        })
    }

    fun copyInTable(cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "copyInTable")
        webView?.callHandler(METHOD_COPY_IN_TABLE, "", object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "copyInTable:$data")
                cb?.invoke(data)
            }
        })
    }

    fun pasteInTable(pasteResult: PasteResult, cb: ((String) -> Unit)?) {
        AppLogger.BASIC.d(TAG, "pasteInTable")
        webView?.callHandler(METHOD_PASTE_IN_TABLE, Gson().toJson(pasteResult), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "pasteInTable:$data")
                cb?.invoke(data)
            }
        })
    }

    fun shareTableToPicture(cb: CallJSResponse? = null) {
        AppLogger.BASIC.d(TAG, "shareTableToPicture")
        webView?.callHandler(METHOD_SHARE_TABLE_TO_PICTURE, null, object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "shareTableToPicture $data")
                cb?.invoke(data)
            }
        })
    }

    fun onAISummaryStartStop(isStart: Boolean) {
        webView?.callHandler(METHOD_SET_AI_SUMMARY_START_STOP, isStart.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "onAISummaryStartStop isStart$isStart data:$data")
            }
        })
    }

    fun setPaintIsEmpty(isEmpty: Boolean) {
        webView?.callHandler(METHOD_SET_PAINT_EMPTY, isEmpty.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "setPaintIsEmpty isEmpty$isEmpty data:$data")
            }
        })
    }

    fun setSummaryContent(all: String, end: String, isFinal: Boolean, cb: ((String) -> Unit)?) {
        val jsonObject = JSONObject().apply {
            put("text", all)
            put("isFinal", isFinal)
            put("end", end)
        }
        webView?.callHandler(
            METHOD_SET_SUMMARY_CONTENT_WITH_ANIMATION,
            jsonObject.toString(),
            object : IWVJBResponseCallback {
                override fun onResult(data: String) {
                    AppLogger.BASIC.d(TAG, "setSummaryContent isFinal$isFinal")
                    cb?.invoke(data)
                }
            })
    }

    fun onChangeWebViewWidthScale(start: Boolean) {
        webView?.callHandler(METHOD_CHANGE_WEBVIEW_WIDTH_SCALE, start.toString(), object : IWVJBResponseCallback {
            override fun onResult(data: String) {
                AppLogger.BASIC.d(TAG, "onChangeWebViewWidthScale $data")
            }
        })
    }

    override fun toString(): String {
        return "${this.javaClass.name}@:${Integer.toHexString(this.hashCode())}, web:$webView"
    }

    companion object {
        private const val TAG = "WebViewProxy"
        private const val METHOD_GET_TEXT_AND_HTML = "callGetTextAndHtmlFromJava"
        private const val METHOD_SET_TEXT = "callSetTextFromJava"
        private const val METHOD_GET_SELECT_TEXT = "callGetSelectTextFromJava"
        private const val METHOD_INIT_CONTENT = "callInitContentFromJava"
        private const val METHOD_UPDATE_CONTENT = "callUpdateContentFromJava"
        private const val METHOD_CLEAR_CONTENT = "callClearContentFromJava"
        private const val METHOD_CLEAR_FOCUS = "callClearFocusFromJava"
        private const val METHOD_FOCUS = "callFocusFromJava"
        private const val METHOD_SET_BOLD = "callSetBoldFromJava"
        private const val METHOD_SET_ITALIC = "callSetItalicFromJava"
        private const val METHOD_SET_UNDERLINE = "callSetUnderlineFromJava"
        private const val METHOD_SET_CROSS_LINE = "callSetStrikethroughFromJava"
        private const val METHOD_SET_FONT_SIZE = "callSetFontSizeFromJava"
        private const val METHOD_TOGGLE_BULLET_LIST = "callToggleBulletListFromJava"
        private const val METHOD_TOGGLE_BULLET_LIST_HX = "callToggleBulletListHXFromJava"
        private const val METHOD_TOGGLE_ORDERED_LIST = "callToggleOrderedListFromJava"
        private const val METHOD_TOGGLE_TASK_LIST = "callToggleTaskListFromJava"
        private const val METHOD_SET_TEXT_ALIGN = "callSetTextAlignFromJava"
        private const val METHOD_INSERT_IMAGE = "callInsertImageFromJava"
        private const val METHOD_NOTIFY_IMAGE_SRC_EXIT = "-ImageSrcExistFromJava"
        private const val METHOD_UPDATE_IMAGE = "callUpdateImageFromJava"
        private const val METHOD_UPDATE_CARD = "callUpdateCardFromJava"
        private const val METHOD_REPLACE_NODE_BY_CARD = "callReplaceNodeByCardFromJava"
        private const val METHOD_REPLACE_NODE_BY_IMAGE = "callReplaceNodeByImageFromJava"
        private const val METHOD_REPLACE_ATTACHMENT_BY_TEXT = "callReplaceAttachmentByTextFromJava"
        private const val METHOD_SCROLL_INTO_VIEW = "callScrollIntoViewFromJava"
        private const val METHOD_FOCUS_COORDINATE = "callFocusCoordinateFromJava"
        private const val METHOD_GET_NODE_RECT_BY_COORDS = "callGetNodeRectByCoordsFromJava"
        private const val METHOD_GET_NODE_RECT_BY_SRC = "callGetNodeRectBySrcFromJava"
        private const val METHOD_GET_RECORD_DETAIL_RECT = "callGetRecordDetailRectFromJava"
        private const val METHOD_GET_RECORD_CARD_RECT = "callGetRecordCardRectFromJava"
        const val METHOD_UNDO = "callUndoFromJava"
        const val METHOD_REDO = "callRedoFromJava"
        private const val METHOD_SET_BACKGROUND_COLOR = "callSetBackgroundColorFromJava"
        private const val METHOD_UNSET_BACKGROUND_COLOR = "callUnsetBackgroundColorFromJava"
        private const val METHOD_SET_TEXT_SELECTION = "callSetTextSelectionFromJava"
        private const val METHOD_SET_TEXT_SELECTION_ALL = "callSetTextSelectionAllFromJava"
        private const val METHOD_CANCEL_TEXT_SELECTION_ALL = "callCancelTextSelectionAllFromJava"
        private const val METHOD_SET_UI_MODE = "callSetUiModeFromJava"
        private const val METHOD_SET_RECORD_CURRENTTIME = "callSetRecordCurrentTimeFromJava"
        private const val METHOD_SEARCH = "callSearchFromJava"
        private const val METHOD_MATCH_PREVIOUS = "callMatchPreviousFromJava"
        private const val METHOD_MATCH_NEXT = "callMatchNextFromJava"
        private const val METHOD_CLEAR_SEARCH_RESULT = "callClearSearchResultFromJava"
        private const val METHOD_DELETE_NODE_BY_ATTACH_ID = "callDeleteNodeByAttachIdFromJava"
        private const val METHOD_INSERT_CARD = "callInsertCardFromJava"
        private const val METHOD_SET_EDITOR_SCALE = "callSetEditorScaleFromJava"
        private const val METHOD_UPDATE_RECORD_STATE = "callUpdateRecordStateFromJava"
        private const val METHOD_UPDATE_RECORD_CALLLOGS = "callUpdateRecordCallLogsFromJava"
        private const val METHOD_UPDATE_RECORD = "callUpdateRecordFromJava"
        private const val METHOD_UPDATE_CONTENT_HEIGHT = "callUpdateContentHeightFromJava"
        private const val METHOD_HAS_METHOD = "_hasJavascriptMethod"
        private const val METHOD_CALL_JAVA = "callJavaFromJs"
        private const val METHOD_SET_DENSITY_SCALE = "callSetDensityScaleFromJava"
        private const val METHOD_INSERT_PAINT = "callInsertPaintFromJava"
        private const val METHOD_UPDATE_PAINT = "callUpdatePaintFromJava"
        private const val METHOD_DELETE_PAINT = "callDeletePaintFromJava"
        private const val METHOD_SET_BASIC_CSS_PARAMS = "callSetBasicCssParamsFromJava"
        private const val METHOD_SET_SKIN_CSS_PARAMS = "callSetSkinCssParamsFromJava"
        private const val METHOD_UPDATE_OVERLAY_PAINT_HEIGHT = "callUpdateOverlayPaintHeightFromJava"
        private const val METHOD_INSERT_HINT_TEXT = "callInsertHintTextFromJava"
        private const val METHOD_INSERT_PHONE_HINT_TEXT = "callInsertPhoneHintTextFromJava"
        private const val METHOD_SCROLL_BY = "callScrollByFromJava"
        private const val METHOD_INSERT_CONTACT_CARD = "callInsertContactCardFromJava"
        private const val METHOD_INSERT_SCHEDULE_CARD = "callInsertScheduleCardFromJava"
        private const val METHOD_INSERT_RECORD_CARD = "callInsertRecordCardFromJava"
        private const val METHOD_INSERT_FILE_CARD = "callInsertFileCardFromJava"
        private const val METHOD_SET_SUMMARY_ENTITY = "callSetSummaryEntityFromJava"
        private const val METHOD_INSERT_DOODLE_COMMAND = "callDrawDoodleFromJava"
        private const val METHOD_GET_ALL_ATTACHMENTS = "callGetAttachmentsFromJava"
        private const val METHOD_ON_GLOBAL_DRAG_ENTER = "callOnGlobalDragEnterFromJava"
        private const val METHOD_ON_GLOBAL_DRAG_END = "callOnGlobalDragEndFromJava"
        private const val METHOD_LOAD_IMAGE_TIPS_LOTTIE_ANIMATION = "-loadImageTipsLottieAnimation"
        private const val METHOD_DESTROY_IMAGE_TIPS_LOTTIE_ANIMATION = "-destroyImageTipsLottieAnimation"
        private const val METHOD_GET_EDITORS_HEIGHT = "callGetEditorsHeightFromJava"
        private const val METHOD_REPLACE_NODE_BY_PAINT = "callReplaceNodeByPaintFromJava"
        private const val METHOD_REPLACE_NODE_BY_DOCUMENT = "callReplaceNodeByDocumentFromJava"
        private const val METHOD_REPLACE_NODE_BY_VIDEO = "callReplaceNodeByVideoFromJava"
        private const val METHOD_REPLACE_NODE_BY_AUDIO = "callReplaceNodeByAudioFromJava"
        private const val METHOD_ATTACH_TIP_TAP_COMPONENT = "callAttachTipTapFromJava"
        private const val METHOD_REPLACE_ALL_CONTENT = "callReplaceAllContentFromJava"
        private const val METHOD_INSERT_SUMMARY_STREAM_TIP = "callInsertSummaryStreamTipFromJava"
        private const val METHOD_UPDATE_SUMMARY_STREAM_TIP = "callUpdateSummaryStreamTipFromJava"
        private const val METHOD_DELETE_SUMMARY_STREAM_TIP = "callDeleteSummaryStreamTipFromJava"
        private const val METHOD_CAPTURE_ELEMENT = "callCaptureElementFromJava"
        private const val METHOD_INTERCEPT_CLICKON_EDIT_AREA = "callEditorInterceptClickFromJava"
        private const val METHOD_DISABLE_RECORD = "callRecordDisableFromJava"
        private const val METHOD_ENABLE_IMG_ANIMATION = "callEnableImageAnimationFromJava"
        private const val METHOD_MOVE_ATTACH_TO_SELECTION = "callMoveAttachToSelectionFromJava"
        private const val METHOD_ON_AIGC_REWRITE_START = "callOnAIGCRewriteStartFromJava"
        private const val METHOD_ON_AIGC_REWRITE_FINISH = "callOnAIGCRewriteFinishFromJava"
        private const val METHOD_ON_AIGC_REWRITE_RESULT = "callOnAIGCRewriteResultFromJava"
        private const val METHOD_ON_AIGC_REWRITE_DELETE = "callOnAIGCRewriteDeleteFromJava"
        private const val METHOD_ON_AIGC_REWRITE_INSERT = "callOnAIGCRewriteInsertFromJava"
        private const val METHOD_ON_AIGC_REWRITE_REPLACE = "callOnAIGCRewriteReplaceFromJava"
        private const val METHOD_GET_SELECTED_TEXT = "callGetSelectedTextFromJava"
        private const val METHOD_SELECT_AND_GET_ALL_TEXT = "callSelectAndGetAllTextFromJava"
        private const val METHOD_SELECT_RANGE_TEXT_AIGC = "callSelectRangeTextAIGCFromJava"
        private const val METHOD_SELECT_AND_GET_FORWARD_ALL_TEXT = "callSelectAndGetForwardAllTextFromJava"
        private const val METHOD_SCROLL_TO_BOTTOM = "callScrollToBottomFromJava"
        private const val METHOD_GET_ALL_HTML = "callGetAllHTMLFromJava"
        private const val METHOD_START_SPEECH_RECOGNIZE = "callStartSpeechRecognizeFromJava"
        private const val METHOD_GET_FOCUSED_EDITOR = "callGetFocusedEditorFromJava"
        private const val METHOD_UPDATE_ELEMENT_ATTRIBUTES = "callUpdateElementAttributesFromJava"
        private const val METHOD_SET_USER_SCROLL = "callSetUserScrollFromJava"
        private const val METHOD_DECREASE_INDENT = "callDecreaseIndentFromJava"
        private const val METHOD_INCREASE_INDENT = "callIncreaseIndentFromJava"
        private const val METHOD_GET_CURSOR_START_POSITION = "callGetCursorStartPosition"
        private const val METHOD_INSERT_TABLE = "callInsertTableFromJava"
        private const val METHOD_ADD_ROW_IN_TABLE = "callAddRowInTableFromJava"
        private const val METHOD_ADD_COLUMN_IN_TABLE = "callAddColumnInTableFromJava"
        private const val METHOD_REMOVE_ROW_IN_TABLE = "callRemoveRowInTableFromJava"
        private const val METHOD_REMOVE_COLUMN_IN_TABLE = "callRemoveColumnInTableFromJava"
        private const val METHOD_MOVE_ROW_IN_TABLE = "callMoveRowInTableFromJava"
        private const val METHOD_MOVE_COLUMN_IN_TABLE = "callMoveColumnInTableFromJava"
        private const val METHOD_CUT_IN_TABLE = "callCutInTableFromJava"
        private const val METHOD_COPY_IN_TABLE = "callCopyInTableFromJava"
        private const val METHOD_PASTE_IN_TABLE = "callPasteInTableFromJava"
        private const val METHOD_SHARE_TABLE_TO_PICTURE = "callShareTableToPictureFromJava"

        private const val METHOD_SET_TITLE_STYLE = "callSetTitleStyleFromJava"
        private const val METHOD_SET_CONTENT_STYLE = "callSetContentStyleFromJava"
        private const val METHOD_SET_AID_TEXT_STYLE = "callSetAidTextStyleFromJava"
        private const val METHOD_SET_TEXT_COLOR = "callSetTextColorFromJava"
        private const val METHOD_SET_TABLE_COLOR = "callSetTableColorFromJava"
        private const val METHOD_SET_AI_SUMMARY_START_STOP = "callSetAiSummaryStartStop"
        private const val METHOD_SET_PAINT_EMPTY = "callSetPaintEmpty"
        private const val METHOD_SET_SUMMARY_CONTENT_WITH_ANIMATION = "callSetSummaryContentWithAnimation"
        private const val UPDATE_CONTENT_MARGIN = "updateContentMargin"
        private const val METHOD_GET_HAS_LINK_CARD = "callGetHasLinkCard"
        private const val METHOD_CHANGE_WEBVIEW_WIDTH_SCALE = "callChangeWebViewWidthScaleFromJava"
        private const val METHOD_GET_AIGC_HTMLCONTENT = "callOnGetAIGCHTMLContentFromJava"
        private const val METHOD_SELECT_ALL_AIGC_TEXT = "callOnAIGCSelectAllTextFromJava"
        private const val METHOD_GET_SELECTED_AIGC_HTMLCONTENT = "callOnAIGCGetSelectHtmlTextFromJava"
        private const val METHOD_ON_ACTION_ITEM_CLICKED_BEFORE = "callOnActionItemClickedBeforeFromJava"
        private const val METHOD_ON_ACTION_ITEM_CLICKED_AFTER = "callOnActionItemClickedAfterFromJava"
        private const val METHOD_GET_TABLE_FULL_DISPLAY_WIDTH = "callGetTableFullDisplayWidthFromJava"
        private const val METHOD_ON_TABLE_MENU_DISMISS = "callOnTableMenuDismissFromJava"
        private const val METHOD_ON_TABLE_SELECT_MENU_DISMISS = "callOnTableSelectMenuDismissFromJava"
        private const val METHOD_NOTIFY_IDLE = "callNotifyIdleFromJava"
        private const val METHOD_INSERT_VIDEO_PLACEHOLDER = "callInsertVideoPlaceHolderFromJava"
        private const val METHOD_UPDATE_VIDEO = "callUpdateVideoFromJava"
        private const val METHOD_UPDATE_DOC_THUMBNAIL = "callUpdateFileDocThumbnailFromJava"
        const val ON_APP_VUE_MOUNTED = "OnAppVueMounted"
        const val ON_TIP_TAP_MOUNTED = "OnTipTapMounted"

        private const val TEXT_ZOOM_100 = 100

        const val URL = "https://appassets.androidplatform.net/assets/tiptapcoverpaint/index.html"
        private const val METHOD_SET_BLOCK_QUOTE = "callSetBlockQuoteFromJava"

        fun with(context: Context): Builder {
            return Builder(context)
        }
    }
}