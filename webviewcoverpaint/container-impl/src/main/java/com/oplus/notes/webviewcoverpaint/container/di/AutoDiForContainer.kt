/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : AutoDiForContainer.kt
 * Description    : AutoDiForContainer.kt
 * Version        : 1.0
 * Date           : 2023/6/5
 * Author         : Chandler
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * Chandler     2023/6/5         1.0           create
 */
package com.oplus.notes.webviewcoverpaint.container.di

import com.oplus.notes.webviewcoverpaint.container.api.IUndoManager
import com.oplus.notes.webviewcoverpaint.container.api.IWebViewContainer
import com.oplus.notes.webviewcoverpaint.cache.api.IWebViewProxyCache
import com.oplus.notes.webviewcoverpaint.container.impl.WVUndoManager
import com.oplus.notes.webviewcoverpaint.cache.impl.WebViewProxyCacheImpl
import com.oplus.notes.webviewcoverpaint.container.api.IWebViewContainerCoverpaint
import com.oplus.notes.webviewcoverpaint.container.impl.WebViewContainerImpl
import org.koin.dsl.module

/**
 * Description : 注册Koin的类
 */
val webviewCoverpaintModule = module {
    factory<IWebViewContainerCoverpaint> { WebViewContainerImpl() }
    factory<IUndoManager> { WVUndoManager() }
    single<IWebViewProxyCache> { WebViewProxyCacheImpl() }
}
