/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: HtmlStandardParser.kt
 * Description: add head file
 *
 *
 * Version: 1.0
 * Date: 2024-04-16
 * Author: <PERSON><PERSON><PERSON>.<EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON>.<PERSON>@ROM.Apps.OppoNote                  2023-05-18    1.0     add head file
 **********************************************************************************/
package com.oplus.richtext.core.parser

import android.text.Layout
import android.text.Spanned
import android.text.TextUtils
import com.oplus.note.logger.AppLogger
import com.oplus.richtext.core.html.*
import com.oplus.richtext.core.node.IItemNode
import com.oplus.richtext.core.node.MediaNode
import com.oplus.richtext.core.spans.background.TextBackgroundColorSpan
import com.oplus.richtext.core.spans.checkbox.CheckBoxSpan
import com.oplus.richtext.core.utils.ColorConverter
import com.oplus.richtext.core.utils.Constants.ALIGN_SPAN_EMPTY_CHAR
import com.oplus.richtext.core.utils.Constants.AND
import com.oplus.richtext.core.utils.Constants.BULLET_SPAN_EMPTY_CHAR
import com.oplus.richtext.core.utils.Constants.CHAR_ALIGN_SPAN
import com.oplus.richtext.core.utils.Constants.CHAR_BULLET_SPAN
import com.oplus.richtext.core.utils.Constants.CHAR_NEWLINE
import com.oplus.richtext.core.utils.Constants.CHAR_NUMBER_SPAN
import com.oplus.richtext.core.utils.Constants.EMPTY_CHAR
import com.oplus.richtext.core.utils.Constants.GT
import com.oplus.richtext.core.utils.Constants.HTML_AND
import com.oplus.richtext.core.utils.Constants.HTML_GT
import com.oplus.richtext.core.utils.Constants.HTML_LT
import com.oplus.richtext.core.utils.Constants.HTML_WHITESPACE
import com.oplus.richtext.core.utils.Constants.LT
import com.oplus.richtext.core.utils.Constants.MAGIC_STRING
import com.oplus.richtext.core.utils.Constants.NUMBER_SPAN_EMPTY_CHAR
import com.oplus.richtext.core.utils.Constants.SOURCE_EMPTY_CHAR
import com.oplus.richtext.core.utils.Constants.WHITESPACE
import com.oplus.richtext.core.spans.*
import com.oplus.richtext.core.spans.AlignSpan
import com.oplus.richtext.core.spans.ICharacterSpan
import com.oplus.richtext.core.spans.IGroupSpan
import com.oplus.richtext.core.spans.IParagraphSpan
import com.oplus.richtext.core.spans.TextSizeSpan
import com.oplus.richtext.core.utils.notEmptySpan

object HtmlStandardParser {
    fun fromHtml(
        source: String,
        tagHandler: OplusTagHandler = OplusTagHandler(Html.FROM_HTML_MODE_COMPACT),
        shouldSkipTidying: Boolean = false,
        flag: Int = Html.FROM_HTML_MODE_COMPACT
    ): List<IItemNode<*>> {
        val tidySource = if (shouldSkipTidying) source else tidy(source)
        return Html.fromHtml(tidySource, tagHandler, null, flag)
    }

    private fun tidy(text: String): String {
        return text
            .replace(EMPTY_CHAR, "")
            .replace(NUMBER_SPAN_EMPTY_CHAR, "")
            .replace(BULLET_SPAN_EMPTY_CHAR, "")
            .replace(ALIGN_SPAN_EMPTY_CHAR, "")
            .replace(MAGIC_STRING, "")
    }


    private fun withinTitle(out: StringBuilder, text: Spanned) {
        val len = text.length
        var next: Int
        var i = 0
        do {
            next = text.nextSpanTransition(i, len, AlignSpan::class.java)
            val alignSpans = text.getSpans(i, next, AlignSpan::class.java)
            //对齐方式Span不会嵌套，取第一个span处理
            out.append("<${HtmlTags.HEAD}><${HtmlTags.TITLE}")
            if (alignSpans.isNotEmpty()) {
                alignSpans[0].also {
                    val textAlign = when (it.alignment) {
                        Layout.Alignment.ALIGN_OPPOSITE -> {
                            "${CssAttributes.TEXT_ALIGN}:${CssAttributes.Value.END};"
                        }
                        Layout.Alignment.ALIGN_CENTER -> {
                            "${CssAttributes.TEXT_ALIGN}:${CssAttributes.Value.CENTER};"
                        }
                        else -> {
                            "${CssAttributes.TEXT_ALIGN}:${CssAttributes.Value.START};"
                        }
                    }
                    out.append(" ${HtmlAttributes.STYLE}=\"$textAlign\">")
                }
            } else {
                out.append(">")
            }
            withinOplusParagraph(out, text, i, next)
            out.append("</${HtmlTags.TITLE}></${HtmlTags.HEAD}>")
            i = next
        } while (i < len)
    }

     fun withinImage(density: Float, out: StringBuilder, path: String?, width: Int, height: Int) {
        path?.let {
            out.append("<${HtmlTags.DIV} ${HtmlAttributes.CLASS}=\"${HtmlAttributes.Value.MEDIA}\">")
                .append("<${HtmlTags.IMG} ${HtmlAttributes.SRC}=\"$it\"")
                .append(" ${HtmlAttributes.WIDTH}=\"${(width / density).toInt()}\"")
                .append(" ${HtmlAttributes.HEIGHT}=\"${(height / density).toInt()}\"")
                .append("/>")
                .append("</${HtmlTags.DIV}>")
        }
    }

    fun withinSummary(out: StringBuilder, summary: String) {
        out.append("<${HtmlTags.DIV} ${HtmlAttributes.CLASS}=\"${HtmlAttributes.Value.SUMMARY}\">")
            .append(summary)
            .append("</${HtmlTags.DIV}>")
    }

    /**
     * 此方法将MediaNode转为最终html_text对应的格式，最后仅有图片附件及其他类型附件的占位图信息
     */
    @Suppress("MaximumLineLength")
    fun parseMediaNodeToHtmlText(out: StringBuilder, mediaNode: MediaNode) {
        val src = mediaNode.data
        out.append("<${HtmlTags.DIV} ${HtmlAttributes.CLASS}=\"${HtmlAttributes.Value.MEDIA}\">")
        when (mediaNode.type) {
            MediaNode.TYPE_AUDIO -> {
                out.append("<${HtmlTags.AUDIO} ${HtmlAttributes.SRC}=\"$src\"")
            }

            MediaNode.TYPE_VIDEO -> {
                out.append("<${HtmlTags.VIDEO} ${HtmlAttributes.SRC}=\"$src\"")
            }

            MediaNode.TYPE_IMAGE -> {
                out.append("<${HtmlTags.IMG} ${HtmlAttributes.SRC}=\"$src\"")
            }

            MediaNode.TYPE_FILE -> out.append("<${HtmlTags.IMG} ${HtmlAttributes.SRC}=\"$src\"")
        }
        if (mediaNode.width > 0) {
            out.append(" ${HtmlAttributes.WIDTH}=\"${mediaNode.width}\"")
        }
        if (mediaNode.height > 0) {
            out.append(" ${HtmlAttributes.HEIGHT}=\"${mediaNode.height}\"")
        }
        out.append("/></${HtmlTags.DIV}>")
    }

    /**
     * 此方法将MediaNode转为用于创建笔记的数据源，会携带附件原本信息
     */
    fun withinMediaNode(out: StringBuilder, mediaNode: MediaNode) {
        val src = mediaNode.data
        out.append("<${HtmlTags.DIV} ${HtmlAttributes.CLASS}=\"${HtmlAttributes.Value.MEDIA}\">")
        when (mediaNode.type) {
            MediaNode.TYPE_AUDIO -> {
                out.append("<${HtmlTags.AUDIO} ${HtmlAttributes.SRC}=\"$src\"")
            }

            MediaNode.TYPE_VIDEO -> {
                out.append("<${HtmlTags.VIDEO} ${HtmlAttributes.SRC}=\"$src\"")
            }

            MediaNode.TYPE_IMAGE ->
                out.append("<${HtmlTags.IMG} ${HtmlAttributes.SRC}=\"$src\" ${HtmlAttributes.ALT} =\"${mediaNode.alt}\"")

            MediaNode.TYPE_FILE -> {
                out.append("<${HtmlTags.INPUT} ${HtmlAttributes.TYPE}=\"file\" ${HtmlAttributes.SRC}=\"$src\" " +
                        "${HtmlAttributes.ALT} =\"${mediaNode.alt}\"")
            }
        }
        if (mediaNode.width > 0) {
            out.append(" ${HtmlAttributes.WIDTH}=\"${mediaNode.width}\"")
        }
        if (mediaNode.height > 0) {
            out.append(" ${HtmlAttributes.HEIGHT}=\"${mediaNode.height}\"")
        }
        out.append("/></${HtmlTags.DIV}>")
    }


    fun withCont(text: Spanned) :String{
        val out = StringBuilder()
        withinSpanned(out,text,false)
        return  out.toString()
    }
    fun withinSpanned(out: StringBuilder, text: Spanned, isAttachmentFollow: Boolean) {
        withinAlignment(out, text, isAttachmentFollow)
    }

    private fun withinAlignment(out: StringBuilder, text: Spanned, isAttachmentFollow: Boolean) {
        val len = text.length
        var next: Int
        var i = 0
        do {
            next = text.nextSpanTransition(i, len, AlignSpan::class.java)
            var alignSpans: Array<AlignSpan> = arrayOf()
            runCatching {
                /**
                 * getSpans内部可能出现ArrayIndexOutOfBoundsException
                 * 而此Crash通过外部的传参无法判断，因此通过try catch进行规避
                 */
                alignSpans = text.getSpans(i, next, AlignSpan::class.java)
            }.onFailure {
                AppLogger.BASIC.d("HtmlStandardParser", "withinAlignment e:$it")
            }
            //对齐方式Span不会嵌套，取第一个span处理
            val needDiv = alignSpans.isNotEmpty()
            if (needDiv) {
                alignSpans[0].also {
                    val textAlign = when (it.alignment) {
                        Layout.Alignment.ALIGN_OPPOSITE -> {
                            "${CssAttributes.TEXT_ALIGN}:${CssAttributes.Value.END};"
                        }
                        Layout.Alignment.ALIGN_CENTER -> {
                            "${CssAttributes.TEXT_ALIGN}:${CssAttributes.Value.CENTER};"
                        }
                        else -> {
                            "${CssAttributes.TEXT_ALIGN}:${CssAttributes.Value.START};"
                        }
                    }
                    out.append("<${it.startTag} ${HtmlAttributes.STYLE}=\"$textAlign\">")

                }
            }

            //isAttachmentFollow代表当前文本是否跟随附件，只作用于最后一个AlignSpan
            withinOplusParagraph(
                out, text, i, next, if (next == len) {
                    isAttachmentFollow
                } else {
                    false
                }
            )
            if (needDiv) {
                out.append("</${alignSpans[0].endTag}>")
            }
            i = next
        } while (i < len)
    }

    private fun withinOplusParagraph(out: StringBuilder,
                                     text: Spanned,
                                     start: Int,
                                     end: Int,
                                     isAttachmentFollow: Boolean = false) {
        var next: Int
        var i = start
        do {
            next = text.nextSpanTransition(i, end, OplusParagraphSpan::class.java)
            var paragraphSpans: Array<OplusParagraphSpan> = arrayOf()
            runCatching {
                paragraphSpans = text.getSpans(i, next, OplusParagraphSpan::class.java)
            }.onFailure {
                AppLogger.BASIC.d("HtmlStandardParser", "withinOplusParagraph e:$it")
            }
            val needDiv = paragraphSpans.isNotEmpty()
            if (needDiv) {
                out.append("<${HtmlTags.DIV}>")
            }

            //isAttachmentFollow代表当前文本是否跟随附件，只作用于最后一个OplusParagraphSpan
            withinGroup(
                out, text, i, next, if (next == text.length) {
                    isAttachmentFollow
                } else {
                    false
                }
            )
            if (needDiv) {
                out.append("</${HtmlTags.DIV}>")
            }
            i = next
        } while (i < end)
    }

    private fun withinGroup(out: StringBuilder,
                            text: Spanned,
                            start: Int,
                            end: Int,
                            isAttachmentFollow: Boolean) {
        var next: Int
        var i = start
        var hasGroup = false
        while (i < end) {
            next = text.nextSpanTransition(i, end, IGroupSpan::class.java)
            val groups = text.getSpans(i, next, IGroupSpan::class.java)
            if (groups.isNotEmpty()) {
                //由于有序列表，无序列表，待办不会互相嵌套，直接取第一个Span处理
                val group = groups[0]
                val startTag = "<${group.startTag}>"
                out.append(startTag)
                /** https://odocs.myoas.com/docs/Wr3DVZKEQNHnlWkJ
                 * 有 checkbox-group 但没有 item 时会导致文字丢失 */
                val hasChildItem = withinGroupItem(out, text, i, next)
                if (hasChildItem) {
                    out.append("</${group.endTag}>")
                    hasGroup = true
                } else {
                    out.delete(out.length - startTag.length, out.length)
                    withinContentCheckAttachment(
                        out, text, isAttachmentFollow, next, i, hasGroup
                    )
                    hasGroup = false
                }
            } else {
                // 首先判断文本前是否有段落级span
                // 若是，则判断开头字符是否是\n
                // 若开头字符为\n，则判断是否isAttachmentFollow是否为true，
                // 并且text[i]是否是文本的最后一个字符,
                // 若是，说明段落级span之后\n后添加了图片或者表格等附件或者什么都没有，这时保留最后的\n，否则删除
                /*val needRemoveNewLine = hasGroup && text[i] == '\n'
                        && (!isAttachmentFollow || i != text.length - 1)
                withinContent(
                    out, text, if (needRemoveNewLine) {
                        i + 1
                    } else {
                        i
                    }, next
                )*/
                // 若普通文本前有段落级样式，并且起始位置为\n，则删除\n
                withinContentCheckAttachment(
                    out, text, isAttachmentFollow, next, i, hasGroup
                )
                hasGroup = false
            }
            i = next
        }
    }

    private fun withinContentCheckAttachment(
        out: StringBuilder,
        text: Spanned,
        isAttachmentFollow: Boolean,
        next: Int,
        i: Int,
        hasGroup: Boolean
    ) {
        withinContent(
            out, text, if (hasGroup && text[i] == '\n') {
                i + 1
            } else {
                i
            }, next
        )
        // 若该段文本后有附件或者段落级样式后的普通文本只是\n，则添加br标签
        if ((isAttachmentFollow && next == text.length)
            || (hasGroup && i == text.length - 1 && text[i] == '\n')) {
            out.append("<${HtmlTags.BR}>")
        }
    }

    private fun withinGroupItem(out: StringBuilder, text: Spanned, start: Int, end: Int): Boolean {
        var next: Int
        var i = start
        var hasChildItem = false
        while (i < end) {
            next = text.nextSpanTransition(i, end, IParagraphSpan::class.java)
            val items = text.getSpans(i, next, IParagraphSpan::class.java)
            if (items.isNotEmpty()) {
                hasChildItem = true
                //由于有序列表，无序列表，待办的子项不会互相嵌套，取第一个Span处理
                val item = items[0]
                out.append("<${item.startTag}>")
                val spans = text.getSpans(i, next, UnknownSpan::class.java)
                if (spans.any { it.customSplitTag }) {
                    out.insert(out.length - 1, " data-customsplit=\"nestlist\"")
                }
                //如果是待办列表，加一层label标签方便解析
                if (item is CheckBoxSpan) {
                    out.append("<${HtmlTags.LABEL} ${HtmlAttributes.CLASS}")
                        .append("=\"")
                        .append(HtmlAttributes.Value.CHECKBOX_TEXT)
                        .append("\">")
                }
                withinContent(out, text, i, next)
                if (item is CheckBoxSpan) {
                    out.append("</${HtmlTags.LABEL}>")
                }
                out.append("</${item.endTag}>")
            }
            i = next
        }
        return hasChildItem
    }

    private fun withinContent(out: StringBuilder, text: Spanned, start: Int, end: Int) {
        var next: Int
        var i = start
        while (i < end) {
            next = TextUtils.indexOf(text, CHAR_NEWLINE, i, end)
            if (next < 0) {
                next = end
            }

            var nl = 0

            while (next < end && text[next] == CHAR_NEWLINE) {
                nl++
                next++
            }

            withinParagraph(out, text, i, next - nl)
            if (nl == 1) {
                out.append("<${HtmlTags.BR}>")
            } else {
                for (j in 0 until nl) {
                    out.append("<${HtmlTags.BR}>")
                }
            }
            i = next
        }
    }

    private fun withinParagraph(out: StringBuilder, text: Spanned, start: Int, end: Int) {
        var next: Int
        var i = start
        while (i < end || start == end) {
            next = text.nextSpanTransition(i, end, ICharacterSpan::class.java)

            if (i == next)
                break

            val spans = text.getSpans(i, next, ICharacterSpan::class.java)

            for (span in spans) {
                text.notEmptySpan(span) {
                    when (span) {
                        is TextBackgroundColorSpan -> {
                            parseBackgroundSpan(true, out, span)
                        }
                        is TextSizeSpan -> {
                            parseTextSizeSpan(true, out, span)
                        }
                        else -> {
                            out.append("<${span.startTag}>")

                        }
                    }
                }
            }

            withinStyle(out, text, i, next)

            for (span in spans.reversed()) {
                text.notEmptySpan(span) {
                    when (span) {
                        is TextBackgroundColorSpan -> {
                            parseBackgroundSpan(false, out, span)
                        }
                        is TextSizeSpan -> {
                            parseTextSizeSpan(false, out, span)
                        }
                        else -> {
                            out.append("</${span.endTag}>")
                        }
                    }
                }
            }

            if (start == end)
                break

            i = next
        }
    }

    private fun parseTextSizeSpan(
        opening: Boolean,
        out: StringBuilder,
        span: TextSizeSpan
    ) {
        if (opening) {
            val size = span.getValue()
            out.append("<${HtmlTags.SPAN} ${HtmlAttributes.STYLE}=\"")
                .append(CssAttributes.FONT_SIZE)
                .append(":")
                .append(size)
                .append("em\"")
                .append(">")
        } else {
            out.append("</")
                .append(HtmlTags.SPAN)
                .append(">")
        }
    }

    private fun parseBackgroundSpan(
        opening: Boolean,
        out: StringBuilder,
        span: TextBackgroundColorSpan
    ) {
        if (opening) {
            val htmlColor = ColorConverter.colorToRGBA(span.getValue() as Int)
            out.append("<${HtmlTags.SPAN} ${HtmlAttributes.STYLE}=\"")
                .append(CssAttributes.BACKGROUND_COLOR)
                .append(":")
                .append(htmlColor)
                .append("\"")
                .append(">")
        } else {
            out.append("</")
                .append(HtmlTags.SPAN)
                .append(">")
        }
    }

    private fun withinStyle(out: StringBuilder, text: Spanned, start: Int, end: Int) {
        var i = start
        while (i < end) {
            val c = text[i]
            if (c == CHAR_NUMBER_SPAN ||
                c == SOURCE_EMPTY_CHAR ||
                c == CHAR_BULLET_SPAN ||
                c == CHAR_ALIGN_SPAN
            ) {
                i++
                continue
            }

            when {
                c == LT -> {
                    out.append(HTML_LT)
                }
                c == GT -> {
                    out.append(HTML_GT)
                }
                c == AND -> {
                    out.append(HTML_AND)
                }
                c == WHITESPACE -> {
                    while (i + 1 < end && text[i + 1] == WHITESPACE) {
                        out.append(HTML_WHITESPACE)
                        i++
                    }

                    out.append(WHITESPACE)
                }
                c != CHAR_NEWLINE -> {
                    out.append(c)
                }
            }
            i++
        }
    }

}