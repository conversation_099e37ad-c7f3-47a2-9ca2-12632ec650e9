/****************************************************************
 * * Copyright (C), 2020-2028, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: WordForegroundColorSpan
 * * Description: WordForegroundColorSpan
 * * Version: 1.0
 * * Date: 2020/4/8
 * * Author: 80242942
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * 80242942 2020/4/8 1.0 build this module
 ****************************************************************/
package com.oplus.richtext.core.spans.background

import com.oplus.richtext.core.R
import com.oplus.richtext.core.spans.ICharacterSpan
import com.oplus.richtext.core.spans.IStyleSpan
import com.oplus.richtext.core.utils.RichUiHelper
import com.oplus.richtext.core.html.HtmlTags
import com.oplus.richtext.core.html.OplusAttributes

class TextBackgroundColorSpan(
    private val color: Int = DEFAULT_BACKGROUND_COLOR,
    override val tag: String = HtmlTags.SPAN,
    override var attributes: OplusAttributes = OplusAttributes()
) : ICharacterSpan {

    override val priority: Int = 4

    override fun getValue(): Any {
        return color
    }

    override fun clone(): IStyleSpan {
        return TextBackgroundColorSpan()
    }

    companion object {
        const val DEFAULT_BACKGROUND_COLOR: Int = 0x80FFF049.toInt()
        fun getBackgroundColor(): Int {
            return RichUiHelper.mContext?.getColor(R.color.highlight_color)
                ?: DEFAULT_BACKGROUND_COLOR
        }
    }
}