/****************************************************************
 * * Copyright (C), 2019-2027, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: HtmlFormats.kt
 * * Description: HtmlFormats
 * * Version: 1.0
 * * Date: 2022/11/17
 * * Author: xushuya
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 ****************************************************************/
package com.oplus.richtext.core.parser

object HtmlFormats {
    const val HTML = "html"
    const val BODY = "body"
    const val DIV = "div"
    const val BR = "br"
    const val UL = "ul"
    const val LI = "li"
    const val P = "p"
    const val H1 = "h1"
    const val H2 = "h2"
    const val H3 = "h3"
    const  val H4 = "h4"
    const val H5 = "h5"
    const val H6 = "h6"
    const val S = "s"
    const val B = "b"
    const val I = "i"
    const val U = "u"
    const val DEL = "del"
    const val BLOCKQUOTE = "blockquote"
    const val TABLE = "table"
    const val TR = "tr"
    const val TD = "td"
    const val OL = "ol"

    const val IMG = "img"
    const val VIDEO = "video"
    const val TEXT = "#text"

    const val SPAN = "span"
    const val SPACE = " "
    const val TEXT_INDENT = "text-indent"
    const val CARD = "card"
    const val CONTACT_CARD = "contactcard"
    const val FILE_CARD = "filecard"
    const val RECORD = "record"
    const val SCHEDULE_CARD = "schedulecard"

    const val END_SPAN_DIV = "</span></div>"
    const val END_SPAN = "</span>"
    const val END_LI = "</li>"
    const val END_OL = "</ol>"
    const val END_UL = "</ul>"
    const val END_DIV = "</div>"
    const val BR_TAG = "<br>"
    const val START_TABLE = "<table>"
    const val END_TABLE = "</table>"
    const val START_TR = "<tr>"
    const val END_TR = "</tr>"
    const val START_TD = "<td>"
    const val END_TD = "</td>"


    //    const val END_TD=""
    const val START_SPAN = "<span class=\""
    const val START_LI = "<li>"
    const val START_OL = "<ol>"
    const val START_UL_LINE = "<ul class=\"list-style-line\">"
    const val START_UL = "<ul>"
    const val START_DIV = "<div>"
    const val START_BR = "<br>"
    const val START_LI_UNCHECK = "<li class=\"unchecked\">"
    const val START_LI_CHECKED = "<li class=\"checked\">"


    const val H1_SIZE = "1.25"
    const val H2_SIZE = "1.125"
    const val H3_SIZE = "1.0625"
    private const val H4_SIZE = "1.0"
    private const val H5_SIZE = "0.875"
    private const val H6_SIZE = "0.875"


    fun getSize(size: String?): String? {
        var textSize = ""
        when (size) {
            H1 -> textSize = H1_SIZE
            H2 -> textSize = H2_SIZE
            H3 -> textSize = H3_SIZE
            H4 -> textSize = H4_SIZE
            H5 -> textSize = H5_SIZE
            H6 -> textSize = H6_SIZE
            else -> {}
        }
        return textSize
    }
}