<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:drawable="@drawable/toolbar_item_background_4_corners_disable" android:state_enabled="false" />
    <item android:drawable="@drawable/toolbar_item_background_4_corners_activated_pressed" android:state_activated="true" android:state_pressed="true" />
    <item android:drawable="@drawable/toolbar_item_background_4_corners_activated" android:state_activated="true" />
    <item android:drawable="@drawable/toolbar_item_background_4_corners_normal_press" android:state_pressed="true"/>
    <item android:drawable="@drawable/toolbar_item_background_4_corners_normal" />
</selector>