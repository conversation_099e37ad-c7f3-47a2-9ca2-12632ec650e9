package com.oplus.richtext.editor.utils

import android.text.Spanned
import com.oplus.richtext.core.utils.Constants

fun String.getSpanCharsCount(): Int {
    var count = 0
    this.toCharArray().filterNot(Char::isWhitespace).groupBy{ it }.map {
        if (it.key in Constants.SPECIAL_CHARS) {
            count += it.value.size
        }
    }
    return count
}

inline fun Spanned.notEmptySpan(any: Any?, block: () -> Unit) {
    any?.let {
        if (this.getSpanStart(it) != this.getSpanEnd(it)) {
            block()
        }
    }
}