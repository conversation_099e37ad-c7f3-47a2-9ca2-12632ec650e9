/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/10/21
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9060       2023/10/21      1.0     create file
 ****************************************************************/
package com.oplus.richtext.editor.utils

import android.content.Context
import android.graphics.Bitmap
import android.os.Build
import android.text.Spannable
import android.text.Spanned
import android.text.TextUtils
import androidx.core.graphics.drawable.toBitmap
import androidx.core.text.toSpannable
import androidx.vectordrawable.graphics.drawable.VectorDrawableCompat
import com.oplus.note.data.Constants
import com.oplus.note.data.ThirdLogAIMark
import com.oplus.note.data.ThirdLogMark
import com.oplus.note.data.ThirdLogMarks
import com.oplus.note.data.third.ThirdLogParagraph
import com.oplus.note.logger.AppLogger
import com.oplus.richtext.core.html.HtmlTags
import com.oplus.richtext.core.html.OplusAttributes
import com.oplus.richtext.core.spans.NoteURLSpan
import com.oplus.richtext.core.spans.TopAlignImageSpan
import com.oplus.richtext.core.utils.RichUiHelper
import com.oplus.richtext.editor.R
import java.util.regex.Pattern

object MarkUtils {

    private const val TAG = "MarkUtils"

    class MarkSpec {
        var type: String? = null
        var content: String? = null
        var start = 0
        var end = 0
    }

    /**
     * 详情页操作实体不需要调用此方法  通话记录页需要调用该方法配置自动标记
     */
    @JvmStatic
    fun addMarks(
        context: Context?,
        thirdLogParagraph: ThirdLogParagraph,
        text: CharSequence,
        thirdLogMarks: ThirdLogMarks?,
        isBlod: Boolean = false
    ) {
        if (TextUtils.isEmpty(text)) {
            thirdLogParagraph.paragraphSpannable = null
            return
        }
        if (thirdLogMarks?.aiMarkList == null) {
            thirdLogParagraph.paragraphSpannable = null
            return
        }
        var replace = text
        val markSpecs = ArrayList<MarkSpec>()
        var spannable: Spannable? = null
        //先按AI标记的时间和段落的时间比对 相同的情况下，再按字符串比较内容。
        val filter = thirdLogMarks.aiMarkList?.filter {
            checkMarkTimeInParagraphTime(it, thirdLogParagraph)
        }
        filter?.forEach { mark ->
            replace = replace.toString().replace("${mark.content}", "${mark.content}${Constants.IMG_SPAN_FLAG_TAG}")
        }
        if (!replace.toString().contains(Constants.IMG_SPAN_FLAG_TAG)) {
            thirdLogParagraph.paragraphSpannable = null
            return
        }
        spannable = replace.toSpannable()
        filter?.forEach { mark ->
            summaryMarks(mark, spannable, markSpecs)
        }
        applyMarks(context, spannable, markSpecs, isBlod = isBlod)
        thirdLogParagraph.paragraphSpannable = spannable
    }

    @JvmStatic
    @Suppress("PatternPreCompileRule")
    private fun summaryMarks(
        mark: ThirdLogAIMark,
        s: Spannable,
        marks: MutableList<MarkSpec>
    ) {
        kotlin.runCatching {
            val pattern = Pattern.compile(mark.content + Constants.IMG_SPAN_FLAG_TAG, Pattern.LITERAL)
            val matcher = pattern.matcher(s)
            while (matcher.find()) {
                val start = matcher.start()
                val end = matcher.end()
                val markSpec = MarkSpec()
                markSpec.content = mark.content
                markSpec.start = start
                markSpec.end = end
                marks.add(markSpec)
            }
        }.onFailure {
            AppLogger.BASIC.e(TAG, "summaryMarks error.", it)
        }
    }

    @JvmStatic
    private fun applyMarks(
        context: Context?,
        text: Spannable?,
        marks: List<MarkSpec>,
        isBlod: Boolean = false,
    ) {
        for (mark in marks) {
            applyMark(context, text, mark, isBlod = isBlod)
        }
    }

    @JvmStatic
    private fun applyMark(
        context: Context?,
        text: Spannable?,
        spec: MarkSpec,
        isBlod: Boolean = false
    ) {
        val start = spec.start
        val end = spec.end
        if ((context == null) || TextUtils.isEmpty(spec.content) || (null == text)) {
            AppLogger.NOTE.e(TAG, "context is $context content is null ${TextUtils.isEmpty(spec.content)} text is null ${null == text}")
            return
        }
        if ((start < 0) || (end < 0)) {
            AppLogger.NOTE.e(TAG, "applyMark input param error !")
            return
        }
        if ((end < start) || (start > text.length) || (end > text.length)) {
            AppLogger.NOTE.e(TAG, "applyMark input param error !")
            return
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            changeVectorImageColorBitmap(R.drawable.speech_black_mark, context)?.let {
                val span = TopAlignImageSpan(context, it)
                text.setSpan(
                    span,
                    start + (spec.content?.length ?: 0),
                    end,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
            if (isBlod) {
                val span = NoteURLSpan("", HtmlTags.A, OplusAttributes(), jumpAble = false)
                text.setSpan(
                    span,
                    start,
                    (end - 1),
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }
    }

    /**
     *  检测标记时间与段落时间
     */
    @JvmStatic
    fun checkMarkTimeInParagraphTime(
        mark: ThirdLogMark,
        thirdLogParagraph: ThirdLogParagraph
    ): Boolean {
        return (mark.showTime == thirdLogParagraph.showTime)
    }

    /**
     * 创建主题色红旗bitmap
     */
    @JvmStatic
    private fun changeVectorImageColorBitmap(id: Int, context: Context): Bitmap? {
        val drawableCompat =
            VectorDrawableCompat.create(context.resources, id, null)
        RichUiHelper.linkColor?.let { color ->
            drawableCompat?.setTint(color)
        }
        return drawableCompat?.toBitmap()
    }

    /**
     * 创建主题色红旗drawable
     */
    @JvmStatic
    fun changeVectorImageColorDrawable(
        id: Int,
        colorId: Int?,
        context: Context
    ): VectorDrawableCompat? {
        val drawableCompat =
            VectorDrawableCompat.create(context.resources, id, null)
        colorId?.let { color ->
            drawableCompat?.setTint(color)
        }
        return drawableCompat
    }
}