package com.oplus.richtext.editor.view.toolbar.content

import android.view.ViewGroup
import android.view.ViewStub
import androidx.core.view.WindowInsetsControllerCompat
import com.oplus.note.edgeToEdge.EdgeToEdgeManager
import com.oplus.note.logger.AppLogger
import com.oplus.richtext.editor.view.toolbar.ToolbarContract
import com.oplus.richtext.editor.view.toolbar.ToolbarState
import com.oplus.richtext.editor.view.toolbar.callback.IToolbarCallback
import com.oplus.richtext.editor.view.toolbar.insets.WindowInsetsCallback
import com.oplus.richtext.editor.view.toolbar.view.AbsToolbarView

abstract class AbsToolbar : WindowInsetsCallback.OnInsetsCallback {

    var navigationHeight = 0
    var toolBarType = ToolbarContract.NULL_TYPE
    var toolbarUiMode = ToolbarContract.UiMode()
    var imeHeight = ToolbarContract.AnimationContract.INVALID_IME_HEIGHT
    var imeVisible = false
    var imeShowHeight = ToolbarContract.AnimationContract.INVALID_IME_HEIGHT
    var toolbarHeightChangeListener: ((height: Int) -> Unit)? = null
    var titleEnableListener: ((isTitle: Boolean) -> Unit)? = null
    var isInTable: Boolean = false

    /**
     * 移植原来的代码，RichUiHelper中相同的变量。用于标识当前正在尝试显示软键盘。
     * 即调用showSoftInput方法后，到软键盘开始弹出这段时间内为true。
     */
    var isShowingSoftInput = false
        set(value) {
            AppLogger.BASIC.d(tag(), "Set isShowingSoftInput to $value")
            field = value
        }
    protected var toolbarCallback: IToolbarCallback? = null
    protected var imeEndListener: ((isImeVisible: Boolean) -> Unit)? = null
    protected var imeResetListener: ((isImeVisible: Boolean) -> Unit)? = null
    private lateinit var windowInsetsCallback: WindowInsetsCallback

    fun setWindowCallback() {
        windowInsetsCallback = WindowInsetsCallback()
        EdgeToEdgeManager.observeInsetsAnimationCallback(getToolbarContainer(), windowInsetsCallback)
        EdgeToEdgeManager.observeOnApplyWindowInsets(getToolbarContainer(), windowInsetsCallback)
        windowInsetsCallback.setInsetsCallback(this)
    }

    fun initToolbarCallback(tCallback: IToolbarCallback) {
        toolbarCallback = tCallback
        getToolbarView().initToolbarCallback(tCallback)
    }

    fun setImeEndCallBack(imeEndListener: ((isImeVisible: Boolean) -> Unit)?) {
        this.imeEndListener = imeEndListener
    }

    fun setImeResetCallBack(imeResetListener: ((isImeVisible: Boolean) -> Unit)?) {
        this.imeResetListener = imeResetListener
    }

    abstract fun initToolBarView(view: ViewGroup): AbsToolbar

    /**
     * 正在展示添加面板
     */
    abstract fun isShowToolbarExtraPanel(): Boolean

    /**
     * 初始化添加面板
     */
    abstract fun initToolbarExtraPanel(stub: ViewStub?)

    abstract fun getToolbarView(): AbsToolbarView

    abstract fun getToolbarContainer(): ViewGroup

    /**
     * 获取工具栏高度
     */
    abstract fun getToolbarHeight(): Int

    abstract fun isRichTextPanelShow(): Boolean

    abstract fun allowShowSoftInput(): Boolean

    abstract fun hideSoftInput()

    abstract fun showSoftInput()

    /**
     * 设置工具栏样式
     * @see [ToolbarContract.UiMode]
     */
    abstract fun setToolbarUiModeType(mode: Int)

    /**
     * 获取工具栏样式
     * @see [ToolbarContract.UiMode]
     */
    abstract fun getToolbarUiModeType(): Int

    abstract fun setImeVisiblie(imeVisible: Boolean)

    /**
     * 设置布局的边界位置
     */
    abstract fun setPadding(deviceType: Int)

    open fun updateSpecialState(state: ToolbarState) {
        AppLogger.BASIC.d(tag(), "updateSpecialState: $state")
    }

    abstract fun tag(): String

    /**
     * 内容的适配方法
     */
    abstract fun resetContentSearchState()

    /**
     * 折叠屏父子级时，工具栏要比原来高8dp 为56dp
     */
    abstract fun updateHeight(isTwoPane: Boolean)

    /**
     * 大屏精品化当左右分屏时更新高度为56dp
     */
    abstract fun updateHeightInCallDetail(shouldHide: Boolean)
}