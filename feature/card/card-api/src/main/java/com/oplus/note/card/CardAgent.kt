/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: CardAgent.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/10/01
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.card

import android.content.Context

interface CardAgent {

    /**
     * 刷新卡片
     */
    suspend fun refresh(context: Context, parameter: RefreshParameter)

    /**
     * 获取卡片id
     */
    fun getCardId(): String

    /**
     * 获取卡片尺寸
     */
    fun getCardSize(): String

    /**
     * 匹对卡片
     */
    fun equalsCard(id: String): Boolean

    /**
     * 获取卡片类型
     */
    fun getCardType(): CardType
}

enum class CardType {
    /**
     * 速览卡
     */
    ASSISTANT,

    /**
     * 泛在卡
     */
    SEEDING
}