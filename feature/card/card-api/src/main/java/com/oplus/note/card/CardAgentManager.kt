/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: CardAgentManager.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/10/01
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.card

import android.content.Context
import com.oplus.note.logger.AppLogger
import org.json.JSONObject

object CardAgentManager {
    private const val TAG = "CardAgentManager"

    fun postIntention(context: Context, eventCode: Int, event: String, data: JSONObject, cardType: AgentType) {
        val agent = CardAgentFactory.getIntentionAgents().find { it.getAgentType() == cardType }
        if (agent == null) {
            AppLogger.BASIC.d(TAG, "postIntention agent is null")
        } else {
            agent.postIntention(context, eventCode, event, data)
        }
    }

    suspend fun refresh(context: Context, parameter: RefreshParameter) {
        CardAgentFactory.getAgents().forEach { it.refresh(context, parameter) }
    }

    fun getCardAgent(id: String): CardAgent? {
        return CardAgentFactory.getAgents().find {
            it.equalsCard(id)
        }
    }

    fun registerCardAgent(agent: CardAgent) {
        CardAgentFactory.register(agent)
    }

    fun registerCardAgent(agent: IntentionAgent) {
        CardAgentFactory.register(agent)
    }

    fun unregisterCardAgent(agent: CardAgent?) {
        if (agent != null) {
            CardAgentFactory.unregister(agent)
        }
    }

    fun unregisterCardAgent(agent: IntentionAgent?) {
        if (agent != null) {
            CardAgentFactory.unregister(agent)
        }
    }
}