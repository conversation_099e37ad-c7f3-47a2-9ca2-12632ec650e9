/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: SnackBarManager.kt
 * Description: Manager for SnackBar
 *
 *
 * Version: 1.0
 * Date: 2023-02-08
 * Author: W9013986
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9013986                       2023-02-08    1.0    Create this module
 * W9005794                       2023-05-19    2.0    Update
 **********************************************************************************/
package com.oplus.note.aigraffiti.util

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.core.view.WindowInsetsCompat
import com.coui.appcompat.snackbar.COUISnackBar
import com.nearme.note.view.LottieIconSnackBar
import com.oplus.note.aigraffiti.R

internal object SnackBarManager {

    const val DURATION_DEFAULT = 5000

    fun showCOUISnackBar(
        context: Context,
        params: SnackBarParams,
        actionText: String? = null,
        snackBarTextDirection: Int = -1,
        listener: View.OnClickListener? = null
    ) {
        // 指定marginBottom的情况下使用当前指定的margin，否则使用默认的margin
        if (params.marginBottom > 0) {
            createCOUISnackBar(context, params, 0).apply {
                actionText?.let {
                    this.setOnAction(it, listener)
                }
                if (snackBarTextDirection >= 0) {
                    this.textDirection = snackBarTextDirection
                }
            }.show()
        } else {
            if (params.view.rootWindowInsets == null) {
                return
            }
            val naviBarInsetsBottom = WindowInsetsCompat.toWindowInsetsCompat(params.view.rootWindowInsets)
                .getInsets(WindowInsetsCompat.Type.navigationBars()).bottom
            createCOUISnackBar(context, params, naviBarInsetsBottom).apply {
                actionText?.let {
                    this.setOnAction(it, listener)
                }
                if (snackBarTextDirection >= 0) {
                    this.textDirection = snackBarTextDirection
                }
            }.show()
        }
    }

    /**
     * 创建COUI的SnackBar
     */
    internal fun createCOUISnackBar(
        context: Context,
        params: SnackBarParams,
        naviBarHeight: Int
    ): COUISnackBar {
        return with(params) {
            if (marginBottom > 0) {
                COUISnackBar.make(view, text, duration, marginBottom)
            } else {
                val defaultMargin = context.resources.getDimensionPixelSize(R.dimen.dp_88)
                if (naviBarHeight > 0) {
                    COUISnackBar.make(view, text, duration, defaultMargin + naviBarHeight)
                } else {
                    COUISnackBar.make(view, text, duration, defaultMargin)
                }
            }
        }
    }

    fun showAIGraffitiSnackBar(
        context: Context,
        params: SnackBarParams,
        actionText: String,
        statusListener: COUISnackBar.OnStatusChangeListener,
        listener: View.OnClickListener
    ): COUISnackBar {
        val snackBar = createSnackBar(context, params, 0).apply {
            actionText.let {
                this.setOnAction(it, listener)
            }
            this.setOnStatusChangeListener(statusListener)
            this.showLottieIcon(R.raw.ai_generating)
            contentView.updateMarginLayoutParams {
                marginStart = params.view.context.resources.getDimensionPixelSize(R.dimen.dp_6)
            }
            actionView.invalidate()
            setDismissWithoutAnimate(true)
            show()
        }
        return snackBar
    }

    /**
     * 创建动效icon的Snackbar
     */
    private fun createSnackBar(
        context: Context,
        params: SnackBarParams,
        naviBarHeight: Int
    ): LottieIconSnackBar {
        return with(params) {
            if (marginBottom > 0) {
                LottieIconSnackBar.make(view, text, duration, marginBottom)
            } else {
                val defaultMargin = context.resources.getDimensionPixelSize(R.dimen.dp_88)
                if (naviBarHeight > 0) {
                    LottieIconSnackBar.make(view, text, duration, defaultMargin + naviBarHeight)
                } else {
                    LottieIconSnackBar.make(view, text, duration, defaultMargin)
                }
            }
        }
    }

    /**
     * Executes [block] with the View's margin layoutParams and reassigns the layoutParams with the
     * updated version.
     *
     * @see View.getLayoutParams
     * @see View.setLayoutParams
     **/
    inline fun View.updateMarginLayoutParams(block: ViewGroup.MarginLayoutParams.() -> Unit) {
        updateMarginLayoutParams<ViewGroup.MarginLayoutParams>(block)
    }

    /**
     * Executes [block] with a typed version of the View's margin layoutParams and reassigns the
     * layoutParams with the updated version.
     *
     * @see View.getLayoutParams
     * @see View.setLayoutParams
     **/
    @JvmName("updateMarginLayoutParamsTyped")
    inline fun <reified T : ViewGroup.MarginLayoutParams> View.updateMarginLayoutParams(
        block: T.() -> Unit
    ) {
        val params = layoutParams
        if (params is T) {
            block(params)
            layoutParams = params
        }
    }
}

data class SnackBarParams(
    val view: View,
    val text: String,
    val duration: Int = SnackBarManager.DURATION_DEFAULT,
    val marginBottom: Int = -1
)