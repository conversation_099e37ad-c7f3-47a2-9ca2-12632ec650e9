/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: NoteBookData.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/08/08
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook.internal

object NoteBookData {
    // NOTE: 以下封面id与图片资源一一对应，且对应埋点信息，非设计明确要求，不要修改id与图片对应关系
    private const val IMG_COVER_DEFAULT = "img_cover_default" // 纯色封面浅灰色
    private const val IMG_COVER_1 = "img_cover_1" // 纯色封面原木色
    private const val IMG_COVER_2 = "img_cover_2" // 渐变封面黄色
    private const val IMG_COVER_3 = "img_cover_3" // 纯色封面黑色
    private const val IMG_COVER_4 = "img_cover_4" // 图形封面字母N
    private const val IMG_COVER_5 = "img_cover_5" // 图形封面水蜜桃
    private const val IMG_COVER_6 = "img_cover_6" // 图形封面不规则图形
    private const val IMG_COVER_7 = "img_cover_7" // 图形封面丧脸
    private const val IMG_COVER_8 = "img_cover_8" // 插画封面染发灰头的少女
    private const val IMG_COVER_9 = "img_cover_9" // 插画封面盖粉被的男人
    private const val IMG_COVER_10 = "img_cover_10" // 插画封面小怪兽

    private const val IMG_COVER_11 = "img_cover_11" // 纯色封面蓝色
    private const val IMG_COVER_12 = "img_cover_12" // 纯色封面深灰色
    private const val IMG_COVER_13 = "img_cover_13" // 插画封面彩虹书
    private const val IMG_COVER_14 = "img_cover_14" // 插画封面带珍珠耳环的少女
    private const val IMG_COVER_15 = "img_cover_15" // 图形封面XO鬼脸
    private const val IMG_COVER_16 = "img_cover_16" // 图形封面艺术眼睛粉
    private const val IMG_COVER_17 = "img_cover_17" // 图形封面艺术眼睛黑
    private const val IMG_COVER_18 = "img_cover_18" // 图形封面艺术圈蓝
    private const val IMG_COVER_19 = "img_cover_19" // 纯色封面红色
    private const val IMG_COVER_20 = "img_cover_20" // 渐变封面蓝色
    private const val IMG_COVER_21 = "img_cover_21" // 渐变封面红色
    private const val IMG_COVER_22 = "img_cover_22" // 渐变封面粉红色
    private const val IMG_COVER_23 = "img_cover_23" // 渐变封面水生蓝色
    private const val IMG_COVER_24 = "img_cover_24" // 渐变封面水生红色

    const val IMG_COVER_PURE_YELLOW = "img_cover_yellow" // 黄
    const val IMG_COVER_PURE_ORANGE = "img_cover_orange" // 橙
    const val IMG_COVER_PURE_RED = "img_cover_red" // 红
    const val IMG_COVER_PURE_GREEN = "img_cover_green" // 绿
    const val IMG_COVER_PURE_AZURE = "img_cover_azure" // 浅蓝
    const val IMG_COVER_PURE_GREY = "img_cover_grey" // 灰
    const val IMG_COVER_PURE_BROWN = "img_cover_brown" // 棕

    val imageList = ArrayList<String>()
    private val pureColorList = ArrayList<String>()    // 纯色
    private val gradientList = ArrayList<String>()     // 渐变
    private val graphicalList = ArrayList<String>()    // 图形
    private val illustrationList = ArrayList<String>() // 插画

    val oldList = ArrayList<String>()

    init {
        initWithDefault()
    }

    private fun initWithDefault() {
        pureColorList.add(IMG_COVER_11)
        pureColorList.add(IMG_COVER_DEFAULT)
        pureColorList.add(IMG_COVER_1)
        pureColorList.add(IMG_COVER_12)
        pureColorList.add(IMG_COVER_3)
        pureColorList.add(IMG_COVER_19)

        gradientList.add(IMG_COVER_20)
        gradientList.add(IMG_COVER_21)
        gradientList.add(IMG_COVER_22)
        gradientList.add(IMG_COVER_2)
        gradientList.add(IMG_COVER_23)
        gradientList.add(IMG_COVER_24)

        graphicalList.add(IMG_COVER_6)
        graphicalList.add(IMG_COVER_7)
        graphicalList.add(IMG_COVER_5)
        graphicalList.add(IMG_COVER_4)
        graphicalList.add(IMG_COVER_15)
        graphicalList.add(IMG_COVER_17)
        graphicalList.add(IMG_COVER_16)
        graphicalList.add(IMG_COVER_18)

        illustrationList.add(IMG_COVER_10)
        illustrationList.add(IMG_COVER_13)
        illustrationList.add(IMG_COVER_14)
        illustrationList.add(IMG_COVER_8)
        illustrationList.add(IMG_COVER_9)

        imageList.addAll(pureColorList)
        imageList.addAll(gradientList)
        imageList.addAll(graphicalList)
        imageList.addAll(illustrationList)

        oldList.add(IMG_COVER_DEFAULT)
        oldList.add(IMG_COVER_1)
        oldList.add(IMG_COVER_2)
        oldList.add(IMG_COVER_3)
        oldList.add(IMG_COVER_4)
        oldList.add(IMG_COVER_5)
        oldList.add(IMG_COVER_6)
        oldList.add(IMG_COVER_7)
        oldList.add(IMG_COVER_8)
        oldList.add(IMG_COVER_9)
        oldList.add(IMG_COVER_10)
    }

    fun getAllNoteDefaultCover(): String {
        return IMG_COVER_11
    }

    fun getCollectionNoteDefaultCover(): String {
        return IMG_COVER_PURE_ORANGE
    }

    fun getDefaultPureCover(): String {
        return IMG_COVER_PURE_YELLOW
    }

    fun getDefaultCover(): String {
        return IMG_COVER_DEFAULT
    }

    fun getLifeCover(): String {
        return IMG_COVER_10
    }

    fun getWorkCover(): String {
        return IMG_COVER_2
    }

    fun getStudyCover(): String {
        return IMG_COVER_3
    }

    fun isOldCover(cover: String?): Boolean {
        return (cover != null) && oldList.contains(cover)
    }

    fun isPureColorStart(cover: String): Boolean {
        return cover == pureColorList[0]
    }

    fun isGradientListStart(cover: String): Boolean {
        return cover == gradientList[0]
    }

    fun isGraphicalListStart(cover: String): Boolean {
        return cover == graphicalList[0]
    }

    fun isIllustrationListStart(cover: String): Boolean {
        return cover == illustrationList[0]
    }

    fun getDefaultCoverIndex(): Int {
        return imageList.indexOf(IMG_COVER_DEFAULT)
    }

    /**
     * 新建状态默认选择的笔记本封面位置
     */
    fun getDefaultCreateFolderCoverIndex(): Int {
        return imageList.indexOf(IMG_COVER_1)
    }
}