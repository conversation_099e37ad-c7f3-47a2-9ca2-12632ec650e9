/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  baixu       2023/2/21      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Outline
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.ViewOutlineProvider
import android.widget.CheckBox
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.buttonBar.COUIButtonBarLayout
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.slideview.COUISlideView
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.anim.CardDeleteAnimation
import com.oplus.note.scenecard.todo.ui.main.TodoListAdapter
import com.oplus.note.scenecard.todo.ui.main.TodoListAdapter.Companion.SHADOW_RADIUS
import com.oplus.note.scenecard.todo.ui.main.TodoListAdapter.Companion.VIEW_TYPE_UNDEFINE
import com.oplus.note.scenecard.utils.ReflectionUtils
import kotlin.math.abs

class NoteSlideView : COUISlideView {
    private var mCardType = TodoListAdapter.VIEW_TYPE_UNDEFINE
    private var mLastType = TodoListAdapter.VIEW_TYPE_UNDEFINE
    var mTitle: TextView? = null
    var mCardView: NoteCardView? = null
    var mBgColor: View? = null
    var mTime: TextView? = null
    var mDone: CheckBox? = null
    var mOverDue: TextView? = null
    var mScaleX: Float = 1.0f
    var mScaleY: Float = 1.0f
    var mPosition: Int = 0
    var mLastAnimationType: Int = VIEW_TYPE_UNDEFINE
    var canDelete = false
    private var clickDeleteButton = false
    private var mLastX = 0
    private var mLastY = 0

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet, defStyle: Int) : super(
        context,
        attrs,
        defStyle
    ) {
        init()
    }

    override fun setDeleteEnable(enable: Boolean) {
        canDelete = enable
        super.setDeleteEnable(enable)
    }

    private fun init() {
        outlineProvider = object : ViewOutlineProvider() {
            override fun getOutline(view: View?, outline: Outline?) {
                outline?.setRoundRect(0, 0, view?.width ?: 0, view?.height ?: 0, SHADOW_RADIUS)
            }
        }
        kotlin.runCatching {
            ReflectionUtils.setSuperPrivateField(this, FIELD_NAME, FIELD_VALUE)
        }.onFailure {
            AppLogger.BASIC.d(TAG, "noteslideview: $it")
        }
    }


    fun setCardType(cardType: Int) {
        mLastType = mCardType
        mCardType = cardType
    }

    fun getCardType(): Int {
        return mCardType
    }

    fun getLastType(): Int {
        return mLastType
    }

    override fun startDeleteAnimation(view: View?) {
        var startWith = 0
        kotlin.runCatching {
            startWith = ReflectionUtils.getSuperPrivateField(this, "mHolderWidth") as Int
        }.onFailure {
            AppLogger.BASIC.d(TAG, "mHolderWidth error: ${it.message}")
        }.onSuccess {
            AppLogger.BASIC.d(TAG, "success")
        }
        val animation: CardDeleteAnimation = object : CardDeleteAnimation(
            view, this@NoteSlideView,
            (if (layoutDirection == View.LAYOUT_DIRECTION_RTL) -startWith else startWith),
            (if (layoutDirection == View.LAYOUT_DIRECTION_RTL) -width else width)
        ) {
            override fun itemViewDelete() {
                reflectClick()
            }
        }
        val dialog = COUIAlertDialogBuilder(context, com.support.appcompat.R.style.COUIAlertDialog_Bottom)
            .setNeutralButton(R.string.scene_btn_delete) { _, _ ->
                clickDeleteButton = true
                run {
                    animation.startAnimation()
                }
            }
            .setNegativeButton(R.string.scene_btn_cancel) { _, _ -> }
            .setWindowGravity(Gravity.BOTTOM)
            .setWindowAnimStyle(com.support.appcompat.R.style.Animation_COUI_Dialog)
            .setOnDismissListener {
                if (!clickDeleteButton) {
                    shrink()
                }
                clickDeleteButton = false
                ReflectionUtils.setSuperPrivateField(this@NoteSlideView, FILED_ANIMATION, false)
            }
            .show()
        fixDialogButtonsPadding(dialog)
    }

    private fun fixDialogButtonsPadding(dialog: AlertDialog) {
        // 利用反射将无title无message的dialog中的button的上下间距设为一致
        val buttonLayout = dialog.findViewById<COUIButtonBarLayout>(com.support.appcompat.R.id.buttonPanel)
        buttonLayout?.let {
            val paddingBottom = it.javaClass.getDeclaredField(PADDING_BOTTOM)
            paddingBottom.isAccessible = true
            val value = paddingBottom.get(it)

            val paddingTop = it.javaClass.getDeclaredField(PADDING_TOP)
            paddingTop.isAccessible = true
            paddingTop.set(it, value)
        }
    }

    private fun reflectClick() {
        kotlin.runCatching {
            val listener = ReflectionUtils.getSuperPrivateField(
                this,
                "mOnDeleteItemClickListener"
            ) as? OnDeleteItemClickListener
            if (listener != null) {
                ReflectionUtils.setSuperPrivateField(this, FILED_ANIMATION, false)
                listener.onDeleteItemClick()
            }
        }.onFailure {
            AppLogger.BASIC.d(TAG, "reflectClick error: ${it.message}")
        }.onSuccess {
            AppLogger.BASIC.d(TAG, "success")
        }
    }

    override fun onDraw(canvas: Canvas) {
        deleteDrawable?.apply {
            if ((slideEnable || drawItemEnable) && canDelete) {
                drawDeleteButton(canvas, deleteDrawable)
            }
        }
        super.onDraw(canvas)
    }

    private val deleteDrawable = context.getDrawable(R.drawable.todo_delete)
    private fun drawDeleteButton(canvas: Canvas, drawable: Drawable) {
        var fraction = 0f
        var holderWidth = 0
        kotlin.runCatching {
            holderWidth = ReflectionUtils.getSuperPrivateField(this, "mHolderWidth") as Int
            fraction = abs(slideViewScrollX * 1f / holderWidth)
        }.onFailure {
            fraction = 0f
        }

        val alpha = (fraction / NUMBER_05 * ALPHA_ALL).toInt().coerceAtLeast(0).coerceAtMost(ALPHA_ALL)
        val scale = (NUMBER_05 * fraction + NUMBER_05).coerceAtLeast(0f).coerceAtMost(1f)

        val iconWidth: Int = (drawable.intrinsicWidth * scale).toInt()
        val iconHeight: Int = (drawable.intrinsicWidth * scale).toInt()
        // 删除按钮两边的 margin
        val margin = MARGIN
        val iconLeft: Int = if (isLayoutRtl) margin else width - margin - iconWidth
        val iconTop: Int = (height - iconHeight) / 2
        val iconRight = iconLeft + iconWidth
        val iconBottom = iconTop + iconHeight

        drawable.apply {
            this.alpha = alpha
            setBounds(iconLeft, iconTop, iconRight, iconBottom)
            draw(canvas)
        }
    }

    companion object {
        private const val TAG = "NoteSlideView"
        private const val FIELD_NAME = "EIGHT"
        private const val FIELD_VALUE = 5
        private const val FILED_ANIMATION = "mhasStartAnimation"
        private const val NUMBER_05 = 0.5f
        private const val ALPHA_ALL = 255
        private const val MARGIN = 32

        private const val PADDING_TOP = "mHorizontalButtonPaddingTop"
        private const val PADDING_BOTTOM = "mHorizontalButtonPaddingBottom"
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (!isEnabled) {
            return true
        }
        return super.onTouchEvent(event)
    }
}