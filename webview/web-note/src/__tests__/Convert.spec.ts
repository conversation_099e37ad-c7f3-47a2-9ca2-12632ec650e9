import { pxToRem } from '../ts/utils/Convert';
import 'jest';

describe('Converter.ts', () => {
  test('Should return rem when invoke pxToRem with px', () => {
    // given
    const input = "16px"

    // when
    const result = pxToRem(input)

    // then
    expect(result).toEqual("1rem");
  })

  test('Should return rem when invoke pxToRem without px', () => {
    // given
    const input = "16"

    // when
    const result = pxToRem(input)

    // then
    expect(result).toEqual("1rem");
  })

  test('Should return emprty when invoke pxToRem with invalid value', () => {
    // given
    const input = "16dp"

    // when
    const result = pxToRem(input)

    // then
    expect(result).toEqual("");
  })
})