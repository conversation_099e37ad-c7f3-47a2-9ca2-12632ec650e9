import { mergeAttributes, Node } from '@tiptap/core'
import * as ActiveObserver from '@ts/TipTapAttrsObserver'

export interface ListItemOptions {
    HTMLAttributes: Record<string, any>,
    bulletListTypeName: string
    orderedListTypeName: string
}

export const ListItem = Node.create<ListItemOptions>({
    name: 'listItem',

    addOptions() {
        return {
            HTMLAttributes: {},
            bulletListTypeName: 'bulletList',
            orderedListTypeName: 'orderedList',
        }
    },

    content: 'paragraph*',

    defining: true,

    addAttributes() {
        return {
            itemFontSize: {
                default: ActiveObserver.instance.currentFontSize.value,
                parseHTML: element => {
                    return element.hasAttribute('itemFontSize') ? element.getAttribute('itemFontSize') : ActiveObserver.instance.currentFontSize.value
                },
            },
            itemMarker: {
                default: null,
                parseHTML: element => {
                    return element.hasAttribute('itemMarker') ? element.getAttribute('itemMarker') : null
                },
            },
        }
    },

    parseHTML() {
        return [
            {
                tag: 'li',
            },
        ]
    },

    renderHTML({ HTMLAttributes }) {
        let styleDeclaration =  ``
        if (HTMLAttributes.itemFontSize) {
            styleDeclaration += `font-size: ${HTMLAttributes.itemFontSize}; `
        }
        return ['li', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
            style: styleDeclaration,
          }), 0]
    },

    addKeyboardShortcuts() {
        return {
            Enter: () => this.editor.commands.splitListItem(this.name),
            Tab: () => this.editor.commands.sinkListItem(this.name),
            'Shift-Tab': () => this.editor.commands.liftListItem(this.name),
        }
    },
})