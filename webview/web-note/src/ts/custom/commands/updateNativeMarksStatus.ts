import { DEBUG_ON_WEB, getEditorName } from "@/ts/EditorUtils"
import { Constants } from "@/ts/utils/Constants"
import { RawCommands } from "@tiptap/vue-3"
import { Editor } from '@tiptap/core'
import { isHeadingActive } from "../helpers/textStyleHelper"
import { MARK_NAME_BOLD } from "../CustomBold"
import { MARK_NAME_ITALIC } from "../CustomItalic"
import { MARK_NAME_STRIKE, MARK_NAME_UNDERLINE } from "../helpers/markHelper"
import { getMarkAttributesFilter, isActiveFilter, isSelectionInSameTable, markFilterFunc } from "../helpers"
import { getActiveFontSize } from "../helpers/getActiveFontSize"
import { TEXT_STYLE_FONT_SIZE } from "../CustomFontSize"
import BackgroundColor, { TEXT_STYLE_BACKGROUND_COLOR } from "../extension/background-color"
import { TEXT_STYLE_TEXT_COLOR } from "../CustomColor"
import { EditorState } from "@tiptap/pm/state"
import { isInTable } from '@tiptap/pm/tables'

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        updateNativeMarksStatus: {
            /**
             * 更新Native侧富文本工具栏Marks按钮的状态
             */
            updateNativeMarksStatus: () => ReturnType,
        }
    }
}

function isActive(markName: string, enable: boolean, active: boolean,isInTable:Boolean) {
    if (DEBUG_ON_WEB) {
        console.log(`markStatus, markName:${markName}, enable:${enable}, active:${active} isInTable:${isInTable}`)
    }
    (window as any).injectedObject?.isActive(markName, enable, active, isInTable)
}

function updateTextColor(editor: Editor, isContentEditor: boolean) {
    let enable = false
    let colorValue = 'default'
    if (isContentEditor) {
        enable = !isSelectionInSameTable(editor.state)
        const { schema } = editor.state
        const markType = schema.marks.textStyle
        const attrs = getMarkAttributesFilter(markFilterFunc, editor.state, markType)
        const color = attrs.color
        if (color && (color as string).startsWith('var')) {
            colorValue = color.substring(4, color.length - 1)
        }
    } else {
        colorValue = 'transparent'
    }
    if (DEBUG_ON_WEB) {
        console.log(`markStatus, markName:textColor, enable:${enable}, value:${colorValue}`)
    }
    (window as any).injectedObject?.onTextColorActive(enable, colorValue)
}

function isFontSizeActive(state: EditorState, paragraphFontSize: number | undefined, nonParagraphFontSize: number | undefined) {
    const enable = paragraphFontSize != undefined && !isSelectionInSameTable(state)
    const fontSize = (paragraphFontSize || nonParagraphFontSize || 1) * 16;
    if (DEBUG_ON_WEB) {
        const fontSizeSelectorElement = document.getElementById('fontSizeSelector')
        if (fontSizeSelectorElement) {
            (fontSizeSelectorElement as HTMLSelectElement).value = `${fontSize}`;
            (fontSizeSelectorElement as HTMLSelectElement).disabled = !enable
        }
        if (DEBUG_ON_WEB) {
            console.log(`markStatus, markName:fontSize, enable:${enable}, value:${fontSize}`)
        }
    } else {
        (window as any).injectedObject?.onFontSizeActive(enable, fontSize)
    }
}

export const updateNativeMarksStatus: RawCommands['updateNativeMarksStatus'] = () => ({ editor, state, }) => {
    const editorName = getEditorName(editor)
    const isContentEditor = editorName == Constants.TIPTAP_CONTENT_CLASS_NAME
    const { from , to } = editor.state.selection
    const isFocused = editor.isFocused
    // isFocused 为false的情况下 判断是不是ContentEditor 如果是 ContentEditor 且 选区from !== to 就判定 ContentEditor在聚焦态 focused就为true
    const focused = isFocused ? isFocused : isContentEditor ? from === to ? isFocused : true : isFocused
    let enable = false
    let active = false
    let isActiveHeading = false
    let isInTableArg = isInTable(editor.state)
    // const marks = Object.keys(state.schema.marks)
    const marks: string[] = [MARK_NAME_BOLD, MARK_NAME_ITALIC, MARK_NAME_STRIKE, MARK_NAME_UNDERLINE, TEXT_STYLE_BACKGROUND_COLOR, TEXT_STYLE_TEXT_COLOR, TEXT_STYLE_FONT_SIZE]
    marks.forEach((mark) => {
        switch (mark) {
            case MARK_NAME_BOLD:
                isActiveHeading = isContentEditor ? isHeadingActive(editor) : true
                //  && !isSelectionInSameTable(state)
                enable = focused && isContentEditor && !isActiveHeading
                active = enable && isActiveFilter(() => false, editor.state, mark)
                isActive(mark, enable, active,isInTableArg)
                break
            case MARK_NAME_ITALIC:
            case MARK_NAME_STRIKE:
            case MARK_NAME_UNDERLINE:
                // && !isSelectionInSameTable(state)
                enable = focused && isContentEditor
                active = enable && isActiveFilter(() => false, editor.state, mark)
                isActive(mark, enable, active,isInTableArg)
                break
            case TEXT_STYLE_BACKGROUND_COLOR:
                enable = focused && isContentEditor && !isSelectionInSameTable(state)
                active = enable && isActiveFilter(markFilterFunc, state, state.schema.marks?.textStyle?.name, { backgroundColor: BackgroundColor.options.defaultColor })
                isActive(mark, enable, active,isInTableArg)
                break
            case TEXT_STYLE_TEXT_COLOR:
                updateTextColor(editor, isContentEditor)
                break
            case TEXT_STYLE_FONT_SIZE:
                const fontSize = getActiveFontSize(editor.state, markFilterFunc)
                isFontSizeActive(state, fontSize.firstParagraphFontSize, fontSize.firstNonParagraphFontSize)
                break
            default:
                break
        }
    })

    return true
}