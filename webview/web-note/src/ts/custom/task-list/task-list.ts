import { mergeAttributes, Node } from '@tiptap/core'
import TextStyle from '@tiptap/extension-text-style';
import { ListItem } from "@ts/custom/CustomListItem";
import { nodeWrapInFilterFunc } from '../helpers';

export interface TaskListOptions {
  /**
   * The node type name for a task item.
   * @default 'taskItem'
   * @example 'myCustomTaskItem'
   */
  itemTypeName: string,

  /**
   * The HTML attributes for a task list node.
   * @default {}
   * @example { class: 'foo' }
   */
  HTMLAttributes: Record<string, any>,
  
  /**
   * Keep the marks when splitting a list item.
   * @default false
   * @example true
   */
  keepMarks: boolean,

  /**
   * Keep the attributes when splitting a list item.
   * @default false
   * @example true
   */
  keepAttributes: boolean,

  /**
   * Attributes that are kept for the new text node.
   * @default []
   * @example ['textIndent']
   */
   attributesKeptForNewTextNode: string[],
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    taskList: {
      /**
       * Toggle a task list
       * @example editor.commands.toggleTaskList()
       */
      toggleTaskList: () => ReturnType,
    }
  }
}

/**
 * This extension allows you to create task lists.
 * @see https://www.tiptap.dev/api/nodes/task-list
 */
export const TaskList = Node.create<TaskListOptions>({
  name: 'taskList',

  addOptions() {
    return {
      itemTypeName: 'taskItem',
      HTMLAttributes: {},
      keepMarks: false,
      keepAttributes: false,
      attributesKeptForNewTextNode: [],
    }
  },

  group: 'block list',

  content() {
    return `${this.options.itemTypeName}+`
  },

  parseHTML() {
    return [
      {
        tag: `ul[data-type="${this.name}"]`,
        priority: 51,
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['ul', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, { 'data-type': this.name }), 0]
  },

  addCommands() {
    return {
      toggleTaskList: () => ({ commands, chain }) => {
        if (this.options.keepAttributes) {
          return chain().toggleListFilter(nodeWrapInFilterFunc, this.name, this.options.itemTypeName, this.options.keepMarks).updateAttributesFilter(nodeWrapInFilterFunc, ListItem.name, this.editor.getAttributes(TextStyle.name)).run()
        }
        return commands.toggleListFilter(nodeWrapInFilterFunc, this.name, this.options.itemTypeName, this.options.keepMarks, null, this.options.attributesKeptForNewTextNode)
      },
    }
  },

  addKeyboardShortcuts() {
    return {
      'Mod-Shift-9': () => this.editor.commands.toggleTaskList(),
    }
  },
})