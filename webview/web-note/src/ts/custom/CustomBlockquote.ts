import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'
import { liftTarget } from '@tiptap/pm/transform'
import { isNodeActiveFilter, nodeWrapInFilterFunc } from './helpers'
import { removeMark } from './helpers/removeMark'

export interface CustomBlockquoteOptions {
  /**
   * HTML attributes to add to the blockquote element
   * @default {}
   * @example { class: 'foo' }
   */
  HTMLAttributes: Record<string, any>,
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    blockQuote: {
      /**
       * Set a blockquote node
       */
      setBlockquote: () => ReturnType,
      /**
       * Toggle a blockquote node
       */
      toggleBlockquote: () => ReturnType,
      /**
       * Unset a blockquote node
       */
      unsetBlockquote: () => ReturnType,
    }
  }
}

/**
 * Matches a blockquote to a `>` as input.
 */
export const inputRegex = /^\s*>\s$/

/**
 * This extension allows you to create blockquotes.
 * @see https://tiptap.dev/api/nodes/blockquote
 */
export const CustomBlockquote = Node.create<CustomBlockquoteOptions>({

  name: 'blockquote',

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  content: 'paragraph+',

  group: 'block',

  defining: true,

  parseHTML() {
    return [
      { tag: 'blockquote' },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['blockquote', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]
  },

  addCommands() {
    return {
      setBlockquote: () => ({ chain, state }) => {
        return chain().command(({ state, tr }) => {
          const { from, to } = state.selection
          state.doc.nodesBetween(from, to, (node, pos) => {
            if (nodeWrapInFilterFunc(node)) {
              return false
            }
            if (node.type.name == state.schema.nodes.paragraph?.name) {
              removeMark(tr, node, pos, state.schema.marks.textStyle, { fontSize: null })
              return false
            }
          })
          return true
        }).unsetBlockquote().wrapInFilter(nodeWrapInFilterFunc, this.name).run()
      },
      toggleBlockquote: () => ({ chain, state }) => {
        if (isNodeActiveFilter(nodeWrapInFilterFunc, state, this.name)) {
          return chain().unsetBlockquote().run()
        } else {
          return chain().setBlockquote().run()
        }
      },
      unsetBlockquote: () => ({ state, tr, dispatch }) => {
        const { selection } = tr
        const { ranges } = selection
        ranges.forEach(({ $from, $to }) => {
          state.doc.nodesBetween($from.pos, $to.pos, (node, pos) => {
            if (node.type.isText) {
              return
            }
            const { doc, mapping } = tr
            const $mappedFrom = doc.resolve(mapping.map(pos))
            const $mappedTo = doc.resolve(mapping.map(pos + node.nodeSize))
            const nodeRange = $mappedFrom.blockRange($mappedTo)
            if (!nodeRange) {
              return
            }
            const targetLiftDepth = liftTarget(nodeRange)
            if (targetLiftDepth || targetLiftDepth === 0) {
              tr.lift(nodeRange, targetLiftDepth)
            }
          })
        })
        return true
      },
    }
  },

  addKeyboardShortcuts() {
    return {
      'Mod-Shift-b': () => this.editor.commands.toggleBlockquote(),
    }
  },

  // addInputRules() {
  //   return [
  //     wrappingInputRule({
  //       find: inputRegex,
  //       type: this.type,
  //     }),
  //   ]
  // },
})

export default CustomBlockquote