import {Editor, isTextSelection} from "@tiptap/core";
import {EditorState, Plugin, PluginKey, Transaction} from "@tiptap/pm/state";
import {isInTable, selectedRect} from "@tiptap/pm/tables";
import {Decoration, DecorationSet} from "@tiptap/pm/view";
import {Sc<PERSON>Helper} from "./ScrollHelper";
import {getSelectedTableCellHTMLElement} from "./columnDotMenu";
import { queryTableElement } from "../../helpers";
import { removeTableBorderRadiusProperty } from "@ts/utils/CommonUtil";
import {BScroll} from "@ts/custom/better-scroll/core/src/BScroll";
import * as ActiveObserver from "@ts/TipTapAttrsObserver";
import {TABLE_OVER_FLOW_CLIP_MARGIN} from "@ts/custom/table/TableView";
import {updateDotMenuVisibility} from "@ts/custom/table/utilities/updateDotMenuVisibility";

const key = new PluginKey<number>("selectionCellLighlight");
// 存储触点信息的对象
const touchState = {
    startX: 0,
    startY: 0,
    startTime: 0,
    identifier: null,
    isMoved: false // 是否发生滑动
};
let startCell: HTMLTableCellElement = null;
let currentCell: HTMLTableCellElement = null;
let selectionCellBorderEl: HTMLElement = null;
let tableOffset: DOMRect = null;
let tableWrapperEle: HTMLElement = null;
let selectedArea
let computed = false
let rowStartIndex = -1
let colStartIndex = -1
let rowEndIndex = -1
let colEndIndex = -1
let computeStyle = false
let touchHandling = false
let scrollAble = false
let bs: BScroll = null
let disAbleScroll = false
let modifyCell = -1
let dotHandling = false
const MODIFY_START = 0
const MODIFY_CURRENT = 1
let tableVisible: boolean = true
let cellsCount = 1
// 获取物理像素阈值（适配Retina屏）
const getPhysicalThreshold = () => {
    // 像素基准值
    const baseThreshold = 5;
    return baseThreshold * window.devicePixelRatio;
}
export function selectionCellLighlight(editor): Plugin {
    return new Plugin({
        key,
        props: {
            decorations(state) {
                return this.getState(state);
            },
            handleDOMEvents: {
                touchstart(view, event: TouchEvent) {
                    //双指滑动，不响应
                    const touch = event.touches[0];
                    touchState.startX = touch.clientX;
                    touchState.startY = touch.clientY;
                    touchState.startTime = Date.now();
                    touchState.identifier = touch.identifier;
                    touchState.isMoved = false;
                    scrollAble = shouldScroll(view, event)
                    console.log(`bxx touchstart scrollAble ${scrollAble},inTable:${isInTable(view.state)},dotHandling:${dotHandling}`)
                    if (!isInTable(view.state) || dotHandling) return;
                    // 如果touchstart没有与当前光标所在的单元格一致，也不响应
                    // 获取光标位置
                    const $pos = editor.state.selection.$from;
                    // 获取光标在文档中的位置
                    const pos = $pos.pos;
                    // 获取光标所在的节点的 DOM 元素
                    const dom = editor.view.domAtPos(pos).node;
                    const eventTarget = event.target as HTMLElement;
                    const mSelection = view.state.selection
                    if (!mSelection) return;
                    const attachId = mSelection.$to.node(1).attrs["attachid"];
                    bs = ScrollHelper.getScroll(attachId);
                    if (scrollAble) {
                        enableScroll(bs, true)
                        touchHandling = false
                        return;
                    }
                    if (
                        (eventTarget.tagName === "P" &&
                        eventTarget.parentElement.tagName === "TD" ||
                            //行末的时候，tagName和parentTagName与行中不一样
                         eventTarget.tagName === "TD" &&
                        eventTarget.parentElement.tagName === "TR") &&
                        isDotMenuGone()
                    ) {
                        const allTable = eventTarget.closest('table');
                        intersection.unobserve(allTable);
                        intersection.observe(allTable);
                        let colorStyle = false
                        if (selectionCellBorderEl && selectionCellBorderEl.style) {
                            colorStyle = selectionCellBorderEl.style.getPropertyValue('--my-color') === 'var(--color-primary)'
                        }
                        if (startCell && colorStyle) {
                            enableScroll(bs, false)
                            touchHandling = true
                            // console.log(`bxx startCell not null and disable scroll`)
                            computeStyle = true
                        } else {
                            //手指放下的地方,eventTarget可能为paraph或者HTMLTableCellElement
                            if(eventTarget instanceof HTMLParagraphElement){
                                startCell = eventTarget.parentElement as HTMLTableCellElement;
                            }else if(eventTarget instanceof HTMLTableCellElement){
                                startCell = eventTarget;
                            }
                            // console.log(`bxx startCell  setStartCell 111 ${startCell?.textContent}`)
                            if (startCell && dom) {
                                // console.log(`bxx 光标位置: 包含=${startCell.contains(dom)}`);

                                // 比较光标位置和 tableCell 的位置,光标所在区域和按下区域不同，不响应滑选事件，这个selection需要-1，debug发现的
                                if (startCell.contains(dom) && ActiveObserver.instance.contentOnFocus.value) {
                                    // event.preventDefault();
                                    enableScroll(bs, false)
                                    colStartIndex = startCell.cellIndex
                                    rowStartIndex = (startCell.parentElement as HTMLTableRowElement).rowIndex
                                    computeStyle = true
                                    touchHandling = true
                                    // console.log(`bxx startCell  null and disable scroll (${colStartIndex},${rowStartIndex})`)
                                } else {
                                    //不在区域内，需要置空，否则第二次滑动会导致出现选中
                                    resetSelectionCellBorder();
                                    enableScroll(bs, true)
                                    touchHandling = false
                                    // console.log(`bxx startCell not null and not in the same cell,reset startCell null`)
                                    return;
                                }
                            } else {
                                //from为空，需要置空，否则第二次滑动会导致出现选中
                                resetSelectionCellBorder();
                                enableScroll(bs, true)
                                touchHandling = false
                                // console.log(`bxx from is null and set startCell null`)
                                return;
                            }
                        }
                    }
                },
                touchmove(view, event) {
                    // console.log(`bxx1 move-------------${dotHandling}`)
                    if (!touchState.identifier) {
                        touchState.isMoved = true;
                    }
                    // 检测到移动超过阈值
                    const touch = Array.from(event.touches).find(t => t.identifier === touchState.identifier);
                    if (!touch) {
                        touchState.isMoved = true;
                    }
                    const deltaX = Math.abs(touch.clientX - touchState.startX);
                    const deltaY = Math.abs(touch.clientY - touchState.startY);
                    if (deltaX > 2 || deltaY > 2) {
                        touchState.isMoved = true;
                    }
                    if (!isInTable(view.state) || scrollAble || dotHandling) return;
                    if (computeStyle) {
                        enableScroll(bs, false)
                        computeCurrentStyle(event, editor, MODIFY_CURRENT)
                    }
                },
                touchend(view, event) {
                    //end只需要屏蔽双指，表格外还是需要响应的
                    console.log(`bxx1 touchend: ${dotHandling}`)
                    if (dotHandling) return;
                    endTouch(editor, event, true);
                    onEventEnd()
                },
                touchcancel(view,event){
                    console.log(`bxx1 touchcancel: ${dotHandling}`)
                    if (dotHandling) return;
                    onEventEnd()
                }
            },
        },

        state: {
            init() {
                return null;
            },
            apply(tr, cur, oldState, newState) {
                //自上到下，从左到右的滑选，结束的时候，不会触发apply，但是反向会触发，需要屏蔽,目前原因未知，此状态下，oldState和newState相同
                const apply = applyStateAvailable(tr);
                return apply ? applyState(tr, cur, oldState, newState, editor) : cur;
            },

        },
    });
}

export function endTouch(editor, event, needTap) {
    const attachId = "1";
    const currentTime = Date.now();
    const timeDiff = currentTime - touchState.startTime;
    const moveThreshold = getPhysicalThreshold();
    const touch = event.changedTouches[0]
    const deltaX = Math.abs(touch.clientX - touchState.startX);
    const deltaY = Math.abs(touch.clientY - touchState.startY);
    const isTap = deltaX < moveThreshold &&
        deltaY < moveThreshold &&
        timeDiff < ActiveObserver.instance.tapTime &&
        !touchState.isMoved && needTap;
    touchHandling = false
    if (scrollAble) {
        handleAndroidPop(rowStartIndex, colStartIndex, rowEndIndex, colEndIndex, isTap, attachId, editor)
        return;
    }
    if (inScrollSelectionState()) {
        if (selectedArea) {
            handleAndroidPop(rowStartIndex, colStartIndex, rowEndIndex, colEndIndex, isTap, attachId, editor)
        }
    }
    if (disAbleScroll) {
        enableScroll(bs, true)
    }
}

export function popUpWindow() {
    if (isValid(rowStartIndex, colStartIndex, rowEndIndex, colEndIndex) && inScrollSelectionState()&&!touchHandling) {
        const area = getSelectedRect();
        if (area) {
            (window as any).injectedObject?.onLongClick('table', JSON.stringify(area), "", 1, 0, area.x, area.y);
//            console.log(`bxx onLongClick area 2: ${JSON.stringify(area)},${JSON.stringify(selectedArea)}`)
        }
    }
}

function handleAndroidPop(rowStartIndex, colStartIndex, rowEndIndex, colEndIndex, isTap, attachId, editor) {
    if (isValid(rowStartIndex, colStartIndex, rowEndIndex, colEndIndex) && !isTap) {
        editor.chain().setCellSelection(rowStartIndex, colStartIndex, rowEndIndex, colEndIndex).run();
        if (ActiveObserver.instance.scrollIdle.value) {
            popUpWindow()
        }
    }
}

function getSelectedRect() {
    const cellBorder = document.getElementsByClassName('selection-cell-border') as HTMLCollectionOf<HTMLElement>
    if (cellBorder && cellBorder.length > 0) {
        const selected = cellBorder[0]
        if (selected) {
            return selected.getBoundingClientRect()
        }
    }
    return undefined
}

function shouldScroll(view, event: TouchEvent) {
    //双指可滑动
    const twoFinger = event.touches.length === 2
    //如果是已选择，并且滑动区域在滑选框内，也可滑动
    let inTouchArea = false
    if (inScrollSelectionState()) {
        let cellElement
        //eventTarget可能是p标签，也可能是cell
        const eventTarget = event.target as HTMLParagraphElement;
        // console.log(`bxx eventTarget: ${eventTarget}`)
        if (eventTarget instanceof HTMLTableCellElement) {
            cellElement = eventTarget
        } else if (eventTarget instanceof HTMLParagraphElement) {
            cellElement = eventTarget.parentElement as HTMLTableCellElement
        }
        if (!cellElement) {
            return true
        }
        const colIndex = cellElement.cellIndex
        const rowIndex = (cellElement.parentElement as HTMLTableRowElement).rowIndex
        // console.log(`bxx (${colIndex},${rowIndex}),[${colStartIndex},${rowStartIndex},${colEndIndex},${rowEndIndex}]`)
        //存在从后往前选的区域，所以这里需要用到max和min
        inTouchArea = rowIndex >= Math.min(rowStartIndex, rowEndIndex) &&
            rowIndex <= Math.max(rowStartIndex, rowEndIndex) &&
            colIndex >= Math.min(colStartIndex, colEndIndex) &&
            colIndex <= Math.max(colStartIndex, colEndIndex)

    }
    return twoFinger || inTouchArea
}

export function inScrollSelectionState() {
    let colorStyle = false
    if (selectionCellBorderEl && selectionCellBorderEl.style) {
        colorStyle = selectionCellBorderEl.style.getPropertyValue('--my-color') === 'var(--color-primary)'
    }
    return colorStyle
}

function isDotMenuGone() {
    const columnMenu = document.querySelector(`.show-column-dot-menu-active`);
    const rowMenu = document.querySelector(`.show-row-dot-menu-active`);
    return columnMenu === null && rowMenu === null
}

function applyStateAvailable(tr: Transaction) {
    return isTextSelection(tr.selection) && !touchHandling && isDotMenuGone() && !ActiveObserver.instance.longTap;
}
//反复拖拽过程中，可能出现selectedArea为空的情况，做判空处理
function isValid(rowStartIndex: number, colStartIndex: number, rowEndIndex: number, colEndIndex: number) {
    const cellBorder = document.querySelector(`.selection-cell-border`) as HTMLElement;
    let displayValue = 'none'
    if (cellBorder) {
        displayValue = cellBorder.style.getPropertyValue('display')
    }
    // console.log(`bxx displayValue:${displayValue}, (${rowStartIndex},${colStartIndex},${rowEndIndex},${colEndIndex}),selectArea:${selectedArea != null} ,dot:${isDotMenuGone()}`)
    return rowStartIndex >= 0 && colStartIndex >= 0 && rowEndIndex >= 0 && colEndIndex >= 0 && selectedArea != null && inScrollSelectionState() && isDotMenuGone() && displayValue!='none' && tableVisible
}

function enableScroll(bs: BScroll, scrollAble: boolean) {
    scrollAble ? bs?.enable() : bs?.disable();
    disAbleScroll = !scrollAble;
    (window as any).injectedObject?.onRequestDisallowInterceptTouchEvent(!scrollAble);
}

function applyState(
    tr: Transaction,
    cur: any,
    oldState: EditorState,
    newState: EditorState,
    editor: Editor
) {
    if (!isInTable(newState)) return;
    resetSelectionCellBorder();
    const rect = selectedRect(newState);
    const attachId = newState.selection.$to.node(1).attrs["attachid"];
    const tableEle =queryTableElement(attachId)

    const selectedCell = getSelectedTableCellHTMLElement(rect);
    // console.log(selectedCell, `bxx applyState selectedCell`);
    if (!selectedCell) return;
    const selectedCellRect: DOMRect = selectedCell.getClientRects()[0];
    tableWrapperEle = document.querySelector(`.tableWrapper`) as HTMLElement;
    // 0.66px
    const table_border_width = parseFloat(
        getComputedStyle(tableWrapperEle).getPropertyValue("--table-border-width")
    );
    // 16px
    const table_padding_top = parseFloat(
        getComputedStyle(tableWrapperEle).paddingTop
    );
    selectionCellBorderEl = createSelectionCellBorderWidget(editor);

    if (tableEle) {
        const tableRect: DOMRect = tableEle.getBoundingClientRect();

        const wrapper = tableEle.parentElement;
        wrapper?.style?.setProperty('--table-overflow-clip-margin', `0px`);

        tableOffset = tableRect;
        const top = selectedCellRect.top -
            tableRect.top +
            table_padding_top -
            table_border_width
        const left = selectedCellRect.left - tableRect.left;

        selectionCellBorderEl.style.setProperty(
            "--selecteion-cell-width",
            `${selectedCellRect.width}px`
        );
        selectionCellBorderEl.style.setProperty(
            "--selecteion-cell-height",
            `${selectedCellRect.height}px`
        );
        selectionCellBorderEl.style.setProperty("--selecteion-cell-top", `${top}px`);
        selectionCellBorderEl.style.setProperty(
            "--selecteion-cell-left",
            `${left}px`
        );
        // console.log(`bxx startCell (top:${top},left:${left})`)

        const {tableStart, map, table} = selectedRect(newState);
        const cells: Decoration[] = [];
        cells.push(
            Decoration.widget(tableStart, selectionCellBorderEl, {
                ignoreSelection: true,
            })
        );
        return DecorationSet.create(tr.doc, cells);
    }
    return cur
}

const CLASS_NAME = "selection-cell-border";
const intersection = new IntersectionObserver((entris) => {
    for (const item of entris) {
        const {isIntersecting, target} = item
        tableVisible = isIntersecting
    }
})
function createSelectionCellBorderWidget(editor: Editor) {
    const widget = document.createElement("div");
    widget.className = CLASS_NAME;
    const topDot = document.createElement("div");
    topDot.className = "top-dot";
    const topDotInner = document.createElement("div");
    topDotInner.className = "top-dot-inner";
    const bottomDot = document.createElement("div");
    bottomDot.className = "bottom-dot";
    const bottomDotInner = document.createElement("div");
    bottomDotInner.className = "bottom-dot-inner";
    topDot.addEventListener('touchstart', (event) => {
        // console.log(`bxx1 topDot touchStart`)
        dotHandling = true
        computeStyle = false
        justifyCell(event)
        touchHandling = true
        enableScroll(bs, false)
    })
    topDot.addEventListener('touchmove', (event) => {
        computeCurrentStyle(event, editor, modifyCell);
        // console.log(`bxx1 topDot touchmove`)
    })
    topDot.addEventListener('touchend', (event) => {
        // console.log(`bxx1 topDot touchend`);
        onEventEnd();
        popUpWindow()
    })
    topDot.addEventListener('touchcancel', (event) => {
        onEventEnd();
    })
    bottomDot.addEventListener('touchstart', (event) => {
        console.log(`bxx1 bottomDot touchStart`)
        dotHandling = true
        computeStyle = false
        justifyCell(event)
        touchHandling = true;
        enableScroll(bs, false)
    })
    bottomDot.addEventListener('touchmove', (event) => {
        computeCurrentStyle(event, editor, modifyCell);
        // console.log(`bxx1 bottomDot touchmove`)
    })
    bottomDot.addEventListener('touchend', (event) => {
        console.log(`bxx1 bottomDot touchend`)
        popUpWindow();
        onEventEnd();
    })
    bottomDot.addEventListener('touchcancel', (event) => {
        console.log(`bxx1 bottomDot touchend`)
        onEventEnd();
    })
    //添加顺序有要求，不能反，反了之后，会导致中心圆圈无法响应touch事件
    widget.appendChild(topDotInner);
    widget.appendChild(topDot);
    widget.appendChild(bottomDotInner);
    widget.appendChild(bottomDot);
    modifyScrollZIndex(false)
    return widget;
}

function justifyCell(event) {
    const cellElement = document
        .elementsFromPoint(
            event.touches[0].clientX,
            event.touches[0].clientY
        )
        .find((i) => i.tagName === "TD") as HTMLTableCellElement;
    if (cellElement === currentCell) {
        modifyCell = MODIFY_CURRENT
    }
    if (cellElement === startCell) {
        modifyCell = MODIFY_START
    }
    console.log(`bxx1 justifyCell startCell:${startCell?.textContent},currentCell:${currentCell?.textContent},start ${startCell},current ${currentCell}`)
    //cell高度为40px，因此增加25的间距
    // console.log(`bxx1 justifyCell1 ${cellElement?.textContent}`)
    if (modifyCell === -1) {
        const cellElement = document
            .elementsFromPoint(
                event.touches[0].clientX + 20,
                event.touches[0].clientY + 20
            )
            .find((i) => i.tagName === "TD") as HTMLTableCellElement;
        // console.log(`bxx1 justifyCell2 ${cellElement?.textContent}`)
        if (cellElement === currentCell) {
            modifyCell = MODIFY_CURRENT
        }
        if (cellElement === startCell) {
            modifyCell = MODIFY_START
        }
    } else {
        return
    }
    if (modifyCell === -1) {
        const cellElement = document
            .elementsFromPoint(
                event.touches[0].clientX - 20,
                event.touches[0].clientY - 20
            )
            .find((i) => i.tagName === "TD") as HTMLTableCellElement;
        // console.log(`bxx1 justifyCell3 ${cellElement?.textContent}`)
        if (cellElement === currentCell) {
            modifyCell = MODIFY_CURRENT
        }
        if (cellElement === startCell) {
            modifyCell = MODIFY_START
        }
    } else {
        return
    }
    console.log(`bxx1 justifyCell modifyCell:${modifyCell}`)
}

function onEventEnd() {
    computed = false;
    computeStyle = false;
    touchHandling = false;
    dotHandling = false
    enableScroll(bs, true);
    modifyCell = -1
    // console.log(`bxx modifyCell -1`)
}

export function modifyScrollZIndex(low: boolean) {
    const selectionCells = document.getElementsByClassName('bscroll-horizontal-scrollbar') as HTMLCollectionOf<HTMLElement>
    for (const item of selectionCells) item.style.setProperty('z-index', low ? '-1' : '9999')
}

function computeCurrentStyle(event, editor, modifyCell) {
    if (modifyCell === MODIFY_CURRENT) {
        const cell = document
            .elementsFromPoint(
                event.touches[0].clientX,
                event.touches[0].clientY
            )
            .find((i) => i.tagName === "TD") as HTMLTableCellElement;
        // console.log(`bxx currentCell 2222 text:${cell?.textContent} , obj:${cell},same:${startCell?.parentElement?.parentElement === cell?.parentElement?.parentElement}`)
        if (cell && cell instanceof HTMLTableCellElement && isDotMenuGone() && cell?.parentElement?.parentElement === startCell?.parentElement?.parentElement) {
            currentCell = cell
            colEndIndex = currentCell.cellIndex
            rowEndIndex = (currentCell.parentElement as HTMLTableRowElement).rowIndex
            computedBorderStyle(editor);
            computed = true
        }
    } else if (modifyCell === MODIFY_START) {
        const cell = document
            .elementsFromPoint(
                event.touches[0].clientX,
                event.touches[0].clientY
            )
            .find((i) => i.tagName === "TD") as HTMLTableCellElement;
        // console.log(`bxx startCell  text:${startCell?.textContent} ,obj:${startCell},same:${startCell?.parentElement?.parentElement === currentCell?.parentElement?.parentElement}`)
        if (cell && cell instanceof HTMLTableCellElement && isDotMenuGone() && cell?.parentElement?.parentElement === currentCell?.parentElement?.parentElement) {
            startCell = cell
            colStartIndex = startCell.cellIndex
            rowStartIndex = (startCell.parentElement as HTMLTableRowElement).rowIndex
            computedBorderStyle(editor);
            computed = true
        }
    } else {
        console.log(`bxx computeCurrentStyle err pos`)
    }
}

export function removeSelectionCellBorderWidget() {
    //直接调用remove无法移除动态添加的div，因此考虑设置display：none处理，后续滑选的时候，会动态设置dispaly，不需要再行设置
    //后续设置过程中，可能出现不可见的时候滑选弹出菜单，如果有相关问题，考虑在isVlaid中判断当前布局是否可见，考虑需要查询，暂不添加
    // console.log(`bxx remove border`)
    const selectionCells = document.getElementsByClassName('selection-cell-border') as HTMLCollectionOf<HTMLTableCellElement>
    for(const item of selectionCells) item.style.setProperty('display', 'none')
    resetSelectionCellBorder()
    intersection.disconnect()
}

function resetSelectionCellBorder() {
    // console.log(`bxx resetSelectionCellBorder`)
    cellsCount = 1
    rowStartIndex = -1;
    colStartIndex = -1;
    rowEndIndex = -1;
    colEndIndex = -1;
    selectedArea = null;
    startCell = null;
    currentCell = null;
    computed = false;
    computeStyle = false
}


function computedBorderStyle(editor) {
    const tableEle = startCell.closest('table') as HTMLTableElement;
    const wrapper = tableEle.parentElement
    if (tableEle && startCell && currentCell) {
        if (currentCell != startCell && cellsCount === 1) {
            cellsCount = 2
        }
        const tableOffset: DOMRect = tableEle.getBoundingClientRect();
        const startRect = startCell.getBoundingClientRect();
        const currentRect = currentCell.getBoundingClientRect();
        wrapper?.style?.setProperty('--table-overflow-clip-margin', `${TABLE_OVER_FLOW_CLIP_MARGIN}px`)
        if (cellsCount === 2) {
            selectionCellBorderEl.style.setProperty('--my-color', 'var(--color-primary)');
            //compute的时候，调用blur隐藏输入法
            currentCell.blur();

            removeTableBorderRadiusProperty();
            editor.chain().setCellSelection(rowStartIndex, colStartIndex, rowEndIndex, colEndIndex).run();
            updateDotMenuVisibility(false)
            const highlightRect = {
                top: Math.min(startRect.top, currentRect.top) - tableOffset.top,
                left: Math.min(startRect.left, currentRect.left) - tableOffset.left,
                right: Math.max(startRect.right, currentRect.right) - tableOffset.left,
                bottom: Math.max(startRect.bottom, currentRect.bottom) - tableOffset.top,
            };
            selectedArea = {
                top: Math.min(startRect.top, currentRect.top),
                left: Math.min(startRect.left, currentRect.left),
                right: Math.max(startRect.right, currentRect.right),
                bottom: Math.max(startRect.bottom, currentRect.bottom),
            };
            const top = highlightRect.top
            const left = highlightRect.left
            selectionCellBorderEl.style.top = `${top}px`;
            selectionCellBorderEl.style.left = `${left}px`;
            selectionCellBorderEl.style.width = `${
                highlightRect.right - highlightRect.left
            }px`;
            selectionCellBorderEl.style.height = `${
                highlightRect.bottom - highlightRect.top
            }px`;
            selectionCellBorderEl.style.display = "block";
            modifyScrollZIndex(true);
        }
    }
}

