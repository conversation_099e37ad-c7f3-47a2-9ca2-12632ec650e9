export class Constants {
    // -1: 焦点未知，需要动态判断； 0：焦点在标题栏；1：焦点在正文
    static UNKNOWN_FOCUSED = -1
    static TITLE_FOCUSED = 0
    static CONTENT_FOCUSED = 1

    // fromEditor 0：标题栏；1：正文
    static TITLE_EDITOR = 0
    static CONTENT_EDITOR = 1


    static DEFAULT_CONTENT_PLACEHOLDER = `<p></p>`
    static DEFAULT_TITLE_PLACEHOLDER = `<h1></h1>`

    static OS_15_0_0 = 34

    // font family
    static Sys_Sans_Font = 'sys-sans'
    static Sys_Sans_Hans_Font = 'sys-sans-hans'
    static One_Sans_Font = 'one-sans'
    static Sys_Regular_Font = 'sys-regular'
    // ROBOTO字体
    static FONT_ROBOTO = "0"
    // 第三方字体
    static FONT_THIRD = "-1"
    // OP SANS
    static FONT_SYS_SANS = "1"
    // OP SANS，且打开了根据场景自动调整粗细
    static FONT_SYS_SANS_AUTO = "2"
    // ONE SANS
    static FONT_ONE_SANS = "3"

    static TIPTAP_CONTAINER_CLASS_NAME = 'TiptapContainer'

    static TIPTAP_TITLE_PARENT_CLASS_NAME = 'tiptapTitleParent'
    static TIPTAP_TITLE_CLASS_NAME = 'tiptapTitle'

    static TIPTAP_CONTENT_PARENT_CLASS_NAME = 'tiptapContentParent'
    static TIPTAP_CONTENT_CLASS_NAME = 'tiptapContent'

    static TIPTAP_BOTTOM_PLACEHOLDER_CLASS_NAME = 'bottomPlaceholder'


     // AIGC文字颜色定义 亮色
     static AIGC_TEXT_COLOR_LIST = [
        "#0f87ad99",
        "#21abd799",
        "#10bff766",
        "#4a86ff66",
        "#c25dff66",
        "#ff653766",
        "#ffc21966",
        "#29e64966",
    ]
   
    // 暗色
    static AIGC_TEXT_DARK_COLOR_LIST=[
        "#94dcf4de",
        "#3fb2d7d1",
        "#10bff7b3",
        "#4a86ffb3",
        "#c25dffb3",
        "#ff6537b3",
        "#ffc219b3",
        "#29e649b3",
    ]

    // Class name
    static CLASS_KOREAN_JAPANESE_CHAR = 'korean-japanese-character'

    // 表格圆点按钮选中时颜色
    static TABLE_DOT_MENU_SELECTED_COLOR = 'rgba(255, 255, 255, 1)'
    // 表格边框宽度
    static TABLE_BORDER_WIDTH = 0.66
    // 单元格圆角
    static TABLE_CELL_RADIUS = 4

    static BULLET_LIST_LI_CHAR = '•'
    static BULLET_LIST_LI_CHAR_WITH_INDENT_1 = '◦'
    static BULLET_LIST_LI_CHAR_WITH_INDENT_2 = '▸'
    static BULLET_LIST_HX_LI_CHAR = '-'
    static INDENT_BLANK_CHAR = '    '

    // Meta key
    static META_FORCE_UPDATE_SELECTED_COL = 'forceUpdateSelectedCol'
    static META_FORCE_UPDATE_SELECTED_ROW = 'forceUpdateSelectedRow'

    // Property
    static PROP_TABLE_BORDER_LEFT_TOP_RADIUS = '--table-border-left-top-radius'
    static PROP_TABLE_BORDER_LEFT_BOTTOM_RADIUS = '--table-border-left-bottom-radius'
    static PROP_TABLE_BORDER_RIGHT_TOP_RADIUS = '--table-border-right-top-radius'
    static PROP_TABLE_BORDER_RIGHT_BOTTOM_RADIUS = '--table-border-right-bottom-radius'
    static PROP_TABLE_DYNAMIC_SELECTED_CELL_RADIUS = '--table-dynamic-selected-cell-radius'

    static PROP_TABLE_DARG_COL_HEIGHT = '--table-drag-col-height'
    static PROP_TABLE_DARG_ROW_WIDTH = '--table-drag-row-width'
    static PROP_TABLE_BORDER_WIDTH = '--table-border-width'
    static PROP_TABLE_BORDER_WIDTH_ACTIVE = '--table-border-width-active'
    static PROP_TABLE_ACTUAL_BORDER_WIDTH = '--table-actual-border-width'
    static PROP_TABLE_BORDER_COLOR = '--table-border-color'

    static SELECT_ACTION_MENU_SELECT_ALL_ID = 'select_action_menu_select_all'

    // 图片分享的时候, 固定卡片/表格td宽度添加的style的类样式
    static FIX_WIDTH_WHEN_IMAGE_SHARE_STYLE_CLASS = 'fix-width-when-image-share-style-class'

    // 表格单独分享时 生成的图片类型组 只转换webp和png两种格式,  表格数据量大的情况下jpeg格式偶现是一坨黑
    static GENERATE_TABLE_IMAGE_TYPE = ['image/webp', 'image/png']

    // 表格支持的marks: strike underline italic bold
    static TABLE_SUPPORT_MARKS = ['strike', 'underline', 'italic', 'bold']
}