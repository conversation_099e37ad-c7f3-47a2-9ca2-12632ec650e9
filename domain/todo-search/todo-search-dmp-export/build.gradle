plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

apply from: rootProject.file('scripts/common.gradle')
android {
    namespace 'com.oplus.todo.search.dpm'
}

dependencies {
    implementation "androidx.core:core-ktx:${core_ktx}"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version"
    implementation "com.oplus.dmp.sdk:connect-export:$dmp_sdk_version"
    implementation "androidx.startup:startup-runtime:$startup"
    implementation project(':domain:todo-search:todo-search-api')
    implementation project(':common:lib_base')
    implementation project(':common:logger:logger-api')
    implementation project(':data:todo-repository')
}