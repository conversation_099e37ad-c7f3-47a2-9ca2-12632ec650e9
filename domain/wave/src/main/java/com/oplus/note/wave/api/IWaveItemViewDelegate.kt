/**************************************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: - IWaveItemViewDelegate.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/5/16
 * * Author: <EMAIL>
 * *
 * * --------------------------Revision History: --------------------------
 * *  <author>                         <data>        <version >    <desc>
 * *  <EMAIL>      2024/5/16         1.0
 **************************************************************************/
package com.oplus.note.wave.api

import android.view.ViewGroup
import com.oplus.note.wave.view.WaveItemView

internal interface IWaveItemViewDelegate {
    fun createNewItemView(parent: ViewGroup): WaveItemView

    fun onBindItemView(rulerView: WaveItemView, position: Int)

    fun halfWidth(): Int
}