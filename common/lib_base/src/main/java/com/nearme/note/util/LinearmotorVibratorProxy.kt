/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - LinearmotorVibrator.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/06/07
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 ****************************************************************/
package com.nearme.note.util

import android.content.Context
import com.coui.appcompat.vibrateutil.VibrateUtils
import com.oplus.os.LinearmotorVibrator

class LinearmotorVibratorProxy(private val context: Context) {
    private val linearmotorVibrator: LinearmotorVibrator? by lazy {
        VibrateUtils.getLinearMotorVibrator(context)
    }

    fun hasLinearMotorVibrator(): Boolean {
        return linearmotorVibrator != null
    }

    fun setLinearMotorVibratorStrength(vibrateType: Int, curValue: Int, maxValue: Int, startStrength: Int, endStrength: Int) {
        VibrateUtils.setLinearMotorVibratorStrength(
            linearmotorVibrator, vibrateType, curValue, maxValue, startStrength, endStrength
        )
    }
}