/***********************************************************
 * * Copyright (C), 2019-2027, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: ShareHelper.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2023/1/20
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.utils

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.content.FileProvider
import com.oplus.note.data.Constants
import com.oplus.note.logger.AppLogger
import java.io.File
import java.net.URLConnection

/**
 * 分享功能实现统一由FileProvider提供，具体定义见清单文件声明。
 */
object ShareHelper {

    private const val TAG = "ShareHelper"
    private const val FILE_MIME_TYPE_DEFAULT = "*/*"

    /**
     * @return the uri to be shared, or null if generate failed.
     */
    fun generateSharedUri(context: Context?, file: String, authority: String): Uri? {
        return generateSharedUri(context, File(file), authority)
    }

    /**
     * @return the uri to be shared, or null if generate failed.
     */
    fun generateSharedUri(context: Context?, file: File, authority: String): Uri? {
        if (context == null) {
            AppLogger.BASIC.e(TAG, "generateSharedUri failed, context is nul")
            return null
        }

        return kotlin.runCatching {
            FileProvider.getUriForFile(context, authority, file)
        }.getOrNull()
    }

    /**
     * 分享图片
     */
    fun shareImage(context: Context?, image: String, authority: String) {
        shareImage(context, File(image), authority)
    }

    /**
     * 分享图片
     */
    fun shareImage(context: Context?, image: File, authority: String) {
        val uri = generateSharedUri(context, image, authority)
        if (uri != null) {
            shareImage(context, uri)
        }
    }

    /**
     * 分享图片
     */
    fun shareImage(context: Context?, image: Uri?) {
        if (context == null || image == null) {
            AppLogger.BASIC.e(TAG, "shareImage failed: dataNull:${image == null}")
            return
        }

        kotlin.runCatching {
            val intent = Intent(Intent.ACTION_SEND)
            intent.setDataAndType(image, "image/*")
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            intent.putExtra(Intent.EXTRA_STREAM, image)
            val real = Intent.createChooser(intent, context.getString(com.oplus.note.baseres.R.string.rich_note_share))
            real.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            // 分享时排除特定目标组件
            addDefaultExcluded(context, real)
            AppLogger.BASIC.d(TAG, "shareImage start")
            context.startActivity(real)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "shareImage failed!", it)
        }
    }
    /**
     * 分享音频
     */
    fun shareAudio(context: Context?, displayName: String?, audio: String, authority: String) {
        shareAudio(context, displayName, File(audio), authority)
    }

    /**
     * 分享音频
     */
    fun shareAudio(context: Context?, displayName: String?, audio: File, authority: String) {
        val uri = generateSharedUri(context, audio, authority)
        if (uri != null) {
            shareAudio(context, displayName, uri)
        }
    }

    /**
     * 分享音频
     */
    fun shareAudio(context: Context?, displayName: String?, audio: Uri?) {
        if (context == null || audio == null) {
            AppLogger.BASIC.e(TAG, "shareAudio failed: dataNull:${audio == null}")
            return
        }

        kotlin.runCatching {
            val uri = audio.extWithDisplayName(displayName)
            val intent = Intent(Intent.ACTION_SEND)
            intent.setDataAndType(uri, "audio/*")
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            intent.putExtra(Intent.EXTRA_STREAM, uri)

            val real = Intent.createChooser(intent, context.getString(com.oplus.note.baseres.R.string.rich_note_share))
            real.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            // 分享时排除特定目标组件
            addDefaultExcluded(context, real)
            AppLogger.BASIC.d(TAG, "shareAudio: start")
            context.startActivity(real)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "shareAudio failed!", it)
        }
    }

    fun shareFile(context: Context?, displayName: String?, path: String, authority: String) {
        shareFile(context, displayName, File(path), authority)
    }

    fun shareFile(context: Context?, displayName: String?, file: File, authority: String) {
        val uri = generateSharedUri(context, file, authority)
        if (uri != null) {
            shareFile(context, displayName, uri, MediaFileUtils.queryMediaFileInfo(context, uri)?.mimeType)
        }
    }

    /**
     * 分享文件
     */
    fun shareFile(context: Context?, displayName: String?, fileUri: Uri?, mimeType: String?) {
        if (context == null || fileUri == null) {
            AppLogger.BASIC.e(TAG, "shareFile failed: dataNull:${fileUri == null}")
            return
        }

        kotlin.runCatching {
            val uri = fileUri.extWithDisplayName(displayName)
            val intent = Intent(Intent.ACTION_SEND)
            MediaFileUtils.queryMediaFileInfo(context, uri)?.mimeType
            intent.setDataAndType(uri, mimeType)
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            intent.putExtra(Intent.EXTRA_STREAM, uri)

            val real = Intent.createChooser(intent, context.getString(com.oplus.note.baseres.R.string.rich_note_share))
            real.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            // 分享时排除特定目标组件
            addDefaultExcluded(context, real)
            AppLogger.BASIC.d(TAG, "shareFile: start")
            context.startActivity(real)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "shareFile failed!", it)
        }
    }

    /**
     * 移除所有的标记占位符
     */
    @JvmStatic
    fun removeMarks(text: String?): String? {
        return text?.replace(Constants.IMG_SPAN_FLAG_TAG.toString(), "")
    }

    /**
     * 分享文本
     */
    fun shareText(context: Context?, text: String?) {
        if (context == null || text == null) {
            AppLogger.BASIC.e(TAG, "shareText failed: dataNull:${text == null}")
            return
        }

        kotlin.runCatching {
            val intent = Intent(Intent.ACTION_SEND)
            intent.type = "text/plain"
            intent.putExtra(Intent.EXTRA_TEXT, removeMarks(text))
            val real = Intent.createChooser(intent, context.getString(com.oplus.note.baseres.R.string.rich_note_share))
            real.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            // 分享时排除特定目标组件
            addDefaultExcluded(context, real)
            AppLogger.BASIC.d(TAG, "shareText: start")
            context.startActivity(real)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "shareText failed!", it)
        }
    }

    fun Uri.extWithDisplayName(displayName: String?): Uri {
        return if ((getQueryParameter("displayName").isNullOrEmpty()) && (!displayName.isNullOrEmpty())) {
            Uri.parse(toString()).buildUpon().appendQueryParameter("displayName", displayName).build()
        } else {
            this
        }
    }

    private fun addDefaultExcluded(context: Context, intent: Intent) {
        intent.putExtra(Intent.EXTRA_EXCLUDE_COMPONENTS, arrayOf(
            ComponentName(context, "com.nearme.note.activity.richedit.ShareActivity")
        ))
    }

    fun getFileMimeType(fileName: String?): String {
        if (fileName.isNullOrEmpty()) {
            AppLogger.BASIC.d(TAG, "getFileMimeType failed fileName is null")
            return ""
        }
        val mimeType = URLConnection.getFileNameMap().getContentTypeFor(fileName)
        return if (mimeType.isNullOrEmpty()) FILE_MIME_TYPE_DEFAULT else mimeType
    }
}