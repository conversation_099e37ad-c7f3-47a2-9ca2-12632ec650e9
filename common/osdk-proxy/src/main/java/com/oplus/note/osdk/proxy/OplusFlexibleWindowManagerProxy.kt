/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: OplusFlexibleWindowManagerProxy.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/06/25
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.osdk.proxy

import android.app.Activity
import android.app.ActivityOptions
import android.content.Context
import android.content.res.Configuration
import android.graphics.Point
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.Display
import android.view.OplusWindowManager
import android.view.WindowManager
import com.oplus.app.OplusWindowInfo
import com.oplus.flexiblewindow.FlexibleWindowManager
import com.oplus.os.OplusBuild

object OplusFlexibleWindowManagerProxy {

    private const val TAG = "OplusFlexibleWindowManagerProxy"

    private const val SUPPORT_FLEXIBLE_ACTIVITY_VERSION_CODE = 29
    const val DEFAULT_ACTIVITY_POSITION_LEFT = 1

    fun getFlexibleWindowManagerBundle(options: ActivityOptions, exBundle: Bundle): Bundle {
        return FlexibleWindowManager.getInstance().setExtraBundle(options, exBundle)
    }

    fun getFlexibleActivityPositionRight(): Int {
        return kotlin.runCatching {
            FlexibleWindowManager.FLEXIBLE_ACTIVITY_POSITION_RIGHT
        }.onFailure {
            Log.e(TAG, "getFlexibleActivityPositionRight error :${it.message}")
        }.getOrDefault(DEFAULT_ACTIVITY_POSITION_LEFT)
    }


    fun getFlexibleActivityPositionLeft(): Int {
        return kotlin.runCatching {
            FlexibleWindowManager.FLEXIBLE_ACTIVITY_POSITION_LEFT
        }.onFailure {
            Log.e(TAG, "getFlexibleActivityPositionLeft error :${it.message}")
        }.getOrDefault(DEFAULT_ACTIVITY_POSITION_LEFT)
    }


    fun getKeyFlexibleActivityPosition(): String? {
        return kotlin.runCatching {
            FlexibleWindowManager.KEY_FLEXIBLE_ACTIVITY_POSITION
        }.onFailure {
            Log.e(TAG, "getKeyFlexibleActivityPosition error :${it.message}")
        }.getOrNull()
    }

    fun getKeyFlexibleStartActivity(): String? {
        return kotlin.runCatching {
            FlexibleWindowManager.KEY_FLEXIBLE_START_ACTIVITY
        }.onFailure {
            Log.e(TAG, "getKeyFlexibleStartActivity error :${it.message}")
        }.getOrNull()
    }


    fun getKeyFlexibleActivityDescendant(): String? {
        return kotlin.runCatching {
            FlexibleWindowManager.KEY_FLEXIBLE_ACTIVITY_DESCENDANT
        }.onFailure {
            Log.e(TAG, "getKeyFlexibleActivityDescendant error :${it.message}")
        }.getOrNull()
    }

    /**
     * 判断当前窗口是否 是 处于跟手面板的状态
     * @param configuration
     * @return
     */
    fun isFlexibleActivitySuitable(configuration: Configuration?): Boolean {
        return kotlin.runCatching {
            if (isSupportFlexibleActivity()) {
                FlexibleWindowManager.isFlexibleActivitySuitable(configuration)
            } else false
        }.onFailure {
            Log.e(TAG, "isFlexibleActivitySuitable error : ${it.message}")
        }.getOrDefault(false)
    }

    /**
     * 判读当前系统是否支持跟手面板
     */
    @JvmStatic
    fun isSupportFlexibleActivity(): Boolean {
        return kotlin.runCatching {
            //Android U上暂不支持，待支持后去掉这个判断就行了
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.TIRAMISU) {
                Log.d(TAG, "isSupportFlexibleActivity Versions above android T are not supported yet")
                return@runCatching false
            }
            Log.d(TAG, "isSupportFlexibleActivity SDK_VERSION: ${OplusBuild.VERSION.SDK_VERSION} " +
                " SDK_SUB_VERSION: ${OplusBuild.VERSION.SDK_SUB_VERSION}")
            // SDK_VERSION + SDK_SUB_VERSION >= 29.29
            return@runCatching ((OplusBuild.VERSION.SDK_VERSION == SUPPORT_FLEXIBLE_ACTIVITY_VERSION_CODE)
                && (OplusBuild.VERSION.SDK_SUB_VERSION >= SUPPORT_FLEXIBLE_ACTIVITY_VERSION_CODE))
                || (OplusBuild.VERSION.SDK_VERSION > SUPPORT_FLEXIBLE_ACTIVITY_VERSION_CODE)
        }.onFailure {
            Log.e(TAG, "isSupportFlexibleActivity error :${it.message}")
        }.getOrDefault(false)
    }

    @JvmStatic
    fun hasFlexibleWindow(): Boolean {
        return kotlin.runCatching {
            FlexibleWindowManager.getInstance()
            true
        }.onFailure {
            Log.e(TAG, "hasFlexibleWindow error :${it.message}")
        }.getOrDefault(false)
    }

    /**
     * 判断是否支持PS分屏模式
     */
    @JvmStatic
    fun isSupportPocketStudio(activity: Activity): Boolean {
        var isSupportPocketStudio = false
        runCatching {
            isSupportPocketStudio = FlexibleWindowManager.getInstance().isSupportPocketStudio(activity)
        }.onFailure {
            Log.e(TAG, "isSupportPocketStudio error:${it.message}")
        }
        Log.d(TAG, "isSupportPocketStudio = $isSupportPocketStudio")
        return isSupportPocketStudio
    }

    /**
     * PS分屏模式下，分屏待选状态判断
     */
    @JvmStatic
    fun isMinimizedPocketStudio(): Boolean {
        var isMinimizedPocketStudio = false
        runCatching {
            isMinimizedPocketStudio = FlexibleWindowManager.getInstance().isMinimizedPocketStudio(Display.DEFAULT_DISPLAY)
        }.onFailure {
           Log.e(TAG, "isMinimizedPocketStudio error:${it.message}")
        }
        Log.d(TAG, "isMinimizedPocketStudio = $isMinimizedPocketStudio")
        return isMinimizedPocketStudio
    }

    /**
     * 是否是分屏态
     *
     * 适用OS13及以上
     */
    @JvmStatic
    fun isInMultiWindowMode(activity: Activity?): Boolean {
        val isInMultiWindowMode = runCatching {
            if (OplusBuildProxy.isAboveOS150()) {
                FlexibleWindowManager.getInstance().getFlexibleWindowState(activity) ==
                    FlexibleWindowManager.FLEXIBLE_WINDOW_SPLIT_SCREEN_MODE
            } else {
                activity?.isInMultiWindowMode == true && !isInFreeFormMode(activity)
            }
        }.onFailure {
            Log.e(TAG, "isInMultiWindowMode error:${it.message}")
        }.getOrDefault(false)
        Log.i(TAG, "isInMultiWindowMode: $isInMultiWindowMode, $activity")
        return isInMultiWindowMode
    }

    /**
     * 是否是浮窗态
     *
     * 适用OS13及以上
     */
    @JvmStatic
    fun isInFreeFormMode(activity: Activity?): Boolean {
        var isInFreeformMode = false
        runCatching {
            isInFreeformMode = if (OplusBuildProxy.isAboveOS150()) {
                FlexibleWindowManager.getInstance().getFlexibleWindowState(activity) ==
                    FlexibleWindowManager.FLEXIBLE_WINDOW_FREEFORM_MODE
            } else {
                isCurrentActivityInZoomState(activity)
            }
        }.onFailure {
            Log.e(TAG, "isInFreeFormMode error:${it.message}")
        }
        Log.i(TAG, "isInFreeFormMode: $isInFreeformMode, $activity")
        return isInFreeformMode
    }

    /**
     * 仅在os14及以前使用，os15后使用FlexibleWindowManager接口判断
     */
    @Suppress("UnnecessarySafeCall")
    private fun isCurrentActivityInZoomState(activity: Activity?): Boolean {
        return kotlin.runCatching {
            if (activity == null) {
                return false
            }
            val appWidth: Int?
            val appHeight: Int?
            val physicalHeight: Int?
            val physicalWidth: Int?
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                val windowManager: WindowManager? = activity.getSystemService(WindowManager::class.java)
                appWidth = windowManager?.currentWindowMetrics?.bounds?.width()
                appHeight = windowManager?.currentWindowMetrics?.bounds?.height()
                physicalHeight = activity.display?.mode?.physicalHeight
                physicalWidth = activity.display?.mode?.physicalWidth
            } else {
                appWidth = activity.window.decorView.width
                appHeight = activity.window.decorView.height

                var mRealSizeWidth = 0
                var mRealSizeHeight = 0
                val display =
                    (activity.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager).defaultDisplay
                val outPoint = Point()
                display.getRealSize(outPoint)
                if (outPoint.y > outPoint.x) {
                    mRealSizeHeight = outPoint.y
                    mRealSizeWidth = outPoint.x
                } else {
                    mRealSizeHeight = outPoint.x
                    mRealSizeWidth = outPoint.y
                }
                physicalHeight = mRealSizeHeight
                physicalWidth = mRealSizeWidth
            }
            val fullScreen = (appWidth != null && appHeight != null) && appWidth * appHeight == (physicalHeight?.let {
                physicalWidth?.times(it)
            })
            Log.d(TAG, "fullScreen: $fullScreen,  app: $appWidth * $appHeight and physical: $physicalWidth * $physicalHeight")
            if (!isMiddleAndLargeScreen(activity)) {
                if (!fullScreen) {
                    return OplusZoomWindowManagerProxy.isPackageZoomWindowState(activity)
                }
            } else {
                if (!fullScreen) {
                    return isInZoomStateWindowInfo(activity)
                }
            }
            return false
        }.onFailure {
            Log.e(TAG, "isCurrentActivityInZoomState error: ${it.message}")
        }.getOrDefault(false)
    }

    private fun isInZoomStateWindowInfo(activity: Activity?): Boolean {
        return kotlin.runCatching {
            if (activity == null) {
                Log.d(TAG, "activity == null")
                return false
            }
            val wm = OplusWindowManager()
            val allVisibleWindowInfo: List<OplusWindowInfo> = wm.allVisibleWindowInfo
            for (windowInfo in allVisibleWindowInfo) {
                if (windowInfo.componentName?.className == activity.componentName?.className
                    && windowInfo.windowingMode == WINDOWING_MODE_SMALL_WINDOW
                ) {
                    Log.d(TAG, "isInZoomStateWindowInfo $activity")
                    return true
                }
            }
            return false
        }.onFailure {
            Log.e(TAG, "isInZoomStateWindowInfo error: ${it.message}")
        }.getOrDefault(false)
    }

    private const val WINDOWING_MODE_SMALL_WINDOW = 100
    private const val MIDDLE_AND_LARGE_SCREEN_SW_VALUE = 480
    private fun isMiddleAndLargeScreen(context: Context) =
        context.resources.configuration.smallestScreenWidthDp >= MIDDLE_AND_LARGE_SCREEN_SW_VALUE
}