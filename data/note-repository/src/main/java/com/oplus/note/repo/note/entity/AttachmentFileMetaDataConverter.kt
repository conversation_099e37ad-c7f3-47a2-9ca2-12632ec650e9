/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - AttachmentFileMetaDataConverter
 ** Description:
 **         v1.0:   Create AttachmentFileMetaDataConverter file
 **
 ** Version: 1.0
 ** Date: 2024/03/04
 ** Author: Jiep<PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>       2024/3/4   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.repo.note.entity

import androidx.room.TypeConverter

class AttachmentFileMetaDataConverter {

    @TypeConverter
    fun stringToAttachmentFileMetaData(extra: String?): AttachmentFileMetaData =
        AttachmentFileMetaData.create(extra)

    @TypeConverter
    fun attachmentFileMetaDataToString(extra: AttachmentFileMetaData?): String =
        extra?.toString() ?: ""
}