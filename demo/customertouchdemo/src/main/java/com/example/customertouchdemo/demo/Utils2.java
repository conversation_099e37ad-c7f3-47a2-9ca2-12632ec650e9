/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.example.customertouchdemo.demo;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.nearme.note.activity.richedit.UiMode;
import com.nearme.note.model.RichNote;
import com.nearme.note.model.RichNoteRepository;
import com.oplus.note.logger.AppLogger;
import com.nearme.note.MyApplication;
import com.oplus.note.notebook.NoteBookData;
import com.nearme.note.appwidget.notewidget.NoteWidgetInfoMap;
import com.nearme.note.data.FolderInfo;
import com.nearme.note.data.NoteInfo;
import com.nearme.note.db.FolderUtil;
import com.nearme.note.db.NoteInfoDBUtil;
import com.nearme.note.paint.view.Pen;
import com.nearme.note.skin.SkinData;
import com.nearme.note.skin.api.SkinManager;
import com.oplus.richtext.core.parser.HtmlParser;
import com.oplus.statistics.OplusTrack;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class Utils {

    public static final int TYPE_TODO_WIDGET_SHOW_COMPLETE_TODO = 1;
    public static final int TYPE_TODO_WIDGET_HIDE_COMPLETE_TODO = 2;

    public static final int TYPE_NOTE_TOP = 0;
    public static final int TYPE_NOTE_UN_TOP = 1;
    public static final int TYPE_NOTE_ALL_TOP = 2;
    public static final int TYPE_NOTE_ALL_UN_TOP = 3;
    public static final int TYPE_NOTE_TODO = 0;
    public static final int TYPE_NOTE_CANCEL_TODO = 1;

    public static final int TYPE_OPERATION_STATE_SUCCESS = 1;
    public static final int TYPE_OPERATION_STATE_FAIL = 0;

    /**
     * 加密
     *
     * @param type 0:编辑页点击设为加密 1:首页单选加密 2:首页多选加密 3:侧边栏点击加密 4全选加密
     */
    public static final int EVENT_ENCRYPTED_NOTE_ENCRYPT_1 = 1;
    public static final int EVENT_ENCRYPTED_NOTE_ENCRYPT_2 = 2;
    public static final int EVENT_ENCRYPTED_NOTE_ENCRYPT_3 = 3;
    public static final int EVENT_ENCRYPTED_NOTE_ENCRYPT_4 = 4;

    public static final int TYPE_NEW_TODO_BY_WIDGHT = 0;
    public static final int TYPE_EDIT_TODO_BY_WIDGHT = 1;

    public static final int TYPE_OPEN_DRAWER_BUTTON = 0;
    public static final int TYPE_OPEN_DRAWER_SLIDE = 1;
    public static final int TYPE_CLOSE_DRAWER_CLICK_BACK = 1;
    public static final int TYPE_CLOSE_DRAWER_SLIDE = 2;
    public static final int TYPE_DELETE_FOLDER_FOLDER_AND_NOTE = 2;
    public static final int TYPE_MOVE_FOLDER_LIST = 0;
    public static final int TYPE_MOVE_FOLDER_NOTE_DETAIL = 1;
    public static final int TYPE_MOVE_ALL_FOLDER_NOTE = 2;
    public static final int TYPE_NOTE_TODO_STATUS_CHECK = 0;
    public static final int TYPE_NOTE_TODO_STATUS_UN_CHECK = 1;
    public static final int TYPE_NOTE_BACK_DEFULT = 0;
    public static final int TYPE_NOTE_BACK_EDIT = 1;
    public static final int TYPE_SHARE_NOTE_BY_PIC_DEFULT = 0;
    public static final int TYPE_SHARE_NOTE_BY_PIC_SHARE = 1;
    public static final int TYPE_SHARE_NOTE_BY_PIC_LOCAL = 2;
    public static final int TYPE_INSERT_PICTURE_TACK_PICTURE = 0;
    public static final int TYPE_INSERT_PICTURE_PHOTOALBUM = 1;
    public static final int TYPE_SWITCH_NOTE_LIST = 0;
    public static final int TYPE_SWITCH_NOTE_GRID = 1;
    public static final int TYPE_RECOVER_LIST = 0;
    public static final int TYPE_RECOVER_NAVIGATION = 1;
    public static final int TYPE_RECOVER_DIALOG = 2;
    public static final int TYPE_DELETE_COMPLETELY_LIST = 0;
    public static final int TYPE_DELETE_COMPLETELY_NAVIGATION = 1;

    public static final int TYPE_TODO_CREATE_ALARM = 0;
    public static final int TYPE_TODO_UPDATE_ALARM = 1;
    public static final int TYPE_TODO_DELETE_ALARM = 2;
    public static final int TYPE_LONG_PRESS_MOVE_ENCRYPTED = 0;
    public static final int TYPE_LONG_PRESS_MULTIPLE_SELECTION_MOVE_ENCRYPTED = 1;
    public static final int TYPE_MEMO_DETAILS_MOVE_ENCRYPTED = 2;

    public static final int TYPE_ADD_ALARM = 0;
    public static final int TYPE_MODIFY_ALARM = 1;
    public static final int TYPE_DELETE_ALARM = 2;
    public static final int TYPE_VIEW_ALARM_NOTIFICATION = 0;
    public static final int TYPE_OPEN_NOTIFICATION_SETTING = 0;
    public static final int TYPE_LIST_MODEL_TYPE = 0;
    public static final int TYPE_GRID_MODEL_TYPE = 1;

    public static final int TYPE_TODO_CREATE_ALARM_WIDGET = 0;
    public static final int TYPE_TODO_UPDATE_ALARM_WIDGET = 1;
    public static final int TYPE_TODO_DELETE_ALARM_WIDGET = 2;

    public static final int TYPE_ADDED_STATUS = 0;
    public static final int TYPE_UNADDED_STATUS = 1;

    public static final int TYPE_ADD_SKIN_IMAGE = 0;
    public static final int TYPE_UPDATE_SKIN_IMAGE = 1;
    public static final int TYPE_ADD_SKIN_COLOR = 2;
    public static final int TYPE_UPDATE_SKIN_COLOR = 3;

    public static final int DRAG_RESULT_SUCCESS = 0;
    public static final int DRAG_RESULT_NOT_RESUME = 1;
    public static final int DRAG_RESULT_OVER_99 = 2;
    public static final int DRAG_RESULT_STORAGE_NOT_ENOUGH = 3;
    public static final int DRAG_RESULT_SYSTEM_FAILURE = 4;
    public static final int DRAG_RESULT_OTHER_FAILURE = 5;

    private static final int TYPE_SELECT_ALL_DELETE = 0;
    private static final int NUM_LENGTH_0 = 1;
    private static final int NUM_LENGTH_1 = 10;
    private static final int NUM_LENGTH_2 = 20;
    private static final int NUM_LENGTH_3 = 50;
    private static final int NUM_LENGTH_4 = 100;
    private static final int NUM_LENGTH_5 = 200;
    private static final int NUM_LENGTH_6 = 500;
    private static final int NUM_LENGTH_7 = 1000;
    private static final int NUM_TYPE_0 = 0;
    private static final int NUM_TYPE_1 = 1;
    private static final int NUM_TYPE_2 = 2;
    private static final int NUM_TYPE_3 = 3;
    private static final int NUM_TYPE_4 = 4;
    private static final int NUM_TYPE_5 = 5;
    private static final int NUM_TYPE_6 = 6;
    private static final int NUM_TYPE_7 = 7;

    private static final int NUM_TODO_LENGTH_0 = 0;
    private static final int NUM_TODO_LENGTH_1 = 5;
    private static final int NUM_TODO_LENGTH_2 = 10;
    private static final int NUM_TODO_LENGTH_3 = 15;
    private static final int NUM_TODO_LENGTH_4 = 19;
    private static final int NUM_TODO_LENGTH_5 = 38;
    private static final int NUM_TODO_LENGTH_6 = 58;
    private static final int NUM_TODO_TYPE_0 = 0;
    private static final int NUM_TODO_TYPE_1 = 1;
    private static final int NUM_TODO_TYPE_2 = 2;
    private static final int NUM_TODO_TYPE_3 = 3;
    private static final int NUM_TODO_TYPE_4 = 4;
    private static final int NUM_TODO_TYPE_5 = 5;
    private static final int NUM_TODO_TYPE_6 = 6;

    private static final int TYPE_TODO_SET_COMPLETE_STATUS = 0;
    private static final int TYPE_TODO_SET_UNCOMPLETE_STATUS = 1;

    private static final int TYPE_TODO_DEFAULT_PAGE_NOTE = 0;
    private static final int TYPE_TODO_DEFAULT_PAGE_TODO = 1;

    private static final int TYPE_DETAIL_NOTE = 0;
    private static final int TYPE_DOODLE_NOTE = 1;

    private final static String TAG_FOLDER = "2001001";
    private final static String EVENT_OPEN_DRAWER = "event_open_drawer";
    private final static String EVENT_CLOSE_DRAWER = "event_close_drawer";
    private final static String EVENT_CREATE_FOLDER = "event_create_folder";
    private final static String EVENT_RENAME_FOLDER = "event_rename_folder";
    private final static String EVENT_DELETE_FOLDER = "event_delete_folder";
    private final static String EVENT_MOVE_NOTE = "event_move_note";

    private final static String TAG_DETAILS = "2001002";
    private final static String EVENT_INSERT_PICTURE = "event_insert_picture";
    private final static String EVENT_CLICK_SPLIT_SCREEN = "event_click_split_screen";
    private final static String EVENT_NOTE_TODO = "event_note_todo";
    private final static String EVENT_NOTE_TODO_STATUS = "event_note_todo_status";
    private final static String EVENT_SPEECH_NOTE_DICTATION = "event_speech_note_dictation";
    private final static String EVENT_IMG_TO_TXT = "event_img_to_txt";
    private final static String EVENT_NOTE_COMPLETE = "event_note_complete";
    private final static String EVENT_NOTE_BACK = "event_note_back";
    private final static String EVENT_SHARE_NOTE_BY_PIC = "share_by_pic";
    private final static String EVENT_SHARE_NOTE_BY_TEXT = "share_by_text";
    private final static String EVENT_SHARE_NOTE_BY_DOC = "share_by_doc";
    private final static String EVENT_OPEN_MEMO = "event_open_memo";
    private final static String EVENT_CLICK_SKIN = "event_click_skin";
    private final static String EVENT_NOTE_CLOSE = "event_note_close";
    private final static String EVENT_ATTACHMENTS_SELECTION_MENU = "event_attachments_selection_menu";

    private final static String TAG_ALL_NOTE = "2001003";
    private final static String EVENT_SEARCH_NOTE = "event_search_note";
    private final static String EVENT_NOTE_TOP = "event_note_top";
    private final static String EVENT_SWITCH_NOTE = "event_switch_note";
    private final static String EVENT_MODEL_TYPE_MEMO = "model_type_memo";
    private final static String EVENT_NUM_MEMO_TOPPED = "num_memo_topped";
    private final static String EVENT_SORT_RULE = "event_sort_rule";
    private final static String EVENT_USER_AGREEMENT = "event_user_agreement";
    private final static String EVENT_NOTE_EDIT_CANCEL = "event_note_edit_cancel";
    private final static String EVENT_ALL_DELETE_NOTE = "event_all_delete_note";
    private final static String EVENT_DELETE_NOTE = "event_delete_note";

    private final static String TAG_SYNC = "2001004";
    private final static String EVENT_SYNC_NOTE_SUBMIT = "sync_note_landed";
    private final static String EVENT_SETTING_AUTO_SYNC = "event_setting_auto_sync";

    private final static String TAG_LAUNCH = "2001005";
    private final static String EVENT_SHORTCUT_CREATE_NOTE_VOICE = "event_shortcut_create_note_voice";
    private final static String EVENT_SHORTCUT_CREATE_NOTE = "event_shortcut_create_note";

    private final static String TAG_STATISTICS = "2001006";
    private final static String EVENT_NOTE_NUM = "event_note_num";
    private final static String EVENT_FOLDER_NUM = "event_folder_num";
    private final static String EVENT_NOTE_TEXT_COUNT = "event_note_text_count";

    private final static String TAG_ADD_NOTE = "2001007";
    private final static String EVENT_VIEW_NOTE_BY_PHONE = "event_view_note_by_phone_notification";
    private final static String EVENT_ADD_NEW_NOTE = "event_add_new_note";
    private final static String EVENT_INSERT_NOTE = "event_insert_note";
    private final static String KEY_INSERT_TODO_PACKAGE_NAME = "key_package_name";

    private final static String TAG_DELETE_NOTE = "2001008";
    private final static String EVENT_RECOVER_NOTE = "event_recover_note";
    private final static String EVENT_DELETE_COMPLETELY_NOTE = "event_delete_completely_note";
    private final static String EVENT_SELECT_ALL_DELETE_NOTE = "event_select_all_delete_note";

    private final static String TAG_ALARM = "2001009";
    private final static String EVENT_SET_ALARM = "event_set_alarm";
    private final static String EVENT_NOTIFICATION_VIEW = "event_notification_view";
    private final static String EVENT_OPEN_NOTIFICATION_SETTING = "event_open_notification_setting";

    private final static String TAG_TODO = "2001010";
    private final static String SUM_TODO_DISTRIBUTION = "sum_todo_distribution";
    private final static String EVENT_CREATE_TODO = "event_create_todo";
    private final static String EVENT_EDIT_TODO = "event_edit_todo";
    private final static String EVENT_TODO_EDIT_ALARM = "event_todo_edit_alarm";
    private final static String EVENT_TODO_SHOW_NOTIFICATION = "event_todo_show_notification";
    private final static String EVENT_TODO_OPEN_NOTIFICATION = "event_todo_open_notification";
    private final static String EVENT_DELETE_TODO = "event_delete_todo";
    private final static String EVENT_TODO_SET_FINISH_STATUS = "event_todo_set_finish_status";
    private final static String EVENT_TODO_DEFAULT_ENTER_PAGE = "sum_default_tab";
    private final static String EVENT_TODO_TEXT_COUNT = "event_todo_text_count";
    private final static String EVENT_TODO_CANCEL = "event_todo_cancel";
    private final static String EVENT_TODO_EDIT_CANCEL = "event_todo_edit_cancel";
    private final static String EVENT_WIDGHT_OPERATION = "event_widght_operation";

    private final static String TAG_ENCYPTED = "2001011";
    private final static String SUM_ENCRYPTED_MEMO = "sum_encrypted_memo";
    private final static String EVENT_MEMO_ENCRYPT = "event_memo_encrypt";

    private final static String TAG_WIDGET = "2001013";
    private final static String EVENT_CREATE_TODO_WIDGET = "event_create_todo_widget";
    private final static String EVENT_EDIT_TODO_WIDGET = "event_edit_todo_widget";
    private final static String EVENT_ENTER_APP = "event_enter_app";
    private final static String EVENT_COMPLETE_TODO_WIDGET = "event_complete_todo_widget";
    private final static String EVENT_RESTORE_TODO_WIDGET = "event_restore_todo_widget";
    private final static String EVENT_UPDATE_ALARM_WIDGET = "event_update_alarm_widget";
    private final static String EVENT_DELETE_TODO_WIDGET = "event_delete_todo_widget";
    private final static String EVENT_ADD_WIDGET_APP = "event_add_widget_app";
    private final static String EVENT_ADD_TODO_WIDGET = "event_add_todo_widget";
    private final static String EVENT_UPDATE_TODO_HEIGHT = "event_update_todo_height";
    private final static String EVENT_TODO_WIDGET_STATUS = "event_todo_widget_status";
    private final static String EVENT_TODO_WIDGET_TO_SETTING = "event_todo_widget_to_setting";
    private final static String EVENT_TODO_WIDGET_CHANGE_OPAQUE = "event_todo_widget_change_opaque";
    private final static String EVENT_TODO_WIDGET_CURRENT_OPAQUE = "event_todo_widget_current_opaque";
    private final static String EVENT_TODO_WIDGET_HIDE_OR_SHOW_COMPLETE = "event_todo_widget_hide_or_show_complete";

    private final static String TAG_NOTE_WIDGET = "2001014";
    private final static String EVENT_ADD_NOTE_WIDGET = "event_add_note_widget";
    private final static String EVENT_DELETE_NOTE_WIDGET = "event_delete_note_widget";
    private final static String EVENT_NOTE_WIDGET_TO_ACTIVITY = "event_note_widget_to_activity";
    private final static String EVENT_NOTE_WIDGET_COUNT = "event_note_widget_count";

    private final static String TAG_SETTING = "2001015";
    private final static String EVENT_SETTING_COUNT = "event_open_setting_count";
    private final static String EVENT_FONT_COUNT = "event_open_font_count";

    private final static String TAG_UNDO_REDO = "2001016";
    private final static String EVENT_UNDO = "event_undo";
    private final static String KEY_UNDO_CLICK_TYPE = "undo_click_type";
    private final static int TYPE_UNDO_CAP_CLICK = 1;
    private final static int TYPE_REDO_CAP_CLICK = 2;
    private final static int TYPE_UNDO_CLICK = 3;
    private final static int TYPE_REDO_CLICK = 4;
    private final static int TYPE_UNDO_IMAGE_CLICK = 5;
    private final static int TYPE_UNDO_TEXT_CLICK = 6;
    private final static int TYPE_UNDO_TODO_CLICK = 7;

    private final static String TAG_NOTEBOOK_COVER = "2001017";
    private final static String EVENT_CANCEL_CREATE_NOTEBOOK = "event_cancel_create_notebook";
    private final static String EVENT_SWITCH_NOTEBOOK_COVER = "event_switch_notebook_cover";
    private final static String EVENT_SET_COVER_FROM_CREATE_NOTEBOOK = "event_set_cover_from_create_notebook";
    private final static String KEY_IS_SET_COVER = "is_set_cover";
    private final static String EVENT_FOLDER_HEARDER_RECENT_DELETE_NOTE = "event_folder_hearder_recent_delete_note";
    private final static String EVENT_NOTE_EDIT_MODEL = "event_note_edit_model";
    private final static String EVENT_SELECT_ALL_NOTE = "event_select_all_note";

    private final static String KEY_OPEN_DRAWER_TYPE = "open_drawer_type";
    private final static String KEY_CLOSE_DRAWER_TYPE = "close_drawer_type";
    private final static String KEY_CREATE_FOLDER_TYPE = "create_folder_type";
    private final static String KEY_DELETE_FOLDER_TYPE = "delete_folder_type";
    private final static String KEY_MOVE_NOTE_TYPE = "move_note_type";
    private final static String KEY_ADD_TODO_STATUS = "add_todo_status";
    private final static String KEY_TODO_STATUS = "todo_status";
    private final static String KEY_EDIT_STATUS = "edit_status";
    private final static String KEY_SHARE_BY_PIC_TYPE = "share_by_pic_type";
    private final static String KEY_TOP_STATUS = "top_status";
    private final static String KEY_COUNT_TYPE = "count_type";
    private final static String KEY_WORD_COUNT_NUM = "key_word_count_num";
    private final static String KEY_COUNT = "count";
    private final static String KEY_ALL_COUNT = "all_count";
    private final static String KEY_REMIND_COUNT = "remind_count";
    private final static String KEY_NO_SKIN_COUNT = "no_skin_count";
    private final static String KEY_COLOR_SKIN_1_COUNT = "color_skin_1_count";
    private final static String KEY_COLOR_SKIN_2_COUNT = "color_skin_2_count";
    private final static String KEY_COLOR_SKIN_3_COUNT = "color_skin_3_count";
    private final static String KEY_COLOR_SKIN_4_COUNT = "color_skin_4_count";
    private final static String KEY_COLOR_SKIN_5_COUNT = "color_skin_5_count";
    private final static String KEY_IMG_SKIN_1_COUNT = "img_skin_1_count";
    private final static String KEY_IMG_SKIN_2_COUNT = "img_skin_2_count";
    private final static String KEY_IMG_SKIN_3_COUNT = "img_skin_3_count";
    private final static String KEY_IMG_SKIN_4_COUNT = "img_skin_4_count";
    private final static String KEY_INSERT_PICTURE_TYPE = "insert_picture_type";
    private final static String KEY_SWITCH_TYPE = "switch_type";
    private final static String KEY_RECOVER_TYPE = "recover_type";
    private final static String KEY_DELETE_COMPLETELY_TYPE = "delete_completely_type";
    private final static String KEY_SELECT_ALL_DELETE_TYPE = "select_all_delete_type";
    private final static String KEY_EVENT_SET_ALARM_TYPE = "event_set_alarm_type";
    private final static String KEY_EVENT_NOTIFICATION_VIEW_TYPE = "event_notification_view_type";
    private final static String KEY_EVENT_OPEN_NOTIFICATION_SETTING_TYPE = "event_open_notification_setting_type";
    private final static String KEY_TODO_EVENT_DELETE_COMPLETE_TODO = "key_todo_event_delete_complete_todo";
    private final static String KEY_TODO_EVENT_DELETE_UNCOMPLETE_TODO = "key_todo_event_delete_uncomplete_todo";
    private final static String KEY_TODO_EVENT_DELETE_ALL_TODO = "key_todo_event_delete_all_todo";
    private final static String KEY_TODO_EVENT_DELETE_SOME_TODO = "key_todo_event_delete_some_todo";
    private final static String KEY_TODO_EVENT_SET_COMPLETE_STATUS = "key_todo_event_set_complete_status";
    private final static String KEY_TODO_DEFAULT_ENTER_PAGE = "key_todo_default_enter_page";
    private final static String KEY_EVENT_CREATE_TODO = "key_event_create_todo";
    private final static String KEY_EVENT_UPDATE_TODO = "key_event_update_todo";
    private final static String KEY_EVENT_DELETE_ALARM = "key_event_delete_alarm";
    private final static String KEY_ENCRYPTED_COUNT = "encrypted_count";
    private final static String KEY_MOVE_ENCRYPTED = "move_encrypted_type";
    private final static String KEY_MODEL_TYPE = "model_type";
    private final static String KEY_SORT_RULE_TYPE = "sort_rule_type";
    private final static String KEY_TOPPED_COUNT = "topped_count";
    private final static String KEY_TODO_COUNT = "count_todo";
    private final static String KEY_WIDGET_ALARM = "widget_alarm";
    private final static String KEY_WIDGET_HEIGHT = "widget_height";
    private final static String KEY_WIDGET_STATUS = "widget_status";
    private final static String KEY_SKIN_CHANGE = "skin_change";
    private final static String KEY_TODO_WIDGET_OPAQUE = "todo_widget_opaque";
    private final static String KEY_TODO_WIDGET_HIDE_OR_SHOW_TODO = "todo_widget_hide_or_show_todo";
    private final static String KEY_IS_CHANGE = "is_change";
    private final static String KEY_OPERATION_STATE = "key_operation_state";
    private final static String KEY_OPERATION_TYPE = "key_operation_type";
    private final static String KEY_ATTACHMENT_TYPE = "key_attachment_type";

    private final static String TAG_ROOM_DB_UPGRADE = "2001012";
    private final static String EVENT_DB_UPGRADE_FAILED = "db_upgrade_failed";
    private final static String KEY_EXCEPTION = "key_exception";
    private final static String KEY_TYPE = "key_type";
    private final static String TYPE_OLD_DB_UPGRADE_FAILED = "0";
    private final static String TYPE_ROOM_UPGRADE_FAILED = "1";
    private final static String TYPE_MIGRATE_TODO_FAILED = "2";

    private final static String TAG_MULTI_WINDOW = "2001018";
    private final static String EVENT_MULTI_WINDOW = "event_multi_window";

    private final static String TAG_ZOOM_WINDOW = "2001019";
    private final static String EVENT_ZOOM_WINDOW = "event_zoom_window";
    private final static String EVENT_SPLIT_SCREEN_NOTE = "event_split_screen_note";

    private final static String TAG_DRAG = "2001020";
    private final static String EVENT_DRAG_IN_WINDOW_MODE = "event_drag_in_window_mode";
    private final static String EVENT_DRAG_IN_TEXT = "event_drag_in_text";
    private final static String EVENT_DRAG_IN_IMAGE = "event_drag_in_image";
    private final static String EVENT_DRAG_OUT = "event_drag_out";
    private final static String EVENT_DRAG_IN_ERROR = "event_drag_in_error";
    private final static String KEY_MIME_TYPE = "key_mime_type";

    private final static String TAG_TITLE = "2001021";
    private final static String EVENT_TITLE_OPERATION = "event_title_operation";
    private final static String EVENT_TITLE_NOTE_HAS_TITLE_NUM = "event_title_note_has_title_num";
    private final static String KEY_COUNT_EMOTICON = "count_emoticon";
    private final static String EVENT_TITLE_NOTE_NOT_TITLE_NUM = "event_title_note_not_title_num";
    private final static String EVENT_TITLE_NOTE_ONLY_TITLE_NUM = "event_title_note_only_title_num";
    private final static String EVENT_TITLE_WORD_NUM = "event_title_word_num";
    private static final int NUM_TITLE_LENGTH_0 = 10;
    private static final int NUM_TITLE_LENGTH_1 = 20;
    private static final int NUM_TITLE_LENGTH_2 = 30;
    private static final int NUM_TITLE_LENGTH_3 = 40;

    private final static String TAG_NOTEBOOK = "2001022";
    private final static String EVENT_NOTEBOOK_NUM = "event_notebook_num";
    private final static String EVENT_NOTEBOOK_EDIT = "event_notebook_edit";
    private final static String EVENT_NOTEBOOK_COVER = "event_notebook_cover";
    private static final int NUM_NOTEBOOK_COUNT_0 = 0;
    private static final int NUM_NOTEBOOK_COUNT_1 = 1;
    private static final int NUM_NOTEBOOK_COUNT_2 = 2;
    private static final int NUM_NOTEBOOK_COUNT_3 = 3;
    private static final int NUM_NOTEBOOK_COUNT_4 = 4;
    private static final int NUM_NOTEBOOK_COUNT_5 = 5;
    private static final int NUM_NOTEBOOK_COUNT_6 = 10;
    private static final String KEY_NOT_COVER = "key_not_cover";
    private static final String TAG = "StatisticsUtils";


    private final static String TAG_ONLINE_SKIN = "2001023";
    private final static String EVENT_ONLINE_SKIN_NUM = "event_online_skin_num";
    private final static String KEY_SKIN_ID = "key_skin_id";

    private final static String TAG_ENCRYPTED_NOTE = "2001024";
    private final static String EVENT_ENCRYPTED_NOTE_ENCRYPT = "event_encrypted_note_encrypt";
    private final static String EVENT_ENCRYPTED_NOTE_DECRYPT = "event_encrypted_note_decrypt";

    private final static String TAG_INSERT_TODO = "2001025";
    private final static String EVENT_INSERT_TODO = "event_insert_todo";

    private final static String TAG_INSERT_NOTE = "2001026";
    private final static String EVENT_INSERT_TEXT_NOTE = "event_insert_text_note";
    private final static String KEY_PACKAGE_NAME = "key_package_name";
    private final static String EVENT_PHONE_LINK_CLICK = "event_phone_link_click";
    private final static String KEY_PHONE_LINK_CLICK_TYPE = "key_phone_link_click_type";
    private final static String KEY_PHONE_LINK_CLICK_FROM = "key_phone_link_click_from";
    private final static String EVENT_PAINT_OPEN = "event_paint_open";
    private final static String KEY_PAINT_IS_CREATE = "key_paint_is_create";
    private final static String EVENT_PAINT_COMPLETE = "event_paint_complete";
    private final static String EVENT_PAINT_SELECT_PEN = "event_paint_select_pen";
    private final static String KEY_PAINT_SELECT_PEN_TYPE = "key_paint_select_pen_type";
    private final static String KEY_PAINT_SELECT_COLOR = "key_paint_select_color";
    private final static String KEY_PAINT_SELECT_STROKE = "key_paint_select_stroke";
    private final static String KEY_PAINT_SELECT_ALPHA = "key_paint_select_alpha";
    private final static String EVENT_PAINT_UNDO = "event_paint_undo";
    private final static String EVENT_PAINT_REDO = "event_paint_redo";
    private final static String EVENT_PAINT_VISIBLE_DURATION = "event_paint_visible_duration";
    private final static String KEY_PAINT_VISIBLE_DURATION = "key_paint_visible_duration";
    private final static String KEY_PAINT_DOODLE_COPY = "key_paint_doodle_copy";
    private final static String KEY_PAINT_DOODLE_CUT = "key_paint_doodle_cute";
    private final static String KEY_PAINT_DOODLE_DELETE = "key_paint_doodle_delete";
    private final static String KEY_PAINT_QUICKPAINT_SHARE = "key_paint_quickpaint_share";
    private final static String KEY_PAINT_QUICKPAINT_CREATE = "key_paint_quickpaint_create";
    private final static String KEY_PAINT_QUICKPAINT_ACTIVITY = "key_paint_quickpaint_activity";
    private final static String EVENT_REPEAT_REMINDER_ENTRANCE = "event_repeat_reminder_entrance";
    private final static String EVENT_REPEAT_ONLY_ONCE = "event_repeat_only_once";
    private final static String EVENT_REPEAT_EVERY_DAY = "event_repeat_every_day";
    private final static String EVENT_REPEAT_EVERY_WEEK = "event_repeat_every_week";
    private final static String EVENT_REPEAT_EVERY_TWO_WEEKS = "event_repeat_every_two_weeks";
    private final static String EVENT_REPEAT_EVERY_MONTH = "event_repeat_every_month";
    private final static String EVENT_REPEAT_EVERY_YEAR = "event_repeat_every_year";
    private final static String EVENT_REPEAT_SET_SUCCESSFULLY = "event_repeat_set_sucessfully";
    private final static String EVENT_NOTE_REMIND = "event_note_remind";
    private static final String EVENT_RED_NOTE_MIGRATE_RESULT = "upgrade_os12_data_compatible"; //event id
    private final static String KEY_MIGRATE_RESULT = "result"; //result (0 or 1)
    private final static String KEY_MIGRATE_REASON = "reason"; //reason (fail reason)

    private final static String EVENT_DRAG_LIST = "event_drag_list";
    private final static String KEY_DRAG_LIST_TYPE = "drag_type";
    private final static int DRAG_LIST_TYPE_NOTE = 0;
    private final static int DRAG_LIST_TYPE_TODO = 1;
    private final static String KEY_DRAG_LIST_COUNT = "drag_count";
    private final static String KEY_DRAG_LIST_RESULT = "drag_result";

    /**
     * 点击内容搜索菜单
     */
    private static final String EVENT_CLICK_CONTENT_SEARCH_MENU = "note_detail_search_menu_click";
    private static final String SPEECH_TO_TEXT_WORDS = "speech_to_text_words";
    private static final String SPEECH_NUMBER_OF_NOTES = "speech_number_of_notes";

    /**
     * 代办-新建-取消
     */
    public static void setEventTodoCancel() {
        OplusTrack.onCommon(MyApplication.getAppContext(), TAG_TODO, EVENT_TODO_CANCEL, null);
    }

    /**
     * 待办-更多-编辑-取消
     */
    public static void setEventTodoEditCancel() {
        OplusTrack.onCommon(MyApplication.getAppContext(), TAG_TODO, EVENT_TODO_EDIT_CANCEL, null);
    }

    /**
     * 卡片-待办操作
     *
     * @param type 0 卡片新建 1 卡片item点击
     */
    public static void setEventWidghtOperation(int type) {
        Map<String, String> map = new HashMap<String, String>(1);
        map.put(KEY_TYPE, String.valueOf(type));
        OplusTrack.onCommon(MyApplication.getAppContext(), TAG_TODO, EVENT_WIDGHT_OPERATION, map);
    }

    public static void setEventEncryptedMemoCount(Context context) {
        int count = NoteInfoDBUtil.queryEncryptedNotesCount();
        Map<String, String> map = new HashMap<String, String>(1);
        map.put(KEY_ENCRYPTED_COUNT, String.valueOf(count));
        OplusTrack.onCommon(context, TAG_ENCYPTED, SUM_ENCRYPTED_MEMO, map);
    }

    public static void setMoveToEncrypted(Context context, int type) {
        Map<String, String> map = new HashMap<String, String>(1);
        map.put(KEY_MOVE_ENCRYPTED, String.valueOf(type));
        OplusTrack.onCommon(context, TAG_ENCYPTED, EVENT_MEMO_ENCRYPT, map);
    }

    public static void setEventOpenMemo(boolean isChangeNote) {
        Map<String, String> map = new HashMap<String, String>(1);
        map.put(KEY_IS_CHANGE, isChangeNote ? "0" : "1");
        OplusTrack.onCommon(MyApplication.getAppContext(), TAG_DETAILS, EVENT_OPEN_MEMO, map);
    }

    public static void setEventSettingOpenCount(Context context) {
        OplusTrack.onCommon(context, TAG_SETTING, EVENT_SETTING_COUNT, null);
    }

    public static void setEventFontOpenCount(Context context) {
        OplusTrack.onCommon(context, TAG_SETTING, EVENT_FONT_COUNT, null);
    }

    public static void setTypeMemoModel(Context context, int type) {
        Map<String, String> map = new HashMap<String, String>(1);
        map.put(KEY_MODEL_TYPE, String.valueOf(type));
        OplusTrack.onCommon(context, TAG_ALL_NOTE, EVENT_MODEL_TYPE_MEMO, map);
    }

    public static void setEventNumMemoTopped(Context context, int count) {
        Map<String, String> map = new HashMap<String, String>(1);
        map.put(KEY_TOPPED_COUNT, String.valueOf(count));
        OplusTrack.onCommon(context, TAG_ALL_NOTE, EVENT_NUM_MEMO_TOPPED, map);
    }

    public static void setEventCreateToDoWidget(Context context) {
        if (context == null) {
            return;
        }
        OplusTrack.onCommon(context.getApplicationContext(), TAG_WIDGET, EVENT_CREATE_TODO_WIDGET, null);
    }

    public static void setEventEditToDoWidget(Context context) {
        if (context == null) {
            return;
        }
        OplusTrack.onCommon(context.getApplicationContext(), TAG_WIDGET, EVENT_EDIT_TODO_WIDGET, null);
    }

    public static void setEventOpenAppWidget(Context context) {
        if (context == null) {
            return;
        }
        OplusTrack.onCommon(context.getApplicationContext(), TAG_WIDGET, EVENT_ENTER_APP, null);
    }

    public static void setEventCompleteToDoWidget(Context context) {
        if (context == null) {
            return;
        }
        OplusTrack.onCommon(context.getApplicationContext(), TAG_WIDGET, EVENT_COMPLETE_TODO_WIDGET, null);
    }

    public static void setEventRestoreToDoWidget(Context context) {
        if (context == null) {
            return;
        }
        OplusTrack.onCommon(context.getApplicationContext(), TAG_WIDGET, EVENT_RESTORE_TODO_WIDGET, null);
    }

    public static void setEventEditAlarmWidget(Context context, int type) {
        if (context == null) {
            return;
        }
        Map<String, String> map = new HashMap<>(1);
        map.put(KEY_WIDGET_ALARM, String.valueOf(type));
        OplusTrack.onCommon(context.getApplicationContext(), TAG_WIDGET, EVENT_UPDATE_ALARM_WIDGET, map);
    }

    public static void setEventDeleteTodoWidget(Context context) {
        if (context == null) {
            return;
        }
        OplusTrack.onCommon(context.getApplicationContext(), TAG_WIDGET, EVENT_DELETE_TODO_WIDGET, null);
    }

    public static void setEventAddTodoWidgetApp(Context context) {
        if (context == null) {
            return;
        }
        OplusTrack.onCommon(context.getApplicationContext(), TAG_WIDGET, EVENT_ADD_WIDGET_APP, null);
    }

    public static void setEventAddTodoWidget(Context context) {
        if (context == null) {
            return;
        }
        OplusTrack.onCommon(context.getApplicationContext(), TAG_WIDGET, EVENT_ADD_TODO_WIDGET, null);
    }

    public static void setEventUpdateTodoHeight(Context context, int height) {
        if (context == null) {
            return;
        }
        Map<String, String> map = new HashMap<>();
        map.put(KEY_WIDGET_HEIGHT, String.valueOf(height));
        OplusTrack.onCommon(context.getApplicationContext(), TAG_WIDGET, EVENT_UPDATE_TODO_HEIGHT, map);
    }

}