<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/black">


    <TextView
        android:id="@+id/todo_title"
        style="@style/todoTitleTextStyle"
        android:layout_marginStart="12dp"
        android:layout_marginTop="12dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:text="待办xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" />

    <com.example.rolltext.RollingTextView
        android:id="@+id/number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/todo_title"
        android:layout_toEndOf="@id/todo_title"
        android:textColor="@android:color/white"
        android:textSize="@dimen/todo_count_text_size"
        android:textStyle="bold"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="12dp"/>

    <Button
        android:id="@+id/increase"
        android:layout_width="100dp"
        android:layout_height="60dp"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="50dp"
        android:layout_marginStart="20dp"
        android:text="加1"/>

    <Button
        android:id="@+id/random"
        android:layout_width="100dp"
        android:layout_height="60dp"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="50dp"
        android:layout_centerHorizontal="true"
        android:text="随机0~1000"/>

    <Button
        android:id="@+id/decrease"
        android:layout_width="100dp"
        android:layout_height="60dp"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="50dp"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="20dp"
        android:text="减1"/>

    <TextView
        android:id="@+id/content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_above="@id/increase5"
        android:layout_marginBottom="30dp"
        android:textColor="@color/white"
        style="@style/todoTitleTextStyle"/>

    <Button
        android:id="@+id/increase10"
        android:layout_width="100dp"
        android:layout_height="60dp"
        android:layout_above="@+id/increase"
        android:layout_marginBottom="30dp"
        android:layout_marginStart="20dp"
        android:text="加10"/>


    <Button
        android:id="@+id/decrease10"
        android:layout_width="100dp"
        android:layout_height="60dp"
        android:layout_above="@id/increase"
        android:layout_marginBottom="30dp"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="20dp"
        android:text="减10"/>

    <Button
        android:id="@+id/random100"
        android:layout_width="100dp"
        android:layout_height="60dp"
        android:layout_marginBottom="30dp"
        android:layout_centerHorizontal="true"
        android:layout_above="@id/increase"
        android:text="随机0~100"/>

    <Button
        android:id="@+id/increase5"
        android:layout_width="100dp"
        android:layout_height="60dp"
        android:layout_above="@+id/increase10"
        android:layout_marginBottom="30dp"
        android:layout_marginStart="20dp"
        android:text="加5"/>


    <Button
        android:id="@+id/decrease5"
        android:layout_width="100dp"
        android:layout_height="60dp"
        android:layout_above="@id/increase10"
        android:layout_marginBottom="30dp"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="20dp"
        android:text="减5"/>

</RelativeLayout>